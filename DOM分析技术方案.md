# DOM分析技术方案

## 方案概述

本方案旨在解决通过自然语言生成Playwright测试脚本时，如何更准确地获取和分析页面DOM结构的问题。通过智能DOM分析，让大模型能够生成更精确的元素选择器和测试脚本。

## 核心问题

1. **DOM获取问题**：如何便捷地获取目标页面的DOM结构
2. **DOM筛选问题**：如何从大量DOM元素中筛选出对测试有用的元素
3. **上下文优化**：如何将DOM信息以最优的方式提供给大模型

## 技术方案

### 1. DOM获取策略

#### 方案A：后端Playwright爬取（推荐）

- **优点**：稳定可靠，支持复杂页面，可处理动态内容
- **缺点**：需要后端资源，有一定延迟
- **实现**：使用Playwright在后端访问目标页面，执行JavaScript获取DOM

#### 方案B：浏览器扩展

- **优点**：实时性好，用户体验佳
- **缺点**：需要安装扩展，有安全限制
- **实现**：开发Chrome扩展，直接在目标页面注入分析脚本

#### 方案C：代理服务

- **优点**：无需安装额外软件
- **缺点**：可能遇到跨域问题，对动态页面支持有限
- **实现**：通过代理服务器获取页面内容并分析

### 2. DOM筛选策略

#### 2.1 元素类型过滤

只保留可交互的元素类型：

- `input`：输入框
- `button`：按钮
- `a`：链接
- `select`：下拉选择器
- `textarea`：文本域
- `form`：表单

#### 2.2 可见性过滤

- 过滤掉隐藏元素（`display: none`, `visibility: hidden`）
- 过滤掉尺寸为0的元素
- 过滤掉透明度为0的元素

#### 2.3 智能选择器生成

按优先级生成最佳选择器：

1. `id` 属性（最高优先级）
2. `data-testid` 或 `data-test` 属性
3. `name` 属性
4. 稳定的 `class` 组合
5. 基于文本内容的选择器
6. `placeholder` 属性
7. 位置相关选择器（最后选择）

#### 2.4 场景相关过滤

根据测试场景描述，智能筛选相关元素：

- **登录场景**：优先保留用户名、密码输入框和登录按钮
- **搜索场景**：优先保留搜索框和搜索按钮
- **购买场景**：优先保留商品相关按钮和表单

### 3. DOM上下文优化

#### 3.1 结构化描述

将DOM元素转换为结构化的文本描述：

```
页面URL: https://example.com

可交互元素列表:
1. INPUT: 用户名输入框
   选择器: input[name="username"]
   属性: name="username" placeholder="请输入用户名"

2. BUTTON: 登录按钮
   选择器: button.login-btn
   属性: class="login-btn" type="submit"
```

#### 3.2 语义化标注

为每个元素添加语义化描述：

- 元素类型（INPUT、BUTTON、LINK等）
- 元素用途（基于文本内容和属性推断）
- 推荐的操作方式

#### 3.3 上下文压缩

- 限制元素数量（如最多20个）
- 优先级排序（重要元素优先）
- 去重相似元素

### 4. 实现架构

```
前端 DOM分析组件
    ↓
后端 DOM分析服务
    ↓
Playwright 浏览器实例
    ↓
目标页面 DOM获取
    ↓
智能筛选和处理
    ↓
结构化上下文
    ↓
大模型 脚本生成
```

## 实现细节

### 前端组件功能

1. **URL输入**：用户输入要分析的页面URL
2. **分析触发**：调用后端API进行DOM分析
3. **结果展示**：以可视化方式展示分析结果
4. **元素筛选**：用户可以手动选择需要的元素
5. **上下文生成**：生成优化的DOM上下文信息

### 后端服务功能

1. **页面访问**：使用Playwright访问目标页面
2. **DOM提取**：执行JavaScript获取页面DOM结构
3. **智能分析**：分析元素类型、可见性、语义
4. **选择器生成**：为每个元素生成最佳选择器
5. **结果优化**：按重要性排序和去重

### 大模型集成

1. **上下文注入**：将DOM信息作为上下文提供给大模型
2. **提示词优化**：结合DOM信息优化生成提示词
3. **脚本验证**：验证生成的选择器是否有效

## 使用流程

1. **输入测试需求**：用户描述测试场景
2. **输入目标URL**：指定要测试的页面
3. **DOM分析**：系统自动分析页面结构
4. **元素筛选**：用户选择相关的页面元素
5. **上下文生成**：生成优化的DOM上下文
6. **脚本生成**：大模型基于需求和DOM信息生成脚本
7. **脚本执行**：运行生成的Playwright测试脚本

## 优势特点

1. **精确性提升**：基于真实DOM结构生成选择器，避免无效选择器
2. **智能筛选**：自动过滤无关元素，减少噪音
3. **用户友好**：可视化界面，用户可以直观选择元素
4. **场景适配**：根据测试场景智能推荐相关元素
5. **可扩展性**：支持多种DOM获取方式和筛选策略

## 技术栈

- **前端**：Vue.js + Element UI
- **后端**：Node.js + Express
- **DOM分析**：Playwright
- **大模型**：通过API调用（如OpenAI、文心一言等）

## 部署要求

1. **Node.js环境**：版本16+
2. **Playwright依赖**：自动安装浏览器
3. **网络访问**：能够访问目标测试页面
4. **计算资源**：中等配置即可，主要消耗在浏览器渲染

## 安全考虑

1. **URL验证**：防止访问恶意网站
2. **资源限制**：限制分析时间和资源消耗
3. **权限控制**：限制可访问的域名范围
4. **数据清理**：及时清理临时数据和浏览器实例

## 扩展方向

1. **视觉分析**：结合页面截图进行视觉元素识别
2. **AI增强**：使用机器学习优化元素重要性判断
3. **批量分析**：支持同时分析多个页面
4. **实时更新**：监控页面变化，自动更新DOM信息
5. **插件生态**：支持自定义分析规则和筛选策略
