<template>
  <div class="json-editor">
    <codemirror ref="cm" v-model="code" :code="text" :options="cmOptions" @codeChange="onCodeChange"></codemirror>
  </div>
</template>

<script>
// 全局引入vue-codemirror
import { codemirror } from 'vue-codemirror'
// 引入css文件
import 'codemirror/lib/codemirror.css'
// 引入主题 可以从 codemirror/theme/ 下引入多个
import 'codemirror/theme/idea.css'
// 引入语言模式 可以从 codemirror/mode/ 下引入多个
import 'codemirror/mode/sql/sql.js'

export default {
  name: 'JsonEditor',
  components: { codemirror },
  model: {
    prop: 'code', // 指向props的参数名
    event: 'codeUpdate' // 事件名称
  },
  props: {
    readonly: {
      default: false,
      type: Boolean
    },
    code: {
      default: '',
      type: String
    },
    lang: {
      default: 'json',
      type: String
    },
    width: {
      default: '100%',
      type: [String, Number]
    },
    height: {
      default: '400px',
      type: [String, Number]
    }
  },
  data() {
    return {
      // code: '',
      cmOptions: {
        // 语言及语法模式
        mode: 'text/x-sql',
        // 主题
        theme: 'idea',
        // 显示函数
        line: true,
        lineNumbers: true,
        // 软换行
        lineWrapping: true,
        // tab宽度
        tabSize: 4
      }
    }
  },
  computed: {
    text: {
      get: function () {
        return this.code
      },
      set: function (val) {
        val = val.replaceAll('	', '    ')
        this.$emit('codeUpdate', val)
      }
    }
  },
  methods: {
    onCodeChange(editor) {
      var val = editor.getValue()
      this.text = val
    },
    layout(code) {
      const editor = this.$refs.cm.codemirror
      if (editor) {
        editor.refresh()
        if (code) {
          editor.setValue(code)
        } else {
          editor.setValue(this.code)
        }
      }
    }
  }
}
</script>
