<template>
  <div id="ProcessBar" class="pB_Container">
    <div class="first" :style="error" v-if="processValue[0] != '0'">
      <!-- {{ getProcessDesc(processValue[0]) }} -->
    </div>
    <div class="second" :style="cancel" v-if="processValue[1] != '0'">
      <!-- {{ getProcessDesc(processValue[1]) }} -->
    </div>
    <div class="third" :style="success" v-if="processValue[2] != '0'">
      <!-- {{ getProcessDesc(processValue[2]) }} -->
    </div>
    <div class="last" :style="notDone" v-if="processValue[3] != '0'">
      <!-- {{ getProcessDesc(processValue[3]) }} -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProcessBar',
  props: {
    processValue: {
      type: Array,
      default: function () {
        return [0]
      }
    }
  },
  data() {
    return {
      error: {
        width: this.processValue[0] + '%'
      },
      cancel: {
        width: this.processValue[1] + '%'
      },
      success: {
        width: this.processValue[2] + '%'
      },
      notDone: {
        width: this.processValue[3] + '%'
      }
    }
  },

  components: {},
  methods: {
    getProcessDesc(data) {
      return data == 0 ? ' ' : data + '%'
    }
  },

  watch: {
    processValue(val, oldVal) {
      console.log(this.processValue)
    }
  },

  mounted() {}
}
</script>
<style scoped>
.pB_Container {
  width: 75%;
  background-color: #e6ebf5;
  height: 8px;
  display: inline-flex;
  line-height: 8px;
  text-align: right;
  overflow: hidden;
  color: white;
  box-shadow: inset 0 2px 2px rgba(0, 0, 0, 0.1);
  border-radius: 20px 20px 20px 20px !important;
}
.pB_Container div {
  float: left;
  border-radius: 20px;
}
.first {
  background-color: rgb(115, 216, 151);
  border-right: 1px #f5f5f5 solid;
  /* border-radius: 0 20px 20px 0 !important; */
}
.second {
  background-color: rgb(255, 117, 117);
  border-right: 1px #f5f5f5 solid;
  border-radius: 0 20px 20px 0 !important;

  /* background: radial-gradient(
    15px at left,
    #f5f5f5 15px,
    rgb(255, 117, 117) 20%
  ); */
}
.third {
  background-color: rgb(255, 205, 93);
  border-right: 1px #fff solid;
  border-radius: 0 20px 20px 0 !important;
  /* background: radial-gradient(
    15px at left,
    #f5f5f5 15px,
    rgb(255, 205, 93) 20%
  ); */
}
.last {
  background-color: #3296ff;
  border-right: 1px #fff solid;
  border-radius: 0 20px 20px 0 !important;
}
</style>
