import axios from '@/views/zpPerformance/utils/request'

// 获取取样数据接口
export const getSamplerList = form => axios.post('auth/report/getSamplerList', form)

//获取聚合报告接口
export const getAggregateList = form => axios.post('auth/report/getAggregateList', form)

//获取单次聚合报告接口
export const getReportList = form => axios.post('auth/test/result/getAggReportList', form)

//新建接口
export const create = form => axios.post('/auth/element/create', form)

//删除接口
export const deleted = form => axios.post('/auth/element/delete', form)

//获取详情页接口
export const getDetail = id =>
  axios.get('auth/element/getDetail', {
    params: {
      id: id
    }
  })

//启动线程组接口
export const startTest = planId =>
  axios.post('auth/element/startTest', {
    planId
  })

//修改新增保存配置接口
export const update = form => axios.post('auth/element/update', form)

//获取测试计划列表
export const getPlansDict = () => axios.get('auth/element/getPlansDict')

//获取线程组树结构
export const getMenuTree = user =>
  axios.get('auth/common/getMenuTree', {
    params: {
      user: user
    }
  })

//启用禁用接口
export const enabled = formData => axios.post('auth/element/enabled', formData)

//获取查看结果树数据列表

export const getSampleTree = formData => axios.post('auth/test/result/getSampleTree', formData)

//获取详情页接口
export const getResultDetail = id =>
  axios.get('auth/test/result/detail', {
    params: {
      id: id
    }
  })

//获取节点数据
export const getClusters = () => axios.get('/auth/cluster/getClusters')

//是否启用节点

export const clusterEnabled = formData => axios.post('/auth/cluster/enabled', formData)

//删除取样数据
export const deleteSample = formData => axios.post('/auth/report/deleteSampler', formData)

//移动组件 编辑排序
export const move = formData => axios.post('/auth/element/move', formData)

//拖拽组件 编辑分组
export const treeDrag = formData => axios.post('/auth/element/treeDrag', formData)

export const getUsers = formData => axios.post('/auth/user/getUsers', formData)

export const createUser = formData => axios.post('/auth/user/create', formData)
