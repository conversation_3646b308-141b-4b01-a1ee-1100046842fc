import request from '@/utils/request'
import qs from 'qs'

//添加
export function addBatchCases(data) {
  return request({
    url: '/boss/batchCase/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function editBatchCases(data) {
  return request({
    url: '/boss/batchCase/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteBatchCases(id) {
  return request({
    url: '/boss/batchCase/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//用例执行
export function executeBatchCase(data) {
  return request({
    url: '/boss/batchCase/batchExecute',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data),
    timeout: 60 * 1000
  })
}

//获取
export function getBatchCases(projectId, groupId, id, envId, pageIndex, pageSize) {
  return request({
    url: '/boss/batchCase/list',
    method: 'get',
    params: {
      projectId,
      groupId,
      envId,
      id,
      pageIndex,
      pageSize
    }
  })
}

//复制
export function copyBatchCase(batchId) {
  return request({
    url: '/boss/batchCase/copy',
    method: 'get',
    params: {
      batchId
    }
  })
}

//获取单条
export function getBatchCaseById(batchCaseId) {
  return request({
    url: '/boss/batchCase/listById',
    method: 'get',
    params: {
      batchCaseId
    }
  })
}

//获取
export function searchBatchCase(projectId, groupId, keyword, pageIndex, pageSize) {
  return request({
    url: '/boss/batchCase/search',
    method: 'get',
    params: {
      projectId,
      groupId,
      keyword,
      pageIndex,
      pageSize
    }
  })
}
