import request from '@/utils/request'
import qs from 'qs'

//获取
export function getLog(projectId, taskId, isSuccess, startTime, endTime, pageIndex, pageSize) {
  return request({
    url: '/boss/log/list',
    method: 'get',
    params: {
      projectId,
      taskId,
      isSuccess,
      startTime,
      endTime,
      pageIndex,
      pageSize
    },
    timeout: 60 * 1000
  })
}

//获取
export function getLogDetail(taskId, serialId, pageIndex, pageSize) {
  return request({
    url: '/boss/log/detail/list',
    method: 'get',
    params: {
      taskId,
      serialId,
      pageIndex,
      pageSize
    },
    timeout: 20 * 1000
  })
}

//获取
export function getLogDetailSimple(serialId, pageIndex, pageSize) {
  return request({
    url: '/boss/log/detail/simpleList',
    method: 'get',
    params: {
      serialId,
      pageIndex,
      pageSize
    },
    timeout: 20 * 1000
  })
}

//获取
export function getLogReportSimple(serialId) {
  return request({
    url: '/boss/log/report/simple',
    method: 'get',
    params: {
      serialId
    },
    timeout: 20 * 1000
  })
}
