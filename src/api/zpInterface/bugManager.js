import request from '@/utils/request'
import qs from 'qs'

export function getBugList(interfaceId, caseId, caseStatus, author, pageIndex, pageSize) {
  return request({
    url: '/boss/bug/list',
    method: 'get',
    params: {
      interfaceId,
      caseId,
      caseStatus,
      author,
      pageIndex,
      pageSize
    }
  })
}

//id查询
export function listBy(message, urlMessage) {
  return request({
    url: '/boss/bug/listBy',
    method: 'get',
    params: {
      message,
      urlMessage
    }
  })
}

export function addBug(data) {
  return request({
    url: '/boss/bug/add',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data
  })
}

export function updateBug(data) {
  return request({
    url: '/boss/bug/update',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data
  })
}

export function deleteBug(id) {
  return request({
    url: '/boss/bug/delete',
    method: 'get',
    params: {
      id
    }
  })
}
