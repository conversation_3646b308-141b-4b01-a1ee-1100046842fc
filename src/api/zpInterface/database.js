import request from '@/utils/request'
import qs from 'qs'

//获取
export function getdb(projectId, databaseName, pageIndex, pageSize) {
  return request({
    url: '/boss/database/list',
    method: 'get',
    params: {
      projectId,
      databaseName,
      pageIndex,
      pageSize
    }
  })
}

//添加
export function adddb(data) {
  return request({
    url: '/boss/database/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function editdb(data) {
  return request({
    url: '/boss/database/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deletedb(id) {
  return request({
    url: '/boss/database/delete',
    method: 'get',
    params: {
      id
    }
  })
}
