import request from '@/utils/request'
import qs from 'qs'

//获取
export function getParameter(projectId, name, type, global, operator, pageIndex, pageSize) {
  return request({
    url: '/boss/parameter/list',
    method: 'get',
    params: {
      projectId,
      name,
      type,
      global,
      operator,
      pageIndex,
      pageSize
    }
  })
}

//添加
export function addParameter(data) {
  return request({
    url: '/boss/parameter/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function editParameter(data) {
  return request({
    url: '/boss/parameter/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteParameter(id) {
  return request({
    url: '/boss/parameter/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//获取
export function getParameterList(projectId, groupId, id, pageIndex, pageSize) {
  return request({
    url: '/boss/parameter/list',
    method: 'get',
    params: {
      projectId,
      groupId,
      id,
      pageIndex,
      pageSize
    }
  })
}

//获取
export function getBatchParameterList(projectId, groupId, id, pageIndex, pageSize) {
  return request({
    url: '/boss/parameter/batchList',
    method: 'get',
    params: {
      projectId,
      groupId,
      id,
      pageIndex,
      pageSize
    }
  })
}
