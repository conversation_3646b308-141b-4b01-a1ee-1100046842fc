import request from '@/utils/request'
import qs from 'qs'

//添加
export function addBatchStepCases(data) {
  return request({
    url: '/boss/batchCaseStep/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function editBatchStepCases(data) {
  return request({
    url: '/boss/batchCaseStep/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取
export function getBatchCaseSteps(batchId, pageIndex, pageSize) {
  return request({
    url: '/boss/batchCaseStep/list',
    method: 'get',
    params: {
      batchId,
      pageIndex,
      pageSize
    }
  })
}

//获取单条步骤
export function getBatchCaseStepById(id) {
  return request({
    url: '/boss/batchCaseStep/get',
    method: 'get',
    params: {
      id
    }
  })
}

//删除
export function deleteBatchCaseSteps(id) {
  return request({
    url: '/boss/batchCaseStep/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//根据用例id批量删除用例步骤
export function deleteBatchCaseStepsByIds(ids) {
  return request({
    url: '/boss/batchCaseStep/deleteByIds',
    method: 'get',
    params: {
      ids
    }
  })
}

//移动分组
export function moveBatchCaseSteps(frontId, afterId, type) {
  return request({
    url: '/boss/batchCaseStep/move',
    method: 'get',
    params: {
      frontId,
      afterId,
      type
    }
  })
}
