import request from '@/utils/request'
import qs from 'qs'

//获取
export function getAlarm(projectId, taskId, taskName, pageIndex, pageSize, notifyTo) {
  return request({
    url: '/boss/taskAlarm/list',
    method: 'get',
    params: {
      projectId,
      taskId,
      taskName,
      pageIndex,
      pageSize,
      notifyTo
    }
  })
}

//添加
export function addAlarm(data) {
  return request({
    url: '/boss/taskAlarm/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function editAlarm(data) {
  return request({
    url: '/boss/taskAlarm/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteAlarm(id) {
  return request({
    url: '/boss/taskAlarm/delete',
    method: 'get',
    params: {
      id
    }
  })
}
