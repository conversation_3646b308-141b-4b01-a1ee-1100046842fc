import request from '@/utils/request'
import qs from 'qs'

//获取
export function getMock(interfaceId, id) {
  return request({
    url: '/boss/advmock/list',
    method: 'get',
    params: {
      interfaceId,
      id
    }
  })
}

//添加
export function addMock(data) {
  return request({
    url: '/boss/advmock/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function updateMock(data) {
  return request({
    url: '/boss/advmock/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteMock(id) {
  return request({
    url: '/boss/advmock/delete',
    method: 'get',
    params: {
      id
    }
  })
}
