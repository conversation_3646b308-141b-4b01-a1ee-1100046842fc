import request from '@/utils/request'
import qs from 'qs'

//获取
export function getEnvs(projectId, env, pageIndex, pageSize) {
  return request({
    url: '/boss/env/list',
    method: 'get',
    params: {
      projectId,
      env,
      pageIndex,
      pageSize
    }
  })
}

export function getEnvsById(id) {
  return request({
    url: '/boss/env/envById',
    method: 'get',
    params: {
      id
    }
  })
}

//添加
export function addEnvs(data) {
  return request({
    url: '/boss/env/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function editEnvs(data) {
  return request({
    url: '/boss/env/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteEnvs(id) {
  return request({
    url: '/boss/env/delete',
    method: 'get',
    params: {
      id
    }
  })
}
