import request from '@/utils/request'
import qs from 'qs'

//获取
export function getGroup(projectId, type) {
  return request({
    url: '/boss/interfaceGroup/list',
    method: 'get',
    params: {
      projectId,
      type
    },
    timeout: 60 * 1000
  })
}

//获取
export function getBatchGroup(projectId, type) {
  return request({
    url: '/boss/interfaceGroup/batchList',
    method: 'get',
    params: {
      projectId,
      type
    }
  })
}

//添加
export function addGroup(data) {
  return request({
    url: '/boss/interfaceGroup/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function editGroup(data) {
  return request({
    url: '/boss/interfaceGroup/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteGroup(id) {
  return request({
    url: '/boss/interfaceGroup/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//移动分组
export function moveGroup(frontId, afterId, type) {
  return request({
    url: '/boss/interfaceGroup/move',
    method: 'get',
    params: {
      frontId,
      afterId,
      type
    }
  })
}

//获取-新-包含接口
export function getGroupV2(projectId, groupId, type, pageIndex, pageSize) {
  return request({
    url: '/boss/interfaceGroup/listV2',
    method: 'get',
    params: {
      projectId,
      groupId,
      type,
      pageIndex,
      pageSize
    },
    timeout: 60 * 1000
  })
}

//获取-新-不包含接口
export function getGroupV3(projectId, groupId, type, pageIndex, pageSize) {
  return request({
    url: '/boss/interfaceGroup/listV3',
    method: 'get',
    params: {
      projectId,
      groupId,
      type,
      pageIndex,
      pageSize
    }
  })
}

//获取所有父级模块
export function getAllParentList(groupId) {
  return request({
    url: '/boss/interfaceGroup/allParent/listV2',
    method: 'get',
    params: {
      groupId
    }
  })
}
