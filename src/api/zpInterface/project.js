import request from '@/utils/request'
import qs from 'qs'

//获取
export function getProject(item, projectId, name) {
  return request({
    url: '/boss/project/list',
    method: 'get',
    params: {
      item,
      projectId,
      name
    }
  })
}

//获取
export function getProjectById(id) {
  return request({
    url: '/boss/project/listById',
    method: 'get',
    params: {
      id
    }
  })
}

//添加
export function addProject(data) {
  return request({
    url: '/boss/project/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取项目描述
export function getProjectDesc(projectId) {
  return request({
    url: '/boss/project/desc',
    method: 'get',
    params: {
      projectId
    }
  })
}
