import request from '@/utils/request'
import qs from 'qs'

//添加
export function addCases(data) {
  return request({
    url: '/boss/case/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function editCases(data) {
  return request({
    url: '/boss/case/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取
export function getCase(interfaceId, pageIndex, pageSize) {
  return request({
    url: '/boss/case/list',
    method: 'get',
    params: {
      interfaceId,
      pageIndex,
      pageSize
    }
  })
}

//删除
export function deleteCase(id) {
  return request({
    url: '/boss/case/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//用例执行
export function executeCase(data) {
  return request({
    url: '/boss/case/execute',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data),
    timeout: 60 * 1000
  })
}

//用例调制
export function debugExecuteCase(data) {
  return request({
    url: '/kanzhun/case/debug/executed',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取单条用例
export function getCaseById(id) {
  return request({
    url: '/boss/case/get',
    method: 'get',
    params: {
      id
    }
  })
}

//用例执行日志
export function getCaseExecuteLog(caseId, pageIndex, pageSize, result) {
  return request({
    url: '/boss/case/log/list',
    method: 'get',
    params: {
      caseId,
      pageIndex,
      pageSize,
      result
    },
    timeout: 30 * 1000
  })
}

//获取参数来源
export function getParamsSource(paramName) {
  return request({
    url: '/boss/case/paramsSource',
    method: 'get',
    params: {
      paramName
    }
  })
}

//复制
export function copyCase(interfaceId) {
  return request({
    url: '/boss/case/copy',
    method: 'get',
    params: {
      interfaceId
    }
  })
}

export function getfailedList(id, projectId) {
  return request({
    url: '/boss/case/failList',
    method: 'get',
    params: {
      id,
      projectId
    },
    timeout: 30 * 1000
  })
}
