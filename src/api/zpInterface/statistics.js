import request from '@/utils/request'
import qs from 'qs'

//获取
export function getInterfaceMaintainerDistribution(projectId, startTime, endTime, status) {
  return request({
    url: '/boss/statistics/interfaceMaintainer/distribution',
    method: 'get',
    params: {
      projectId,
      startTime,
      endTime,
      status
    }
  })
}

//获取
export function getCaseMaintainerDistribution(projectId, startTime, endTime, status) {
  return request({
    url: '/boss/statistics/caseMaintainer/distribution',
    method: 'get',
    params: {
      projectId,
      startTime,
      endTime,
      status
    }
  })
}

//获取
export function gettaskCaseDistribution(projectId, status) {
  return request({
    url: '/boss/statistics/taskCase/distribution',
    method: 'get',
    params: {
      projectId,
      status
    }
  })
}

//获取
export function getDailyExecuteCaseStatistics(projectId) {
  return request({
    url: '/boss/statistics/daily/executeCase',
    method: 'get',
    params: {
      projectId
    }
  })
}

//获取
export function getPersonalStatistics(name, email) {
  return request({
    url: '/boss/statistics/personal',
    method: 'get',
    params: {
      name,
      email
    }
  })
}

//获取
export function getInterfaceAndCaseCount(projectId, status) {
  return request({
    url: '/boss/statistics/interfaceAndCase/count',
    method: 'get',
    params: {
      projectId,
      status
    }
  })
}
