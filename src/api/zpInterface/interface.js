import request from '@/utils/request'
import qs from 'qs'

//获取
export function getInterfaces(projectId, groupId, interfaceId, pageIndex, pageSize) {
  return request({
    url: '/boss/interface/list',
    method: 'get',
    params: {
      projectId,
      groupId,
      interfaceId,
      pageIndex,
      pageSize
    }
  })
}

//获取
export function getSampleInterfaces(projectId, groupId, interfaceId, pageIndex, pageSize) {
  return request({
    url: '/boss/interface/list/sample',
    method: 'get',
    params: {
      projectId,
      groupId,
      interfaceId,
      pageIndex,
      pageSize
    }
  })
}

//关键字搜索
export function searchInterfaces(projectId, groupId, interfaceId, keyword, version, status, author, pageIndex, pageSize) {
  return request({
    url: '/boss/interface/search',
    method: 'get',
    params: {
      projectId,
      groupId,
      interfaceId,
      keyword,
      version,
      status,
      author,
      pageIndex,
      pageSize
    }
  })
}

//添加
export function addInterfaces(data) {
  return request({
    url: '/boss/interface/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function editInterfaces(data) {
  return request({
    url: '/boss/interface/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteInterfaces(id) {
  return request({
    url: '/boss/interface/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//获取
export function getInterfaceById(interfaceId) {
  return request({
    url: '/boss/interface/listById',
    method: 'get',
    params: {
      interfaceId
    }
  })
}

//获取SecretKey-废弃
export function getInterfaceSecretKey(envIp, envId, db, phone, projectId) {
  return request({
    url: '/business/getSecretKeyV2',
    method: 'get',
    params: {
      envIp,
      envId,
      db,
      phone,
      projectId
    }
  })
}

//获取SecretKey
export function getInterfaceSecretKeyV4(envIp, envId, projectId, phone, userId, envid) {
  return request({
    url: '/business/getSecretKeyV4',
    method: 'get',
    params: {
      envIp,
      envId,
      userId,
      phone,
      projectId,
      envid
    }
  })
}

//获取SecretKey
export function getRc4sign(data, key) {
  return request({
    url: '/request/rc4',
    method: 'get',
    params: {
      data,
      key
    }
  })
}

//接口调试
export function interfaceGebug(data) {
  return request({
    url: '/request/key',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取SecretKey
export function searchInterfaceBycaseId(caseId, preCaseId, projectId, caseStatus) {
  return request({
    url: '/boss/interface/searchInterfaceBycaseId',
    method: 'get',
    params: {
      caseId,
      preCaseId,
      projectId,
      caseStatus
    }
  })
}

//移动分组
export function moveInterface(frontId, afterId, type) {
  return request({
    url: '/boss/interface/move',
    method: 'get',
    params: {
      frontId,
      afterId,
      type
    }
  })
}

//skip跳转
export function skipYapi(q) {
  return request({
    url: '/skip/Yapi',
    method: 'get',
    params: {
      q
    }
  })
}

//查询接口
export function searchInterfacesV2(projectId, groupId, interfaceId, keyword, version, status, author, caseAuthor, pageIndex, pageSize) {
  return request({
    url: '/boss/interface/searchV2',
    method: 'get',
    params: {
      projectId,
      groupId,
      interfaceId,
      keyword,
      version,
      status,
      author,
      caseAuthor,
      pageIndex,
      pageSize
    }
  })
}

// 复制接口/用例到项目
export function copyToProject(params) {
  return request({
    url: '/boss/interface/copyToProject',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(params)
  })
}
