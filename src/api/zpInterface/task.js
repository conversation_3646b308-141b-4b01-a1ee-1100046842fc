import request from '@/utils/request'
import qs from 'qs'

//获取
export function getTask(type, projectId, taskId, taskName, author, pageIndex, pageSize) {
  return request({
    url: '/boss/task/list',
    method: 'get',
    params: {
      type,
      projectId,
      taskId,
      taskName,
      author,
      pageIndex,
      pageSize
    }
  })
}

//添加
export function addTask(data) {
  return request({
    url: '/boss/task/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function editTask(data) {
  return request({
    url: '/boss/task/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteTask(id) {
  return request({
    url: '/boss/task/delete',
    method: 'get',
    params: {
      id
    }
  })
}
