import axios from 'axios'

// Playwright API基础URL
const baseURL = 'http://localhost:3111/api/agent/dom'

// 创建axios实例
const domApi = axios.create({
  baseURL,
  timeout: 60000, // 60秒超时，DOM分析可能需要较长时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// 分析页面DOM结构
export function analyzePageDOM(url, cookie, options = {}) {
  const requestData = {
    url,
    cookie,
    options: {
      // 是否包含隐藏元素
      includeHidden: options.includeHidden || false,
      // 最大深度
      maxDepth: options.maxDepth || 10,
      // 元素类型过滤
      elementTypes: options.elementTypes || ['input', 'button', 'a', 'select', 'textarea'],
      // 是否包含样式信息
      includeStyles: options.includeStyles || false,
      // 超时时间（毫秒）
      timeout: options.timeout || 30000
    }
  }

  return domApi.post('/analyze', requestData)
}

// 获取页面截图（用于辅助分析）
export function getPageScreenshot(url, cookie) {
  const requestData = { url }

  // 如果提供了cookie，添加到请求中
  if (cookie) {
    requestData.cookie = cookie
  }

  return domApi.post('/screenshot', requestData)
}

// 智能提取页面关键元素
export function extractKeyElements(url, scenario, cookie) {
  const requestData = {
    url,
    scenario // 测试场景描述，用于智能筛选相关元素
  }

  // 如果提供了cookie，添加到请求中
  if (cookie) {
    requestData.cookie = cookie
  }

  return domApi.post('/extract-key-elements', requestData)
}

// 验证验证Playwright Locator
export function validatePlaywrightLocators(url, locators, cookie) {
  const requestData = {
    url,
    locators
  }

  // 如果提供了cookie，添加到请求中
  if (cookie) {
    requestData.cookie = cookie
  }

  return domApi.post('/validate-playwright-locators', requestData)
}
