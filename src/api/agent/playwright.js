import axios from 'axios'

// Playwright API基础URL
const baseURL = 'http://localhost:3111/api/playwright'

/**
 * 启动Playwright本地执行器服务
 * @returns {Promise} 返回启动结果
 */
export function startPlaywrightService() {
  return axios({
    url: `${baseURL}/service/start`,
    method: 'post'
  })
}

/**
 * 停止Playwright本地执行器服务
 * @returns {Promise} 返回停止结果
 */
export function stopPlaywrightService() {
  return axios({
    url: `${baseURL}/service/stop`,
    method: 'post'
  })
}

/**
 * 获取Playwright本地执行器服务状态
 * @returns {Promise} 返回服务状态
 */
export function getPlaywrightServiceStatus() {
  return axios({
    url: `${baseURL}/service/status`,
    method: 'get'
  })
}

/**
 * Playwright环境检查
 * @returns {Promise} 返回安装结果
 */
export function installPlaywrightBrowser() {
  return axios({
    url: `${baseURL}/install`,
    method: 'post'
  })
}

/**
 * 更新Playwright配置
 * @param {Object} settings 配置对象
 * @returns {Promise} 返回更新结果
 */
export function updatePlaywrightConfig(settings) {
  return axios({
    url: `${baseURL}/config/update`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: settings
  })
}

/**
 * 启动Playwright录制
 * @param {Object} options 录制选项
 * @param {string} [options.url] 录制目标URL
 * @param {string} [options.outputFile] 输出文件名
 * @param {string} [options.preScript] 预执行脚本
 * @returns {Promise} 返回录制进程信息
 */
export function startPlaywrightRecording(options = {}) {
  return axios({
    url: `${baseURL}/record/start`,
    method: 'post',
    data: options
  })
}

/**
 * 停止Playwright录制
 * @param {number} processId 录制进程ID
 * @returns {Promise} 返回录制结果
 */
export function stopPlaywrightRecording(processId) {
  return axios({
    url: `${baseURL}/record/stop`,
    method: 'post',
    data: { processId }
  })
}

/**
 * 保存Playwright脚本 - 兼容旧版本单脚本和新版本测试套件
 * @param {string|Object} scriptOrTestSuite 测试脚本内容或测试套件对象
 * @param {string} [filename] 脚本文件名（仅在传入单脚本时使用）
 * @returns {Promise} 返回保存结果
 */
export function savePlaywrightScript(scriptOrTestSuite, filename) {
  // 判断是否为新的测试套件格式
  if (typeof scriptOrTestSuite === 'object' && scriptOrTestSuite.suiteName) {
    // 新的测试套件格式
    return axios({
      url: `${baseURL}/script/save`,
      method: 'post',
      data: { testSuite: scriptOrTestSuite }
    })
  } else {
    // 旧的单脚本格式，保持向后兼容
    return axios({
      url: `${baseURL}/script/save`,
      method: 'post',
      data: { script: scriptOrTestSuite, filename }
    })
  }
}

/**
 * 运行Playwright测试 - 支持新的文件结构
 * @param {string} suitePath 测试套件路径
 * @param {Object} [options] 执行选项
 * @param {boolean} [options.headed=true] 是否使用有界面模式
 * @param {boolean} [options.debug=false] 是否使用调试模式
 * @param {number} [options.timeout=30000] 超时时间（毫秒）
 * @returns {Promise} 返回测试结果
 */
export function runPlaywrightTest(suitePath, options = {}) {
  return axios({
    url: `${baseURL}/test/run`,
    method: 'post',
    data: {
      suitePath,
      options: {
        headed: true,
        debug: false,
        timeout: 30000,
        ...options
      }
    }
  })
}

/**
 * 批量执行多个Playwright测试套件
 * @param {Array<string>} suitePaths 测试套件路径数组
 * @param {Object} [options] 执行选项
 * @param {boolean} [options.headed=false] 是否使用有界面模式（批量执行默认无界面）
 * @param {boolean} [options.debug=false] 是否使用调试模式
 * @param {number} [options.timeout=60000] 超时时间（毫秒）
 * @param {number} [options.workers=2] 并发工作进程数
 * @returns {Promise} 返回批量测试结果
 */
export function runPlaywrightTestBatch(suitePaths, options = {}) {
  return axios({
    url: `${baseURL}/test/run-batch`,
    method: 'post',
    data: {
      scriptPaths: suitePaths, // 保持后端兼容性
      options: {
        headed: false,
        debug: false,
        timeout: 60000,
        workers: 1,
        ...options
      }
    }
  })
}

/**
 * 获取Playwright录制状态
 * @param {String} processId - 进程ID
 * @returns {Promise} - 录制状态及代码
 */
export function getPlaywrightRecordingStatus(processId) {
  return axios({
    url: `${baseURL}/status?processId=${processId}`,
    method: 'get'
  })
}

/**
 * 获取已保存的测试套件列表
 * @returns {Promise} 返回测试套件列表
 */
export function getPlaywrightSuitesList() {
  return axios({
    url: `${baseURL}/suites/list`,
    method: 'get'
  })
}

/**
 * 删除指定的测试套件
 * @param {string} suiteName 测试套件名称
 * @returns {Promise} 返回删除结果
 */
export function deletePlaywrightSuite(suiteName) {
  return axios({
    url: `${baseURL}/suites/${encodeURIComponent(suiteName)}`,
    method: 'delete'
  })
}

/**
 * 启动Playwright UI模式
 * @param {Object} options UI模式选项
 * @param {string} [options.testPath] 指定测试文件或目录路径
 * @returns {Promise} 返回启动结果
 */
export function startPlaywrightUI(options = {}) {
  return axios({
    url: `${baseURL}/ui/start`,
    method: 'post',
    data: options
  })
}

/**
 * 停止Playwright UI模式
 * @returns {Promise} 返回停止结果
 */
export function stopPlaywrightUI() {
  return axios({
    url: `${baseURL}/ui/stop`,
    method: 'post'
  })
}

/**
 * 获取Playwright UI模式状态
 * @returns {Promise} 返回UI模式状态
 */
export function getPlaywrightUIStatus() {
  return axios({
    url: `${baseURL}/ui/status`,
    method: 'get'
  })
}
