import request from '@/utils/request'
import qs from 'qs'

//添加任务
export function addAutoTask(data) {
  return request({
    url: '/boss/zpagent/auto/task/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//分页查询任务
export function getAutoTaskList(projectId, taskName, status, page, size) {
  return request({
    url: '/boss/zpagent/auto/task/listByPage',
    method: 'get',
    params: { projectId, taskName, status, page, size }
  })
}

//删除任务
export function deleteAutoTask(id) {
  return request({
    url: '/boss/zpagent/auto/task/delete',
    method: 'get',
    params: { id }
  })
}

//更新任务
export function updateAutoTask(data) {
  return request({
    url: '/boss/zpagent/auto/task/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
