import request from '@/utils/request'
import qs from 'qs'

//添加测试用例
export function addAutoCase(data) {
  return request({
    url: '/boss/zpagent/auto/case/add',
    method: 'post',
    data: qs.stringify(data)
  })
}

//分页获取测试用例
export function getAutoCaseList(projectId, caseName, groupId, status, creator, page, size) {
  return request({
    url: '/boss/zpagent/auto/case/list',
    method: 'get',
    params: { projectId, caseName, groupId, status, creator, page, size }
  })
}

//分页获取测试用例
//根据多个分组ID查询自动化测试用例列表
//支持查询多个分组及其所有子分组的用例
export function listByMultipleGroups(projectId, caseName, groupIds, page, size) {
  return request({
    url: '/boss/zpagent/auto/case/listByMultipleGroups',
    method: 'get',
    params: { projectId, caseName, groupIds, page, size }
  })
}

//删除测试用例
export function deleteAutoCase(id, name) {
  return request({
    url: '/boss/zpagent/auto/case/delete',
    method: 'get',
    params: { id, name }
  })
}

//更新测试用例
export function updateAutoCase(data) {
  return request({
    url: '/boss/zpagent/auto/case/update',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 获取Playwright配置
export function getPlaywrightSetting() {
  return request({
    url: '/agent/playwright/settings',
    method: 'get'
  })
}

// 保存Playwright配置
export function savePlaywrightSetting(data) {
  return request({
    url: '/agent/playwright/settings',
    method: 'post',
    data
  })
}

//查询测试用例详情
export function getAutoCaseDetail(projectId, beforeId, afterId, groupIds, caseIds) {
  return request({
    url: '/boss/zpagent/auto/case/listByIds',
    method: 'get',
    params: { projectId, beforeId, afterId, groupIds, caseIds }
  })
}

//更新用例执行成功数
export function updateAutoCaseStats(id, isSuccess) {
  return request({
    url: '/boss/zpagent/auto/case/updateStats',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify({ id, isSuccess })
  })
}
