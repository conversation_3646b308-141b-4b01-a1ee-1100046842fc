import request from '@/utils/request'
import qs from 'qs'

//添加提示词收藏
export function addPromptFavorite(data) {
  return request({
    url: '/boss/zpagent/promptFavorite/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除提示词收藏
export function deletePromptFavorite(promptId, userName, agentId) {
  return request({
    url: '/boss/zpagent/promptFavorite/deleteByPromptAndUserAndAgent',
    method: 'get',
    params: {
      promptId,
      userName,
      agentId
    }
  })
}
