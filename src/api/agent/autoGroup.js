import request from '@/utils/request'
import qs from 'qs'

//添加分组
export function addAutoGroup(data) {
  return request({
    url: '/boss/zpagent/auto/group/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//分页获取分组
export function getAutoGroupList(projectId) {
  return request({
    url: '/boss/zpagent/auto/group/listWithPerf',
    method: 'get',
    params: { projectId }
  })
}

//删除分组
export function deleteAutoGroup(projectId, id) {
  return request({
    url: '/boss/zpagent/auto/group/delete',
    method: 'get',
    params: { projectId, id }
  })
}

//更新分组
export function updateAutoGroup(data) {
  return request({
    url: '/boss/zpagent/auto/group/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//拖动排序
export function sortAutoGroup(projectId, data) {
  return request({
    url: '/boss/zpagent/auto/group/sort',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify({ projectId, ...data })
  })
}
