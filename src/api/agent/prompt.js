import request from '@/utils/request'
import qs from 'qs'

//分页获取提示词
export function getPromptList(agentId, keyword, tag, userName, type, pageIndex, pageSize) {
  return request({
    url: '/boss/zpagent/prompt/list',
    method: 'get',
    params: {
      agentId,
      keyword,
      tag,
      userName,
      type,
      pageIndex,
      pageSize
    }
  })
}

//添加提示词
export function addPrompt(data) {
  return request({
    url: '/boss/zpagent/prompt/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新提示词
export function updatePrompt(data) {
  return request({
    url: '/boss/zpagent/prompt/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除提示词
export function deletePrompt(id) {
  return request({
    url: '/boss/zpagent/prompt/delete',
    method: 'get',
    params: {
      id
    }
  })
}
