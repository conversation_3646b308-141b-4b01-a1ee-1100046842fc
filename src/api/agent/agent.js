import request from '@/utils/request'
import qs from 'qs'

// 获取智能体列表
export function getAgentList(keyword, type, pageIndex, pageSize) {
  return request({
    url: '/boss/zpagent/list',
    method: 'get',
    params: {
      keyword,
      type,
      pageIndex,
      pageSize
    }
  })
}

// 更新智能体次数
export function updateAgent(id) {
  return request({
    url: '/boss/zpagent/updateCount',
    method: 'get',
    params: {
      id
    }
  })
}

// 获取智能体详情
export function getAgentDetail(id) {
  return request({
    url: '/boss/zpagent/detail',
    method: 'get',
    params: { id }
  })
}
