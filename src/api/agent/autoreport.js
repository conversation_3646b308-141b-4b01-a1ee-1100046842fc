import request from '@/utils/request'
import qs from 'qs'

//添加测试报告
export function addAutoReport(data) {
  return request({
    url: '/boss/zpagent/auto/report/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//分页获取测试报告
export function getAutoReportList(projectId, name, status, page, size) {
  return request({
    url: '/boss/zpagent/auto/report/listByPage',
    method: 'get',
    params: { projectId, name, status, page, size }
  })
}

// 获取测试报告详情
export function getAutoReportDetail(id) {
  return request({
    url: '/boss/zpagent/auto/report/get',
    method: 'get',
    params: { id }
  })
}
