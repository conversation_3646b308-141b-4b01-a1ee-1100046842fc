import request from '@/utils/request'
import qs from 'qs'

//添加分组
export function addGroup(type, data) {
  return request({
    url: '/boss/interfaceGroup/' + type,
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取项目分组
export function getProjectGroup(projectId, type) {
  return request({
    url: '/boss/interfaceGroup/list',
    method: 'get',
    params: {
      projectId,
      type
    }
  })
}

//删除分组
export function deleteGroup(id) {
  return request({
    url: '/boss/interfaceGroup/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//分组移动
export function moveGroup(frontId, afterId) {
  return request({
    url: '/boss/projectGroup/move',
    method: 'post',
    params: {
      id
    }
  })
}
