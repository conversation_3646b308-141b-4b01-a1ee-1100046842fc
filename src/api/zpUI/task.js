import request from '@/utils/request'

//获取任务
export function getTask(projectId, taskId, taskName, pageIndex, pageSize) {
  return request({
    url: '/boss/task/list?type=AppUI',
    method: 'get',
    params: {
      projectId,
      taskId,
      taskName,
      pageIndex,
      pageSize
    }
  })
}

//编辑任务
export function EditTask(taskId) {
  return request({
    url: '/boss/task/listById',
    method: 'get',
    params: {
      taskId
    }
  })
}

//删除任务
export function deleteTask(id) {
  return request({
    url: '/boss/task/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//执行任务前获取envid
export function start(taskId) {
  return request({
    url: '/boss/task/listById',
    method: 'get',
    params: {
      taskId
    }
  })
}

//开始执行任务--android+ios
export function execite(projectId, groupId, taskId, caseId, udid, hubUrl, automationName, versionplatform, serialId, author) {
  return request({
    url: '/boss/appautoCase/execute',
    method: 'post',
    params: {
      projectId,
      groupId,
      taskId,
      caseId,
      udid,
      hubUrl,
      automationName,
      versionplatform,
      serialId,
      author
    }
  })
}
