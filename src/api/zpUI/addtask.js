import request from '@/utils/request'
import qs from 'qs'

//获取项目
export function getProject(projectId) {
  return request({
    url: '/boss/project/list',
    method: 'get',
    params: {
      projectId
    }
  })
}

//获取项目分组
export function getProjectGroup(projectId, type, pageIndex, pageSize) {
  return request({
    url: '/boss/interfaceGroup/list',
    method: 'get',
    params: {
      projectId,
      type,
      pageIndex,
      pageSize
    }
  })
}

//获取项目分组下的用例
export function getCaseByGroupId(groupIds) {
  return request({
    url: '/boss/appautoCase/list/byGroupId',
    method: 'get',
    params: {
      groupIds
    }
  })
}

//添加任务
export function addOrEditTask(taskForm) {
  return request({
    url: '/boss/task/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(taskForm)
  })
}

//编辑任务
export function EditTask(taskForm) {
  return request({
    url: '/boss/task/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(taskForm)
  })
}
