import request from '@/utils/request'
import qs from 'qs'

//添加用例
export function addCase(data) {
  return request({
    url: '/boss/appautoCase/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//批量导入测试用例
export function exportCases(caseid1, caseid2) {
  return request({
    url: '/boss/appautoCase/addCaseStep',
    method: 'get',
    params: {
      caseid1,
      caseid2
    }
  })
}

//编辑用例
export function editCase(data) {
  return request({
    url: '/boss/appautoCase/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取项目分组
export function getProjectGroup5(projectId, type) {
  return request({
    url: '/boss/interfaceGroup/list',
    method: 'get',
    params: {
      projectId,
      type
    }
  })
}

//项目分组--分页
export function getProjectGroup2(projectId, type, pageIndex, pageSize) {
  return request({
    url: '/boss/interfaceGroup/list',
    method: 'get',
    params: {
      projectId,
      type,
      pageIndex,
      pageSize
    }
  })
}

//获取用例
export function getBatchCase(projectId, groupId, envId, id, pageIndex, pageSize, version) {
  return request({
    url: '/boss/appautoCase/list',
    method: 'get',
    params: {
      projectId,
      groupId,
      envId,
      id,
      pageIndex,
      pageSize,
      version
    }
  })
}

//删除用例
export function deleteAutoCase(id) {
  return request({
    url: '/boss/appautoCase/delete',
    method: 'get',
    params: {
      id
    }
  })
}
