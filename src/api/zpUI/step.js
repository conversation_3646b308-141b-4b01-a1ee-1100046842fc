import request from '@/utils/request'
import qs from 'qs'

//获取用例信息
export function addOrEditCaseStep(type, data) {
  return request({
    url: '/boss/appautoCaseStep/' + type,
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//插入用例步骤
export function insertCaseStep(id, pageIndex, pageSize) {
  return request({
    url: '/boss/appautoCaseStep/list',
    method: 'get',
    params: {
      id,
      pageIndex,
      pageSize
    }
  })
}

//删除步骤
export function deleteAutoCaseStep(id) {
  return request({
    url: '/boss/appautoCaseStep/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//更换step顺序
export function moveUp(frontId, afterId) {
  return request({
    url: '/boss/appautoCaseStep/switch',
    method: 'post',
    params: {
      frontId,
      afterId
    }
  })
}
