import request from '@/utils/request'
import qs from 'qs'

//获取用例信息
export function getAutoCase(projectId, groupId, envId, id, pageIndex, pageSize) {
  return request({
    url: '/boss/appautoCase/list',
    method: 'get',
    params: {
      projectId,
      groupId,
      envId,
      id,
      pageIndex,
      pageSize
    }
  })
}

//获取单用例下所有步骤
export function getAutoCaseStep(caseId, id, pageIndex, pageSize) {
  return request({
    url: '/boss/appautoCaseStep/list',
    method: 'get',
    params: {
      caseId,
      id,
      pageIndex,
      pageSize
    }
  })
}

//开始执行任务--android+ios
export function executeAppAutoCase(projectId, groupId, caseId, id, hubUrl, versionplatform, udid, automationName, serialId, author, taskId) {
  return request({
    url: '/boss/appautoCase/execute',
    method: 'post',
    timeout: 1000000,
    params: {
      projectId,
      groupId,
      caseId,
      id,
      hubUrl,
      udid,
      automationName,
      versionplatform,
      serialId,
      author,
      taskId
    }
  })
}

//编辑步骤
export function editAutoCaseStep(id, pageIndex, pageSize) {
  return request({
    url: '/boss/appautoCaseStep/list',
    method: 'get',
    params: {
      id,
      pageIndex,
      pageSize
    }
  })
}
