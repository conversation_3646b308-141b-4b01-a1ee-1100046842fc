import request from '@/utils/request'
import qs from 'qs'

//获取执行日志
export function getExectedLog(projectId, taskId, startTime, endTime, pageIndex, pageSize) {
  return request({
    url: '/boss/appautoCaseLog/list/log',
    method: 'get',
    params: {
      projectId,
      taskId,
      startTime,
      endTime,
      pageIndex,
      pageSize
    }
  })
}

//关键词搜索
export function getSearchLog(keyword) {
  return request({
    url: '/boss/appautoCaseLog/search',
    method: 'get',
    params: {
      keyword
    }
  })
}

//发送邮件
export function sendMail(url, mail, name, describe, serialId) {
  return request({
    url: '/boss/mail/sendappreport',
    method: 'post',
    params: {
      url,
      mail,
      name,
      describe,
      serialId
    }
  })
}

//发送邮件副本
export function sendMail2(url, mail, describe, serialId, executeTime, platform) {
  return request({
    url: '/boss/mail/sendparamreport',
    method: 'post',
    params: {
      url,
      mail,
      describe,
      serialId,
      executeTime,
      platform
    }
  })
}

//获取用例执行日志
export function getCaseExecuteLogList(taskId, serialId, pageIndex, pageSize) {
  return request({
    url: '/boss/appautoCaseLog/list',
    method: 'get',
    params: {
      taskId,
      serialId,
      pageIndex,
      pageSize
    }
  })
}

//参数校验
export function checkParma(serialId) {
  return request({
    url: '/boss/appautoCaseLog/getTime',
    method: 'get',
    params: {
      serialId
    }
  })
}

//开始校验
export function startCheckParam(startTime, endTime, userId, executeTime, author, platform) {
  return request({
    url: '/boss/appchecklog/getLog',
    method: 'get',
    params: {
      startTime,
      endTime,
      userId,
      executeTime,
      author,
      platform
    }
  })
}

//查看报告--参数校验
export function getReport(executeTime) {
  return request({
    url: '/boss/appCheckReport/getList',
    method: 'get',
    params: {
      executeTime
    }
  })
}

//获取case信息--参数校验
export function getCaseReport(serialId) {
  return request({
    url: '/boss/appCheckReport/getCase',
    method: 'get',
    params: {
      serialId
    }
  })
}

//获取报告整体信息
export function getReportDatail(executeTime) {
  return request({
    url: '/boss/appCheckReport/getReport',
    method: 'get',
    params: {
      executeTime
    }
  })
}
