import request from '@/utils/request'
import qs from 'qs'

//获取用例执行日志
export function getCaseExecuteLogList(caseId, taskId, serialId, pageIndex, pageSize) {
  return request({
    url: '/boss/appautoCaseLog/list',
    method: 'get',
    params: {
      caseId,
      taskId,
      serialId,
      pageIndex,
      pageSize
    }
  })
}

//查询日志
export function getExecutionLogist(serialId, pageIndex, pageSize) {
  return request({
    url: '/boss/applog/detail/simpleList',
    method: 'get',
    params: {
      serialId,
      pageIndex,
      pageSize
    }
  })
}

//获取报告列表
export function getReportList(serialId) {
  return request({
    url: '/boss/applog/report/simple',
    method: 'get',
    params: {
      serialId
    }
  })
}
