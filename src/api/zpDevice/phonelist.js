import request from '@/utils/requestmobile'
import qs from 'qs'

//获取设备列表
export function getAndroidMobileList(data) {
  return request({
    // /boss/device
    url: '/boss/device/mobile/list',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//登陆admin
export function loginAdmin(json) {
  return request({
    url: '/boss/device/user/login',
    method: 'post',
    headers: { 'content-type': 'application/json;charset=UTF-8' },
    data: JSON.stringify(json)
  })
}

//使用设备
export function useMobile(mobile, token) {
  return request({
    url: `/boss/device/mobile/${mobile.id}/start`,
    method: 'get',
    headers: { token: token }
  })
}
