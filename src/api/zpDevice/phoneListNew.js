import request from '@/utils/requestmobileNew'
import qs from 'qs'

//获取设备列表
export function getMobileList() {
  return request({
    url: 'api/v1/devices?present=true',
    method: 'get'
  })
}

export function getMobileListNew(queryForm) {
  return request({
    url: 'boss/agent/get_devices',
    method: 'get',
    params: queryForm
  })
}

export function getMobileInfo(deviceId) {
  return request({
    url: 'boss/agent/get_device_info?deviceId=' + deviceId,
    method: 'get'
  })
}

export function updateDeviceUser(executed, deviceId, user) {
  return request({
    url: 'boss/agent/update_device_user?executed=' + executed + '&deviceId=' + deviceId + '&user=' + user,
    method: 'get'
  })
}

//使用设备
export function useMobile(mobile) {
  return request({
    url: `/api/v1/user/devices/${mobile}`,
    method: 'get'
  })
}

export function activeMobile(mobile) {
  return request({
    url: `/api/v1/user/devices/${mobile}` + '/active',
    method: 'get'
  })
}

export function setMobile() {
  return request({
    url: '/api/v1/user/settings',
    method: 'get'
  })
}

export function stopMobile(mobile) {
  return request({
    url: `/api/v1/user/devices/${mobile}`,
    method: 'delete'
  })
}
