import request from '@/utils/requestagent'
import qs from 'qs'

//获取输入法列表
export function fetchImeList(mobileId) {
  return request({
    url: '/boss/agent/android/' + mobileId + '/imeList',
    method: 'get'
  })
}

//切换输入法
export function changeInputting(mobileId, ime) {
  return request({
    url: '/boss/agent/android/' + mobileId + '/ime?' + 'ime=' + ime,
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

//截图
export function Screencap(mobileId) {
  return request({
    url: '/boss/agent/device/' + mobileId + '/screenshot',
    method: 'get',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

//安装app
export function installApp(mobileId, data) {
  return request({
    url: '/boss/agent/mobile/' + mobileId + '/installApp',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: qs.stringify(data)
  })
}
