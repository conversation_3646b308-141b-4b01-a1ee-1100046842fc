import request from '@/utils/requestPerformance'
import qs from 'qs'

//获取cpu数据
export function getCpu(param) {
  return request({
    url: '/boss/agent/apm/cpu',
    method: 'get',
    params: param
  })
}

//获取内存数据
export function getMem(param) {
  return request({
    url: '/boss/agent/apm/mem',
    method: 'get',
    params: param
  })
}

//获取网络数据
export function getNetwork(param) {
  return request({
    url: '/boss/agent/apm/network',
    method: 'get',
    params: param
  })
}

//获取fps数据
export function getFps(param) {
  return request({
    url: '/boss/agent/apm/fps',
    method: 'get',
    params: param
  })
}

//获取网络数据
export function getBattery(param) {
  return request({
    url: '/boss/agent/apm/battery',
    method: 'get',
    params: param
  })
}
