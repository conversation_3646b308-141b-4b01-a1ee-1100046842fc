import request from '@/utils/request'
import qs from 'qs'
import axios from 'axios'
import store from '@/store'

export const baseApi = ['/prod-api', '/dev-api']
export let atsEnv = 0 //环境切换开关 0是线上环境，1是测试环境
export let atsMode = 2 //1直接请求ats-tools接口，模式2请求platform-server接口
export const atsHost = ['https://qa.kanzhun-inc.com', 'http://*************:9527']
/*
 *常量conditionMode含义
 * 0：线上环境+ats-tools接口
 * 1：线上环境+platform-server接口（提交代码时候，需要设为1）
 * 2：测试环境+ats-tools接口
 * 3：测试环境++platform-server接口
 * */
export const conditionMode = 1
const config = {
  0: { env: 0, mode: 1 },
  1: { env: 0, mode: 2 },
  2: { env: 1, mode: 1 },
  3: { env: 1, mode: 2 }
}
if (config.hasOwnProperty(conditionMode)) {
  const { env, mode } = config[conditionMode]
  atsEnv = env
  atsMode = mode
}
export const atsUrl = atsMode === 1 ? ['/xzats/http/list', '/xzats/http/json'] : [baseApi[atsEnv] + '/xzats/http/list', baseApi[atsEnv] + '/xzats/http/json']

export function getTenant() {
  return request({
    url: '/xzats/tenant',
    method: 'get'
  })
}
export function getTenantUser(data) {
  return request({
    url: '/xzats/tenant/user',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data),
    timeout: 60 * 1000
  })
}

export function getProject(data) {
  return request({
    url: '/xzats/project',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data),
    timeout: 60 * 1000
  })
}

export function getOrgs(data) {
  return request({
    url: '/xzats/orgs',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data),
    timeout: 60 * 1000
  })
}

export function updateVolunteer(data) {
  return request({
    url: '/xzats/updateVolunteer',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data),
    timeout: 60 * 1000
  })
}

export function commonRequest(data) {
  return request({
    url: '/boss/request/common',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data),
    timeout: 60 * 1000
  })
}

export function sendPostRequest(address, fds, select) {
  let data = ''
  for (let [key, value] of fds.entries()) {
    data += `${encodeURIComponent(key)}=${encodeURIComponent(value)}&`
  }
  data = data.slice(0, -1)
  let fd = new FormData()
  fd.append('url', address)
  fd.append('data', data)
  let httpUrl = ''
  if (select == 'list') {
    httpUrl = atsUrl[0]
  } else {
    httpUrl = atsUrl[1]
  }
  return axios
    .post(httpUrl, fd)
    .then(response => {
      var res = response.data
      if (res.code === 50008 || res.code === 50012 || res.code === 50014 || res.code === 401) {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
        return
      }
      return response.data
    })
    .catch(error => {
      // 处理请求失败的情况
      console.error(error)
      throw error
    })
}

export function sendList(url, data, select, option) {
  return sendPostRequest(url, data, select)
    .then(response => {
      for (var resKey in response) {
        var idAndName = {}
        idAndName['value'] = response[resKey]['id']
        idAndName['label'] = response[resKey]['name']
        option.push(idAndName)
      }
      // 处理响应数据
    })
    .catch(error => {
      console.error(error)
      // 处理错误信息
    })
}
