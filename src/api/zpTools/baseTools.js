import request from '@/utils/request'
import qs from 'qs'

export function getCodeByDubbo(data) {
  return request({
    url: '/boss/tools/dubbo/get_code',
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

export function getAtsCodeByDubbo(data) {
  return request({
    url: '/boss/tools/dubbo/get_ats_code',
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

export function getPhoneByDubbo(data) {
  return request({
    url: '/boss/tools/dubbo/get_phone_by_uid',
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

export function getUidByDubbo(data) {
  return request({
    url: '/boss/tools/dubbo/get_uid_by_phone',
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

export function cleanCache(data) {
  return request({
    url: '/boss/tools/dubbo/clean_cache',
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

export function getCodeCount(data) {
  return request({
    url: '/boss/tools/dubbo/get_code_count',
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

export function switchIdentity(data) {
  return request({
    url: '/boss/tools/dubbo/switch_identity',
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

//获取牛人基本信息
export function getGeekInfo(envId, client, phone, userId) {
  return request({
    url: '/boss/tools/getGeekInfo',
    method: 'get',
    params: {
      envId,
      userId,
      client,
      phone
    }
  })
}

//获取BOSS基本信息
export function getBossInfo(envId, client, phone, userId) {
  return request({
    url: '/boss/tools/getBossInfo',
    method: 'get',
    params: {
      envId,
      userId,
      client,
      phone
    }
  })
}

//根据uid查询对应的openid
export function getByUserId(envId, appId, userId) {
  return request({
    url: '/boss/tools/getOpenIdByUserId',
    method: 'get',
    params: {
      envId,
      appId,
      userId
    }
  })
}

//根据openid查询对应的uid
export function getByOpenId(envId, appId, openId) {
  return request({
    url: '/boss/tools/getUserIdByOpenId',
    method: 'get',
    params: {
      envId,
      appId,
      openId
    }
  })
}
