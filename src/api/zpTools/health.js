import request from '@/utils/request'
import qs from 'qs'

export function getHealthLog(env, zhipinName, status, pageIndex, pageSize) {
  return request({
    url: '/boss/enspect/list',
    method: 'get',
    params: {
      env,
      zhipinName,
      status,
      pageIndex,
      pageSize
    }
  })
}

export function executeHealthUrlZhipin(data) {
  return request({
    url: '/boss/enspect/execute',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
