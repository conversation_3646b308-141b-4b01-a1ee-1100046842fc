import request from '@/utils/requestqualityonline'
import qs from 'qs'

//首页
export function onlineBossKpiDeveloperBugStatusByEmail(email) {
  return request({
    url: '/quality/online/boss/kpi/developer/bugStatusByEmail?startTime=2024-1-1&projectId=&version=&email=' + email + '&type=1',
    method: 'get',
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
      'Access-Control-Allow-Origin': '*'
    }
  })
}

//首页
export function onlineBossKpiDeveloperListV5(email) {
  return request({
    url: '/quality/online/boss/kpi/developer/listV5?startTime=2024-1-1&endTime=&projectId=&version=&type=1&email=' + email,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//首页
export function onlineJiraTaskGet(email, status) {
  return request({
    url: '/quality/online/jira/task/get?account=' + email + '&status=' + status,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-Bug&Task
export function onlineBossKpiDeveloperQueryBugCategory(email, kpiSelectList) {
  return request({
    url: '/quality/online/boss/kpi/developer/querybugCategory?email=' + email + '&version=' + kpiSelectList.param.version + '&startTime=' + kpiSelectList.param.startTime + '&endTime=' + kpiSelectList.param.endTime,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-Bug&Task
export function onlineBossKpiDeveloperQueryTesterByDeveloper(email, kpiSelectList) {
  return request({
    url: '/quality/online/boss/kpi/developer/queryTesterByDeveloper?email=' + email + '&version=' + kpiSelectList.param.version + '&startTime=' + kpiSelectList.param.startTime + '&endTime=' + kpiSelectList.param.endTime,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-Bug&Task
export function onlineBossKpiDeveloperQueryDeveloperByTester(email, kpiSelectList) {
  return request({
    url: '/quality/online/boss/kpi/developer/queryDeveloperByTester?email=' + email + '&version=' + kpiSelectList.param.version + '&startTime=' + kpiSelectList.param.startTime + '&endTime=' + kpiSelectList.param.endTime,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-Bug&Task
export function onlineBossKpiDeveloperQueryBugCategoryWithProjectID(vname, kpiSelectList, versionList) {
  return request({
    url: '/quality/online/boss/kpi/developer/querybugCategory?version=' + vname + '&startTime=' + kpiSelectList.param.startTime + '&endTime=' + kpiSelectList.param.endTime + '&type=2' + '&projectId=' + versionList.projectIds,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-Bug&Task
export function onlineBossKpiDeveloperQueryBugCategoryWithType(email, kpiSelectList) {
  return request({
    url: '/quality/online/boss/kpi/developer/querybugCategory?email=' + email + '&version=' + kpiSelectList.param.version + '&startTime=' + kpiSelectList.param.startTime + '&endTime=' + kpiSelectList.param.endTime + '&type=1',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-Bug&Task
export function onlineBossKpiDeveloperQueryBugDetailByVersion(vname, kpiSelectList, versionList, statisticsType) {
  return request({
    url: '/quality/online/boss/kpi/developer/queryBugDetailByVersion?version=' + vname + '&startTime=' + kpiSelectList.param.startTime + '&endTime=' + kpiSelectList.param.endTime + '&projectId=' + versionList.projectIds + '&statisticsType=' + statisticsType,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-Bug&Task
export function onlineBossKpiDeveloperListV2(startTime, endTime, group, version, type, projectId) {
  return request({
    url: '/quality/online/boss/kpi/developer/listV2?startTime=' + startTime + '&endTime=' + endTime + '&group=' + group + '&version=' + version + '&type=' + type + '&projectId=' + projectId,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-Bug&Task
export function onlineBossKpiDeveloperListV4(versionList) {
  return request({
    url: '/quality/online/boss/kpi/developer/listV4?projectIds=' + versionList.projectIds,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-Bug&Task
export function onlineBossKpiDeveloperListV5WithGroup(startTime, endTime, group, type) {
  return request({
    url: '/quality/online/boss/kpi/developer/listV5?startTime=' + startTime + '&endTime=' + endTime + '&group=' + group + '&type=' + type,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-Bug&Task
export function onlineOaUpdateUserInfo() {
  return request({
    url: '/quality/online/oa/update/userInfo?',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    timeout: 300 * 1000
  })
}

//测试管理-用户反馈
export function onlineBossKpiv2FeedbackList(startTime, endTime, seq) {
  return request({
    url: '/quality/online/boss/kpiv2/feedback/list?startTime=' + startTime + '&endTime=' + endTime + '&seq=' + seq,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    timeout: 300 * 1000
  })
}

//测试管理-用户反馈
export function onlineBossKpiv2FeedbackListWithDeptName(startTime, endTime, deptName, seq) {
  return request({
    url: '/quality/online/boss/kpiv2/feedback/listV2?startTime=' + startTime + '&endTime=' + endTime + '&deptName=' + deptName + '&seq=' + seq,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-用户反馈
export function onlineBossKpiv2FeedbackListV2ByReporter(startTime, endTime, deptName, seq) {
  return request({
    url: '/quality/online/boss/kpiv2/feedback/listV2ByReporter?startTime=' + startTime + '&endTime=' + endTime + '&deptName=' + deptName + '&seq=' + seq,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试报告-问题分布（按组）
export function onlineBossKpiDeveloperListV2WithoutProjectId(startTime, endTime, group, version, type) {
  return request({
    url: '/quality/online/boss/kpi/developer/listV2?startTime=' + startTime + '&endTime=' + endTime + '&group=' + group + '&version=' + version + '&type=' + type,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-测试报告-测试进度
export function onlineBossKpiDeveloperListV10(projectIds) {
  return request({
    url: '/quality/online/boss/kpi/developer/listV10?projectIds=' + projectIds,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-测试报告-问题分布（按项目）
export function onlineBossKpiDeveloperListV6(startTime, endTime, projectId, version, type) {
  return request({
    url: '/quality/online/boss/kpi/developer/listV6?startTime=' + startTime + '&endTime=' + endTime + '&projectId=' + projectId + '&version=' + version + '&type=' + type,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-测试报告-问题分布（按项目）
export function onlineBossKpiDeveloperListV7(startTime, endTime, projectId, version) {
  return request({
    url: '/quality/online/boss/kpi/developer/listV7?startTime=' + startTime + '&endTime=' + endTime + '&projectId=' + projectId + '&version=' + version,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-测试报告-问题分布（按项目）
export function onlineBossKpiDeveloperListV8(solveStatus, projectId, version, type, startTime, endTime) {
  return request({
    url: '/quality/online/boss/kpi/developer/listV8?resolution=' + solveStatus + '&projectId=' + projectId + '&version=' + version + '&type=' + type + '&startTime=' + startTime + '&endTime=' + endTime,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-测试报告-问题分布（按项目）
export function onlineBossKpiDeveloperListV9(projectId, version, startTime, endTime) {
  return request({
    url: '/quality/online/boss/kpi/developer/listV9?projectId=' + projectId + '&version=' + version + '&startTime=' + startTime + '&endTime=' + endTime,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//测试管理-目标成绩
export function onlineBossKpiListV2WithGroup(startTime, endTime, kpiSelectList) {
  return request({
    url: '/quality/online/boss/kpi/listV2?startTime=' + startTime + '&endTime=' + endTime + '&group=' + kpiSelectList.param.group,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-故障统计-列表查询
export function qualityOnlineFaultStatisticsList(startTime, endTime, group) {
  return request({
    url: '/quality/online/fault/statistics/list?startTime=' + startTime + '&endTime=' + endTime + '&group=' + group,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-故障统计-详情-按照人查询故障子类型
export function qualityOnlineFaultStatisticsSubtypeCountByName(startTime, endTime, name) {
  return request({
    url: '/quality/online/fault/statistics/subtypeCountByName?startTime=' + startTime + '&endTime=' + endTime + '&name=' + name,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-Bug按RD组分布-查询
export function qualityOnlineBossKpiDeveloperQueryBugCountByGroup(startTime, endTime, group) {
  return request({
    url: '/quality/online/boss/kpi/developer/queryBugCountByGroup?startTime=' + startTime + '&endTime=' + endTime + '&group=' + group,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}
