import request from '@/utils/request'
import qs from 'qs'

export function getJiraList() {
  return request({
    url: '/py_server/users',
    method: 'get'
  })
}

//Boss管家—审核项目单
export function examineProject(projectName) {
  return request({
    url: '/py_server/boss_guanjia/examine_project?project_name=' + projectName,
    method: 'get'
  })
}

//Boss管家—牛人喂奶
export function createNurseData(project_name, geekid) {
  return request({
    url: '/py_server/boss_guanjia/create_nurse_data?project_name=' + project_name + '&geekid=' + geekid,
    method: 'get'
  })
}

export function addClueCallLog(agent_phone, geekname) {
  return request({
    url: '/py_server/boss_guanjia/add_clue_call_log?agent_phone=' + agent_phone + '&geekname=' + geekname,
    method: 'get'
  })
}

export function changeEffectiveHelp() {
  return request({
    url: '/py_server/boss_guanjia/change_effective_help',
    method: 'get'
  })
}

export function createLattestHelp(clue_id, times) {
  return request({
    url: '/py_server/boss_guanjia/create_lattest_help?clue_id=' + clue_id + '&times=' + times,
    method: 'get'
  })
}

export function copyLattestHelp(num) {
  return request({
    url: '/py_server/boss_guanjia/copy_lattest_help?num=' + num,
    method: 'get'
  })
}

export function getAdminCookie(account) {
  return request({
    url: '/py_server/boss_guanjia/get_admin_cookie?account=' + account,
    method: 'get'
  })
}

export function getAgentCookie(phone) {
  return request({
    url: '/py_server/boss_guanjia/get_agent_cookie?phone=' + phone,
    method: 'get'
  })
}

export function cleanClueAgent(clueId) {
  return request({
    url: '/py_server/boss_guanjia/clean_clue_agent?clue_id=' + clueId,
    method: 'get'
  })
}

export function save_tools_cookie({ account, domain, cookie, environment, system_name, check_cookie_url, author, notice_email, send_bossHi_notice }) {
  return request({
    headers: { 'Content-Type': 'application/json' },
    url: '/py_server/common/save_tools_cookie',
    method: 'post',
    data: { account, domain, cookie, environment, system_name, check_cookie_url, author, notice_email, send_bossHi_notice }
  })
}

export function get_tools_encryption_cookie(id, account, domain, environment, system_name, author, notice_email, send_bossHi_notice, pageNum = 1, pageSize = 20) {
  return request({
    url: '/py_server/common/get_tools_encryption_cookie?id=' + id + '&account=' + account + '&domain=' + domain + '&environment=' + environment + '&system_name=' + system_name + '&author=' + author + '&notice_email=' + notice_email + '&send_bossHi_notice=' + send_bossHi_notice + '&pageNum=' + pageNum + '&pageSize=' + pageSize,
    method: 'get'
  })
}

export function delete_tools_cookie(id) {
  return request({
    url: '/py_server/common/delete_tools_cookie?id=' + id,
    method: 'get'
  })
}

export function check_cookie_useable({ cookie, check_cookie_url }) {
  return request({
    headers: { 'Content-Type': 'application/json' },
    url: '/py_server/common/check_cookie_useable',
    method: 'post',
    data: { cookie, check_cookie_url }
  })
}

export function get_job_url(project_name, boss_phone, boss_id) {
  return request({
    url: '/py_server/boss_guanjia/get_jobs_url?project_name=' + project_name + '&boss_phone=' + boss_phone + '&boss_id=' + boss_id,
    method: 'get'
  })
}

export function get_jobs_url(job_id, boss_phone, boss_id) {
  return request({
    url: '/py_server/boss_zhipin/get_jobs_url?job_id=' + job_id + '&boss_phone=' + boss_phone + '&boss_id=' + boss_id,
    method: 'get'
  })
}

export function create_call_clue(phone, cule_type) {
  return request({
    url: '/py_server/boss_guanjia/create_call_clue?phone=' + phone + '&cule_type=' + cule_type,
    method: 'get'
  })
}

export function get_userInfo_by_chatClueId(chatClueId) {
  return request({
    url: '/py_server/boss_guanjia/getUserInfoByChatClueId?chatClueId=' + chatClueId,
    method: 'get'
  })
}

export function quicklyRegisterGeek(phone, type, name, freshGraduate) {
  return request({
    url: '/py_server/boss_zhipin/quicklyRegisterGeek?phone=' + phone + '&type=' + type + '&name=' + name + '&freshGraduate=' + freshGraduate,
    method: 'get'
  })
}

export function quicklyRegisterBoss(phone, comName, coo, token_iuc) {
  return request({
    url: '/py_server/boss_zhipin/quicklyRegisterBoss?phone=' + phone + '&comName=' + comName + '&coo=' + coo + '&token_iuc=' + token_iuc,
    method: 'get'
  })
}

export function uploadFileByOss(data) {
  return request({
    url: '/py_server/common/file_upload',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

export function sendStringMessage(data) {
  return request({
    url: '/py_server/common/string_message',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

export function getUploadHistory(email, limit) {
  return request({
    url: '/py_server/common/recent_uploads?email=' + email + '&limit=' + limit,
    method: 'get'
  })
}

export function deleteUploadRecord(recordId) {
  return request({
    url: '/py_server/common/delete_upload_record?record_id=' + recordId,
    method: 'get'
  })
}

export function sendBossHiMessage(email, markdown) {
  return request({
    url: '/py_server/common/send_hi_message?email=' + email + '&markdown=' + markdown,
    method: 'get'
  })
}

export function getBossHiPhoneVerifyCode(phone) {
  return request({
    url: '/py_server/getBossHiPhoneVerifyCode?phone=' + phone,
    method: 'get'
  })
}

export function getBaizeAllScene(sence_name) {
  return request({
    url: '/py_server/baize/getAllScene?sence_name=' + sence_name,
    method: 'get'
  })
}

export function getSensitiveWords(sence, word) {
  return request({
    url: '/py_server/baize/getSensitiveWords?scene=' + sence + '&word=' + word,
    method: 'get'
  })
}

export function getAllLocation() {
  return request({
    url: '/py_server/baize/getAllLocation',
    method: 'get'
  })
}

export function getSensitiveWordsQa(sence, word) {
  return request({
    url: '/py_server/baize/getSensitiveWordsQa?scene=' + sence + '&word=' + word,
    method: 'get'
  })
}

export function addBaizeMessage({ key, value }) {
  return request({
    url: '/py_server/baize/addBaizeMessage',
    method: 'post',
    data: { key, value },
    headers: { 'Content-Type': 'application/json' }
  })
}

export function reportBoss({ phone }) {
  return request({
    url: '/py_server/baize/reportBoss',
    method: 'post',
    data: { phone },
    headers: { 'Content-Type': 'application/json' }
  })
}

export function reportGeek(report, reported, token) {
  return request({
    url: '/py_server/baize/reportGeek?report_phone=' + report + '&reported_phone=' + reported + '&test_token=' + token,
    method: 'get'
  })
}

export function reportGeekV2({ report_phone, reported_phone }) {
  return request({
    url: 'py_server/baize/reportGeekV2',
    method: 'post',
    data: { report_phone, reported_phone },
    headers: { 'Content-Type': 'application/json' }
  })
}

export function decryptEncrypt(value, action) {
  return request({
    url: '/py_server/washington/decryptEncrypt?value=' + value + '&action=' + action,
    method: 'get'
  })
}

//noah
export function runtonode(name, phone, nodeF, node, jointype) {
  return request({
    url: '/py_server/noah/runtonode?name=' + name + '&phone=' + phone + '&nodeF=' + nodeF + '&node=' + node + '&jointype=' + jointype,
    method: 'get'
  })
}

export function batchAddF1TopCard(data) {
  return request({
    url: '/py_server/batchF1TopCardAdd',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'application/json' }
  })
}

export function batchDeleteF1TopCard(data) {
  return request({
    url: '/py_server/batchF1TopCardDel',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'application/json' }
  })
}
