/*
 * @Author: dq <EMAIL>
 * @Date: 2024-11-25 10:16:55
 * @LastEditors: dq <EMAIL>
 * @LastEditTime: 2025-04-09 01:12:09
 * @FilePath: /platform-boss-front/src/api/zpTools/yolandaPlus.js
 */
import request from '@/utils/request'
import qs from 'qs'
import axios from 'axios'
import { getInfo, getInfoIuc } from '@/api/common/user'
import Cookies from 'js-cookie'
import { getToken } from '@/utils/auth'
import { sendRequest } from '@/api/zpTools/tools'
import { env } from 'echarts'

//快速审批
export function ProcessAudit(processId) {
  return request({
    url: 'py_server/processAudit',
    method: 'get',
    params: {
      processId
    }
  })
}

export function GetBankStatementSearch(env) {
  return request({
    url: 'py_server/getBankStatement',
    method: 'get',
    params: {
      env
    }
  })
}

export function GetProcess(order_no) {
  return request({
    url: 'py_server/processGet',
    method: 'get',
    params: {
      order_no
    }
  })
}

export function BankRefund(order_no, env, coo) {
  return request({
    url: '/py_server/bankRefund?order_no=' + order_no + '&env=' + env + '&coo=' + coo,
    method: 'get'
  })
}
