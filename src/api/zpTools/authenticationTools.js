import request from '@/utils/request'
import qs from 'qs'

//认证工具-用户状态-获取Boss直聘用户极值限制记录
export function getLimitRecord(userId, env) {
  return request({
    url: '/tools/status/limit/record?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-用户状态-获取Boss直聘用户boss身份状态
export function getBossStatus(userId, env) {
  return request({
    url: '/tools/status/bossget?bossid=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-用户状态-获取Boss直聘用户boss身份状态
export function getGeekStatus(userId, env) {
  return request({
    url: '/tools/status/geekget?geekid=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-用户状态-获取店长用户boss身份状态
export function getDianZhangBossStatus(userId, env) {
  return request({
    url: '/tools/status/dianzhang/boss/status?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-用户状态-获取店长用户牛人身份状态
export function getDianZhangGeekStatus(userId, env) {
  return request({
    url: '/tools/status/dianzhang/geek/status?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-解绑认证邮箱
export function unBoundEmail(env, db, sql) {
  return request({
    url: '/tools/data/query?env=' + env + '&db=' + db + '&sql=' + sql,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-删除用户告知
export function delSecurityPrivacy(phone, env) {
  return request({
    url: '/delprivacy?userId=' + phone + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-同步公司数据
export function synchronizeCompany(comName) {
  return request({
    url: '/synchronizeCompany?comName=' + comName,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-推送公司变更通知
export function synCompanyChangeNotice(kgid) {
  return request({
    url: '/synCompanyChangeNotice?kgid=' + kgid,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-查询督察官数据
export function queryJ2(comName) {
  return request({
    url: '/queryJ2?comName=' + comName,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-腾讯云全集token清理
export function delTencentToken(data) {
  return request({
    url: '/boss/dubbo/doSendWithTelnet',
    method: 'post',
    data: qs.stringify(data),
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-IP解封-请求公共接口（域名/request/common）,不是域名/boss/request/common
export function requestCommon(data) {
  return request({
    url: '/request/common',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//数据统计-BUG&TASK-获取计划版本
export function toolsPlanGetVersion(projectType) {
  return request({
    url: '/tools/plan/get/version?projectType=' + projectType,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded', 'Access-Control-Allow-Origin': '*' }
  })
}

//数据统计-BUG&TASK-更新线下库公司人员信息
export function oaOfflineUpdateUserInfo() {
  return request({
    url: '/oa/update/userInfo',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded', 'Access-Control-Allow-Origin': '*' },
    timeout: 300 * 1000
  })
}

//数据统计-BUG&TASK-获取更新时间
export function oaGetUndateTime() {
  return request({
    url: '/oa/get/undateTime',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded', 'Access-Control-Allow-Origin': '*' }
  })
}

//数据统计-问题统计-查询
export function toolsStatisticsList(version, project, keyword, pageIndex, projectType) {
  return request({
    url: '/tools/statistics/list?version=' + version + '&project=' + project + '&name=' + keyword + '&pageIndex=' + pageIndex + '&pageSize=300' + '&projectType=' + projectType,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded', 'Access-Control-Allow-Origin': '*' }
  })
}

//数据统计-问题统计-获取版本
export function toolsStatisticsGetVersion(projectType) {
  return request({
    url: '/tools/statistics/get/version?projectType=' + projectType,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded', 'Access-Control-Allow-Origin': '*' }
  })
}

//数据统计-问题统计-更新
export function toolsStatisticsUpdate(data) {
  return request({
    url: '/tools/statistics/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//数据统计-问题统计-导入-上传文件到服务器
export function toolsPlanUploadFile(data) {
  return request({
    url: '/tools/plan/upload/file',
    method: 'post',
    headers: { 'content-type': 'multipart/form-data' },
    // data: qs.stringify(data)
    data: data
  })
}

//数据统计-问题统计-导入-导入数据
export function toolsStatisticsReadExcel(data) {
  return request({
    url: '/tools/statistics/read/excel',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//数据统计-问题统计-更新-更新wiki
export function toolsStatisticsUpdateWiki(data) {
  return request({
    url: '/tools/statistics/update/wiki',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//数据统计-问题统计-更新-更新bug数
export function toolsStatisticsUpdateWikiBug(data) {
  return request({
    url: '/tools/statistics/update/wikiBug',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data),
    timeout: 60 * 1000
    // data: data
  })
}

//数据统计-问题统计-更新-更新喜鹊
export function toolsStatisticsUpdateMagpie(data) {
  return request({
    url: '/tools/statistics/update/magpie',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//数据统计-问题统计-更新-更新喜鹊bug数
export function toolsStatisticsUpdateMagpieBug(data) {
  return request({
    url: '/tools/statistics/update/magpieBug',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data),
    timeout: 60 * 1000
    // data: data
  })
}

//数据统计-问题统计-删除
export function toolsStatisticsDelete(id) {
  return request({
    url: '/tools/statistics/delete?id=' + id,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-测试报告-查询
export function toolsPlanList(version, project, keyword, pageIndex, projectType, demandType) {
  return request({
    url: '/tools/plan/list?version=' + version + '&project=' + project + '&name=' + keyword + '&pageIndex=' + pageIndex + '&pageSize=500' + '&projectType=' + projectType + '&demandType=' + demandType,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-测试报告-更新
export function toolsPlanUpdate(data) {
  return request({
    url: '/tools/plan/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//数据统计-测试报告-删除
export function toolsPlanDelete(id) {
  return request({
    url: '/tools/plan/delete?id=' + id,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-测试报告-导入-导入数据
export function toolsPlanReadExcel(data) {
  return request({
    url: '/tools/plan/read/excel',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//数据统计-测试报告-更新-更新wiki
export function toolsPlanUpdateWiki(data) {
  return request({
    url: '/tools/plan/update/wiki',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//数据统计-测试报告-更新-更新wiki
export function toolsPlanUpdateMagpie(data) {
  return request({
    url: '/tools/plan/update/magpie',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//数据统计-用户反馈-获取分组
export function oaUserInfoGroup(seq) {
  return request({
    url: '/oa/userInfoGroup?seq=' + seq,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-业务责任人分配-搜索
export function bossModuleSearch(keyword, pageIndex, pageSize) {
  return request({
    url: '/boss/module/search?keyword=' + keyword + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-业务责任人分配-编辑
export function bossModuleList(id) {
  return request({
    url: '/boss/module/list?id=' + id,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-业务责任人分配-编辑-保存
export function bossModuleType(type, data) {
  return request({
    url: '/boss/module/' + type,
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//数据统计-业务责任人分配-删除
export function bossModuleDelete(id) {
  return request({
    url: '/boss/module/delete?id=' + id,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-业务责任人分配-更新排序
export function bossModuleSortUpdate(data) {
  return request({
    url: '/boss/module/sort/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//数据统计-测试机分配-检索
export function bossPhoneList(keyword, department, type, os) {
  return request({
    url: '/boss/phone/list?keyword=' + keyword + '&department=' + department + '&type=' + type + '&os=' + os,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-测试机分配-编辑
export function bossPhonePhoenById(id) {
  return request({
    url: '/boss/phone/phoenById?id=' + id,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-测试机分配-删除
export function bossPhoneDelete(id) {
  return request({
    url: '/boss/phone/delete?id=' + id,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-测试机分配-添加
export function bossPhoneType(type, data) {
  return request({
    url: '/boss/phone/' + type,
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//数据统计-认领测试手机号-查询
export function bossPhoneNumberList(keyword, pageIndex, pageSize) {
  return request({
    url: '/boss/phoneNumber/list?keyword=' + keyword + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-认领测试手机号-添加
export function bossPhoneNumberAdd(data) {
  return request({
    url: '/boss/phoneNumber/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//认证工具-设置cookie
export function setCookies(data) {
  return request({
    url: '/setCookies',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//认证工具-同步公司数据
export function wisdomStonePushAsk(taskId, userId) {
  return request({
    url: '/wisdomStonePushAsk?taskId=' + taskId + '&userId=' + userId,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-检查公司关联关系-校验是否注册过该商标
export function checkComTrademark(comName, brandName, sceneType) {
  return request({
    url: '/checkComRelation?comName=' + comName + '&brandName=' + brandName + '&sceneType=' + sceneType,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-检查公司关联关系-检查一个公司和其他一堆公司是否有关联
export function checkComRelation(comName, comNames, sceneType) {
  return request({
    url: '/checkComRelation?comName=' + comName + '&comNames=' + comNames + '&sceneType=' + sceneType,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//认证工具-检查公司关联关系-自动审核校验CFCB
export function checkAutoCfcb(comName, brandName, sceneType) {
  return request({
    url: '/checkComRelation?comName=' + comName + '&brandName=' + brandName + '&sceneType=' + sceneType,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//业务工具-店长工具-清除验证码次数
export function clearVerificationCode(phone, env) {
  return request({
    url: '/dianzhang/clearVerificationCode?phone=' + phone + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//业务工具-特殊号段认证-删除人脸认证
export function delTruemanCertFun(data) {
  return request({
    url: '/delTruemanCert',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//业务工具-特殊号段认证-清除更换次数
export function delComChangeTimesFun(userId, env) {
  return request({
    url: '/delComChangeTimes',
    method: 'get',
    params: {
      userId,
      env
    },
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//业务工具-特殊号段认证-通过企业环境认证
export function envcertificationPassFun(data) {
  return request({
    url: '/envCertificationPass',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//商业工具-阻断商品退还
export function blockCommodityReturn(userId, env) {
  return request({
    url: '/blockCommodityReturn?bossId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//商业工具-作废道具
export function cancelAllItem(userId, env) {
  return request({
    url: '/cancelAllItem?bossId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//业务工具-特殊号段认证-加V
export function bossToolsDubboAddV(userId, env) {
  return request({
    url: '/boss/tools/dubbo/addV?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//业务工具-特殊号段认证-添加实名认证
export function bossToolsDubboAddTrueMan(userId, certName, certNo, env) {
  return request({
    url: '/boss/tools/dubbo/addTrueMan?userId=' + userId + '&certName=' + certName + '&certNo=' + certNo + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//业务工具-特殊号段认证-二次人脸解冻
export function bossToolsDubboAUnfreezeAllByForceFace(userId, env) {
  return request({
    url: '/boss/tools/dubbo/unfreezeAllByForceFace?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//业务工具-特殊号段认证-取消环境认证
export function bossToolsDubboACancelEnvironmentCert(userId, env) {
  return request({
    url: '/boss/tools/dubbo/cancelEnvironmentCert?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//业务工具-特殊号段认证-删除人脸认证
export function bossToolsDubboDelTrueManCert(userId, env) {
  return request({
    url: '/boss/tools/dubbo/delTrueManCert?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//业务工具-特殊号段认证-下发boss身份冻结
export function bossToolsDubboFreezeUser(userId, env) {
  return request({
    url: '/boss/tools/dubbo/freezeUser?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//业务工具-特殊号段认证-下发boss身份冻结
export function bossToolsDubboUnFreezeUser(userId, env) {
  return request({
    url: '/boss/tools/dubbo/unFreezeUser?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//业务工具-特殊号段认证-下发身份认证-短信
export function bossToolsDubboIdentityVerifyMessage(userId, env) {
  return request({
    url: '/boss/tools/dubbo/identityVerifyMessage?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//业务工具-特殊号段认证-取消身份认证
export function bossToolsDubboCancelIdentityVerify(userId, env) {
  return request({
    url: '/boss/tools/dubbo/cancelIdentityVerify?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//测试管理-数据统计-测试用例分数统计
export function testCaeAchievementGetScore(groups, startTime, endTime) {
  return request({
    url: '/testCaeAchievement/getScore?groups=' + groups + '&startTime=' + startTime + '&endTime=' + endTime,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//牛人管理-删除牛人必填信息
export function geekRequiredInfoUpdateInfo(userId, type, freshGraduateParam) {
  return request({
    url: '/geekRequiredInfo/updateInfo?userId=' + userId + '&type=' + type + '&freshGraduateParam=' + freshGraduateParam,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: qs.stringify(data)
  })
}

//业务工具-添加F1卡片置顶
export function toolsf1CardTopAdd(cardJsonParam, env) {
  return request({
    url: '/tools/f1CardTop/add',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: {
      cardJsonParam: cardJsonParam,
      env: env
    }
  })
}

//业务工具-F1卡片置顶查询
export function toolsf1CardTopQuery(cardJsonParam, env) {
  return request({
    url: '/tools/f1CardTop/query',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: {
      cardJsonParam: cardJsonParam,
      env: env
    }
  })
}

//业务工具-F1卡片置顶删除
export function toolsf1CardTopDelete(id, env) {
  return request({
    url: '/tools/f1CardTop/delete?id=' + id + '&env=' + env,
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//业务工具-测试牛人端账号注销
export function toolsDubboDestoryPhoneByDubbo(regionCode, phone, env) {
  return request({
    url: '/boss/tools/dubbo/destoryPhoneByDubbo?regionCode=' + regionCode + '&phone=' + phone + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//业务工具-测试BOSS端账号注销
export function toolsDubboDestoryForBossPhoneByDubbo(regionCode, phone, env) {
  return request({
    url: '/boss/tools/dubbo/destoryPhoneForBossByDubbo?regionCode=' + regionCode + '&phone=' + phone + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//业务工具-高搜指定牛人
export function toolsAdvancedSearchAdd(cardJsonParam, env, topType) {
  return request({
    url: '/tools/advancedSearch/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: {
      cardJsonParam: cardJsonParam,
      env: env,
      topType: topType
    }
  })
}

//牛人管理-刷新牛人简历卡片数据
export function toolsGeekFreshGeekCard(geekId) {
  return request({
    url: '/tools/geek/freshGeekCard?geekId=' + geekId,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//业务工具-特征修改
export function toolsCharacteristicModify(jsonParam) {
  return request({
    url: '/tools/characteristicModify',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: {
      jsonParam: jsonParam
    }
  })
}

//商业工具-微信提现明细
export function toolsBusinessWeChatWithdrawalDetails(textarea) {
  return request({
    url: '/tools/business/weChatWithdrawalDetails?openIds=' + textarea,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: {
    //     openIds: textarea
    // }
  })
}

//认证工具-清除认证缓存信息
export function toolsCertDelCertifyCache(key) {
  return request({
    url: '/tools/cert/delCertifyCache?key=' + key,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: {
    //     openIds: textarea
    // }
  })
}

//数据统计-APP管理列表-添加
export function bossToolsAppVersionManageTabulationAdd(data) {
  return request({
    url: '/boss/tools/appVersionManageTabulation/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//数据统计-APP管理列表-添加
export function bossToolsAppVersionManageTabulationType(type, name, data) {
  return request({
    url: '/boss/tools/appVersionManageTabulation/' + type + '?operateName=' + name,
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
    // data: data
  })
}

//数据统计-APP管理列表-查询
export function bossToolsAppVersionManageTabulationQuery(keyword, projectType, pageIndex, pageSize) {
  return request({
    url: '/boss/tools/appVersionManageTabulation/query?keyword=' + keyword + '&projectType=' + projectType + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-APP管理列表-编辑
export function bossToolsAppVersionManageTabulationUpdate(id) {
  return request({
    url: '/boss/tools/appVersionManageTabulation/update?id=' + id,
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-APP管理列表-通过id查询
export function bossToolsAppVersionManageTabulationSearchByID(id) {
  return request({
    url: '/boss/tools/appVersionManageTabulation/searchByID?id=' + id,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-APP管理列表-通过id删除
export function bossToolsAppVersionManageTabulationDeleteByID(id) {
  return request({
    url: '/boss/tools/appVersionManageTabulation/deleteByID?id=' + id,
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//数据统计-APP管理列表-日志-查询
export function bossToolsAppVersionManageTabulationSearchLogByID(id) {
  return request({
    url: '/boss/tools/appVersionManageTabulation/searchLogByID?id=' + id,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//其他工具-APM用户加密
export function toolsOtherApmUserEncryption(content, appKey, type) {
  return request({
    url: '/tools/other/apmUserEncryption?content=' + content + '&appKey=' + appKey + '&type=' + type,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//业务工具-设置账户人机验证
export function bossToolsDubboAccountHumanMachineVerification(type, phone, env) {
  return request({
    url: '/boss/tools/dubbo/accountHumanMachineVerification?type=' + type + '&account=' + phone + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//业务工具-特殊号段认证-取消本机号码设备认证
export function cancelDeviceAuthForLocalNumbersFun(data) {
  return request({
    url: '/cancelDeviceAuthForLocalNumbers',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//商业工具-缓存操作-读
export function cacheOperationGet(service, key) {
  return request({
    url: '/cacheOperation/get?service=' + service + '&key=' + key,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: {
    //     openIds: textarea
    // }
  })
}

//商业工具-缓存操作-写
export function cacheOperationSet(service, key, value) {
  return request({
    url: '/cacheOperation/set?service=' + service + '&key=' + key + '&value=' + value,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: {
    //     openIds: textarea
    // }
  })
}

//商业工具-缓存操作-删除
export function cacheOperationDelete(service, key) {
  return request({
    url: '/cacheOperation/delete?service=' + service + '&key=' + key,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: {
    //     openIds: textarea
    // }
  })
}

//业务工具-特殊号段认证-完全注销后删除用户数据
export function deleteUserIfHasCancelledByDubbo(userId, env) {
  return request({
    url: '/boss/tools/dubbo/deleteUserIfHasCancelledByDubbo?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
    // data: {
    //     openIds: textarea
    // }
  })
}

//反馈监测-反馈bug-添加
export function bossAicraftFeedbackBugAdd(data) {
  return request({
    url: '/boss/aicraft/feedbackBug/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//反馈监测-反馈bug-更新
export function bossAicraftFeedbackBugUpdate(data) {
  return request({
    url: '/boss/aicraft/feedbackBug/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//反馈监测-反馈bug-按照条件查询
export function bossAicraftFeedbackBugSearch(bugName, status, author, startSize, pageSize) {
  return request({
    url: '/boss/aicraft/feedbackBug/search',
    method: 'get',
    params: {
      bugName,
      status,
      author,
      startSize,
      pageSize
    }
  })
}

//反馈监测-反馈bug-按照id查询
export function bossAicraftFeedbackBugSearchById(id) {
  return request({
    url: '/boss/aicraft/feedbackBug/searchById',
    method: 'get',
    params: {
      id
    }
  })
}

//反馈监测-反馈bug-按照id删除
export function bossAicraftFeedbackBugDelete(id) {
  return request({
    url: '/boss/aicraft/feedbackBug/delete',
    method: 'post',
    params: {
      id
    }
  })
}

//数据统计-测试报告-喜鹊报告-获取版本
export function dubboBugStatisVersionListFromMagpieData(projectType) {
  return request({
    url: '/boss/tools/dubbo/bugStatisVersionListFromMagpieData',
    method: 'get',
    params: {
      projectType
    }
  })
}
//数据统计-测试报告-喜鹊报告-查询版本数据
export function dubboBugStatisDetailListFromMagpieData(recordId, recordTitle, projectType) {
  return request({
    url: '/boss/tools/dubbo/bugStatisDetailListFromMagpieData',
    method: 'get',
    params: {
      recordId,
      recordTitle,
      projectType
    }
  })
}

//测试报告（新）- 根据项目-获取版本数据
export function newTestReportVersionList(projectType) {
  return request({
    url: '/boss/tools/dubbo/newTestReport/versionList',
    method: 'get',
    params: {
      projectType
    }
  })
}

//数据统计-测试报告-喜鹊报告-查询版本数据
export function dubboNewTestReportBugReport(recordId, recordTitle, projectType, issueOwnershipfilter) {
  return request({
    url: '/boss/tools/dubbo/newTestReport/bugReport',
    method: 'get',
    params: {
      recordId,
      recordTitle,
      projectType,
      issueOwnershipfilter
    },
    paramsSerializer: function (params) {
      const searchParams = new URLSearchParams()
      for (const key in params) {
        if (Array.isArray(params[key])) {
          params[key].forEach(value => {
            searchParams.append(key, value)
          })
        } else {
          searchParams.append(key, params[key])
        }
      }
      return searchParams.toString()
    }
  })
}

//数据统计-测试报告-喜鹊报告-获取BOSS默认版本号
export function dubboNewTestReportGetDefaultVersion() {
  return request({
    url: '/boss/tools/dubbo/newTestReport/getDefaultVersion',
    method: 'get',
    params: {}
  })
}

//数据统计-测试报告-喜鹊报告-获取问题所属（Server、Android...）
export function dubboNewTestReportGetIssueOwnerShip() {
  return request({
    url: '/boss/tools/dubbo/newTestReport/getWorkItemFieldOptions',
    method: 'get',
    params: {}
  })
}

//商业工具-修改无法追溯时间
export function toolsBusinessModifyUntraceableTime(userId, payAmount) {
  return request({
    url: '/tools/business/modifyUntraceableTime',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
      userId: userId,
      payAmount: payAmount
    }
  })
}

//业务工具-特殊号段认证-下发BOSS身份人脸冻结
export function bossTrueManFreeze(data) {
  return request({
    url: '/bossTrueManFreeze',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
