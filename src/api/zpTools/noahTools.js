import request from '@/utils/request'

export function UserId(name) {
  return request({
    url: '/py_server/noah/change',
    method: 'get',
    params: { name },
    headers: { 'Content-Type': 'application/json' }
  })
}

export function LeaveUserId(name) {
  return request({
    url: '/py_server/noah/leave',
    method: 'get',
    params: { name },
    headers: { 'Content-Type': 'application/json' }
  })
}
