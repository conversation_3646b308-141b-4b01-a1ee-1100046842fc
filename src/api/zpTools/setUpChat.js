import request from '@/utils/request'
import qs from 'qs'

export function bossAddFriend(env, bossPhone, phone, count, chatType, name) {
  return request({
    url: 'boss/tools/createFriend/bossAddFriend',
    method: 'get',
    params: {
      env,
      bossPhone,
      phone,
      count,
      chatType,
      name
    },
    timeout: 20 * 1000
  })
}

export function geekAddFriend(env, geekPhone, phone, count, chatType, name) {
  return request({
    url: 'boss/tools/createFriend/geekAddFriend',
    method: 'get',
    params: {
      env,
      geekPhone,
      phone,
      count,
      chatType,
      name
    },
    timeout: 20 * 1000
  })
}

export function bossAddFriendMessage(env, bossPhone, phone, messageCount, name) {
  return request({
    url: 'boss/tools/createFriend/geekAddFriendMessage',
    method: 'get',
    params: {
      env,
      bossPhone,
      phone,
      messageCount,
      name
    },
    timeout: 20 * 1000
  })
}
