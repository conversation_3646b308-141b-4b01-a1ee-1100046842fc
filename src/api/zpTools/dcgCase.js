import request from '@/utils/request'

export function run(data) {
  return request({
    url: '/py_server/dcg/cases/run',
    method: 'post',
    data
  })
}

export function getList(params) {
  return request({
    url: '/py_server/dcg/cases/list',
    method: 'get',
    params
  })
}

export function getSearch(keyword) {
  return request({
    url: '/py_server/dcg/case/search',
    method: 'get',
    params: { keyword }
  })
}

export function getLogList(params) {
  return request({
    url: '/py_server/dcg/log/list',
    method: 'get',
    params
  })
}

export function detail(id) {
  return request({
    url: '/py_server/dcg/case',
    method: 'get',
    params: { id }
  })
}

export function insertParams(data) {
  return request({
    url: '/py_server/dcg/params',
    method: 'post',
    data
  })
}

export function updateParams(data) {
  return request({
    url: '/py_server/dcg/params',
    method: 'put',
    data
  })
}

export function deleteParams(id) {
  return request({
    url: '/py_server/dcg/params',
    method: 'delete',
    params: { id }
  })
}

export function getParmasList(params) {
  return request({
    url: '/py_server/dcg/params/list',
    method: 'get',
    params
  })
}
