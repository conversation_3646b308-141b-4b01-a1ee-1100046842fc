/**
 * CRM工具相关接口
 */
import request from '@/utils/request'
import qs from 'qs'

/**
 * 修改用户信息
 * @param user_name
 * @param env
 * @returns {*}
 * @constructor
 */
export function EditUserInfo({ user_name, env }) {
  return request({
    headers: { 'Content-Type': 'application/json' },
    url: '/py_server/crm_tools/editUserInfo',
    method: 'post',
    data: { user_name, env }
  })
}

export function crmQueryTools({ data, toolVal }) {
  return request({
    url: '/py_server/crm_tools/dailyTools',
    method: 'get',
    params: { data, tool_type: toolVal }
  })
}

export function getCustomer(companyName, operator) {
  return request({
    url: '/py_server/crm_tools/getCustomer?' + 'companyName=' + companyName + '&operateAccount=' + operator,
    method: 'get'
  })
}

export function getAllBossByCustomer(customer, operator) {
  return request({
    url: '/py_server/crm_tools/getAllBossByCustomer?customer=' + customer + '&operateAccount=' + operator,
    method: 'get'
  })
}

export function getProductsByCustomName(customName, operator) {
  return request({
    url: '/py_server/crm_tools/getProductsByCustomName?customName=' + customName + '&operateAccount=' + operator,
    method: 'get'
  })
}

export function createOrder(customName, goodsList, super_admin, serviceDateCount, isTryUse, operator) {
  return request({
    url: '/py_server/crm_tools/createOrder?customName=' + customName + '&goodsList=' + goodsList + '&super_admin=' + super_admin + '&serviceDateCount=' + serviceDateCount + '&isTryUse=' + isTryUse + '&operateAccount=' + operator,
    method: 'get'
  })
}

export function excuteAutoTestCrm() {
  return request({
    url: '/py_server/crm_tools/excuteAuto_Test_Crm',
    method: 'get'
  })
}

// 以下是预发/线上环境，CRM创建订单需调用的接口
//获取测试公司列表
export function getTestCompanyList() {
  return request({
    url: '/py_server/crm_tools/getTestCompanyList',
    method: 'get'
  })
}

//根据公司名称，获取可购买的道具信息
export function getProductsByCompanyName(companyName, operateEnvironment) {
  return request({
    url: '/py_server/crm_tools/getProductsByCompanyName?companyName=' + companyName + '&operateEnvironment=' + operateEnvironment,
    method: 'get'
  })
}

//根据公司名称，创建报价单
export function createPriceCalculator(goodsList, productDetails, companyName, operateEnvironment, operatorName, operatorEmail, verificationCode) {
  return request({
    url: '/py_server/crm_tools/createPriceCalculator?goodsList=' + goodsList + '&productDetails=' + productDetails + '&companyName=' + companyName + '&operateEnvironment=' + operateEnvironment + '&operatorName=' + operatorName + '&operatorEmail=' + operatorEmail + '&verificationCode=' + verificationCode,
    method: 'get'
  })
}

//根据公司名称，获取BossList
export function getBossByCompayName(companyName, operateEnvironment) {
  return request({
    url: '/py_server/crm_tools/getBossByCompayName?companyName=' + companyName + '&operateEnvironment=' + operateEnvironment,
    method: 'get'
  })
}

//根据operator，获取报价单列表
export function findPriceCalculatorByOperator(operatorName, operatorEmail, pageNum, pageSize) {
  return request({
    url: '/py_server/crm_tools/findPriceCalculatorByOperator?operatorName=' + operatorName + '&operatorEmail=' + operatorEmail + '&pageNum=' + pageNum + '&pageSize=' + pageSize,
    method: 'get'
  })
}

//创建合同订单
export function createContractOrder(quoteNumber, superAdmin) {
  return request({
    url: '/py_server/crm_tools/createContractOrder?quoteNumber=' + quoteNumber + '&superAdmin=' + superAdmin,
    method: 'get'
  })
}

//根据operator，获取合同列表
export function findContractByOperator(operatorName, operatorEmail, pageNum, pageSize) {
  return request({
    url: '/py_server/crm_tools/findContractByOperator?operatorName=' + operatorName + '&operatorEmail=' + operatorEmail + '&pageNum=' + pageNum + '&pageSize=' + pageSize,
    method: 'get'
  })
}

//订单开通商品
export function openProductsByOrderNumber(orderNumber) {
  return request({
    url: '/py_server/crm_tools/openProductsByOrderNumber?orderNumber=' + orderNumber,
    method: 'get'
  })
}

//根据operator，获取订单列表
export function findOrderByOperator(operatorName, operatorEmail, pageNum, pageSize) {
  return request({
    url: '/py_server/crm_tools/findOrderByOperator?operatorName=' + operatorName + '&operatorEmail=' + operatorEmail + '&pageNum=' + pageNum + '&pageSize=' + pageSize,
    method: 'get'
  })
}

export function apiResultlist({ create_time, system_id }) {
  return request({
    url: '/py_server/crm_tools/apiAutoTestList?' + 'system_id=' + system_id + '&create_time=' + create_time,
    method: 'get'
  })
}

export function readReport() {
  return request({
    url: '/py_server/crm_tools/readHtmlReport?',
    method: 'get'
  })
}

// //获取用例列表
// export function caseList({ id, system_name, module_name, case_name, url, add_time, add_user, pageNum, pageSize }) {
//   return request({
//     url:
//       "/py_server/crm_tools/caseList?" + "id=" + id + "&system_name=" + system_name + "&module_name=" + module_name + "&case_name=" + case_name + "&url=" + url + "&add_time=" + add_time + "&add_user=" + add_user + "&pageNum=" + pageNum + "&pageSize=" + pageSize,
//     method: "get"
//   })
// }
/**
 * 重构后的caseList方法 用户获取用例列表(分页)
 * @param params { id, systemName, moduleName, caseName, url, addTime, pageNum, pageSize }
 * @returns Promise
 */
export function caseList(params) {
  return request({
    url: '/boss/crm/tools/caseList',
    method: 'get',
    params
  })
}

/**
 * 批量执行用例接口
 * @param data
 * @returns Promise
 */
export function batchExecuteCase(data) {
  return request({
    url: '/boss/crm/tools/batchExecuteCase',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data
  })
}

/**
 * 模糊查询url的接口
 * @param keyword 根据关键字模糊查询
 * @returns Promise
 */
export function searchUrl(keyword) {
  return request({
    url: '/boss/crm/tools/searchUrl',
    method: 'get',
    params: { keyword }
  })
}

//获取用例进度
export function caseProgress() {
  return request({
    url: '/py_server/crm_tools/caseProgress',
    method: 'get'
  })
}

//添加用例
export function addCase({ system_name, module_name, case_name, priority, url, request_header, request_type, parameters, body, body_type, sql_init, except_result, except_status, add_user }) {
  return request({
    url: '/py_server/crm_tools/addCase',
    method: 'post',
    data: {
      system_name,
      module_name,
      case_name,
      priority,
      url,
      request_header,
      request_type,
      parameters,
      body,
      body_type,
      sql_init,
      except_result,
      except_status,
      add_user
    },
    headers: { 'Content-Type': 'application/json' }
  })
}

//删除用例
export function deleteCase({ id }) {
  return request({
    url: '/py_server/crm_tools/deleteCase',
    method: 'post',
    data: { id },
    headers: { 'Content-Type': 'application/json' }
  })
}

//修改用例
export function editCase({ id, system_name, module_name, case_name, priority, url, request_header, request_type, parameters, body, body_type, sql_init, except_result, except_status, add_user }) {
  return request({
    url: '/py_server/crm_tools/editCase',
    method: 'post',
    data: {
      id,
      system_name,
      module_name,
      case_name,
      priority,
      url,
      request_header,
      request_type,
      parameters,
      body,
      body_type,
      sql_init,
      except_result,
      except_status,
      add_user
    },
    headers: { 'Content-Type': 'application/json' }
  })
}

//调试用例
export function debugCase({ add_user, url, request_header, request_type, parameters, body, body_type }) {
  return request({
    url: '/py_server/crm_tools/debugCase',
    method: 'post',
    data: { add_user, url, request_header, request_type, parameters, body, body_type },
    headers: { 'Content-Type': 'application/json' }
  })
}

//解析crm_cURL
export function analysisCrmCurlApi(curl) {
  return request({
    url: '/py_server/crm_tools/analysisCrmCurlApi',
    method: 'post',
    data: qs.stringify(curl),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

//批量上传用例 上传到服务器
export function batchUpload(data) {
  return request({
    url: '/py_server/crm_tools/batchUpload',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: data
  })
}

//导入用例
export function importCase({ filepath }) {
  return request({
    url: '/py_server/crm_tools/importCase',
    method: 'post',
    data: { filepath },
    headers: { 'Content-Type': 'application/json' }
  })
}

//批量执行用例
export function batchExcuteCase({ runUser, caseIdList }) {
  return request({
    url: '/py_server/crm_tools/batchExcuteCase',
    method: 'post',
    data: { runUser, caseIdList },
    headers: { 'Content-Type': 'application/json' }
  })
}

//获取用例执行结果
export function caseResult({ batch_no, add_user, runTime }) {
  return request({
    url: '/py_server/crm_tools/testCaseResultTotal?' + 'batch_no=' + batch_no + '&add_user=' + add_user + '&runTime=' + runTime,
    method: 'get'
  })
}

//用例执行结果明细
export function caseResultDetail({ batch_no, total_status }) {
  return request({
    url: '/py_server/crm_tools/testCaseResultDetail?' + 'batch_no=' + batch_no + '&total_status=' + total_status,
    method: 'get'
  })
}

//获取服务月份
export function getServerMonth(checkedItemIds, operator) {
  return request({
    url: '/py_server/crm_tools/getServerMonth?checkedItemIds=' + checkedItemIds + '&operateAccount=' + operator,
    method: 'get'
  })
}

//添加脚本工具目录
export function getSceneMenuList() {
  return request({
    url: '/py_server/crm_tools/getSceneMenuList',
    method: 'get'
  })
}

//添加或更新脚本工具目录
export function addOrUpdateSceneMenu({ id, parentId, menuName, menuType, testAccount, userName, userEmail }) {
  return request({
    url: '/py_server/crm_tools/addOrUpdateSceneMenu',
    method: 'post',
    data: { id, parentId, menuName, menuType, testAccount, userName, userEmail },
    headers: { 'Content-Type': 'application/json' }
  })
}

//删除脚本工具目录
export function deleteSceneMenu({ id, userName, userEmail }) {
  return request({
    url: '/py_server/crm_tools/deleteSceneMenu',
    method: 'post',
    data: { id, userName, userEmail },
    headers: { 'Content-Type': 'application/json' }
  })
}

//风控相关
export function whiteTool({ phoneMobile, type }) {
  return request({
    url: '/py_server/crm_tools/whiteTool?' + 'phoneMobile=' + phoneMobile + '&type=' + type,
    method: 'get'
  })
}

//风控号码
export function RiskPhoneTool({ risk_type, phonebossID, uniqueID, contentRisk, riskReasonCode }) {
  return request({
    url: '/py_server/crm_tools/riskPhoneNumberType?' + 'risk_type=' + risk_type + '&phonebossID=' + phonebossID + '&uniqueID=' + uniqueID + '&contentRisk=' + contentRisk + '&riskReasonCode=' + riskReasonCode,
    method: 'get'
  })
}

//脚本工具查询步骤接口列表
export function sceneCaseList(menuId) {
  return request({
    url: '/scene/case/list?' + 'menuId=' + menuId,
    method: 'get'
  })
}

//添加或更新脚本工具步骤接口
export function sceneCaseAddOrUpdate({ id, menuId, sceneCaseDesc, apiMethod, apiUrl, domain, requestParams, headers, postStep, step, assertStep, userName, userEmail }) {
  return request({
    url: '/scene/case/addOrUpdate',
    method: 'post',
    data: {
      id,
      menuId,
      sceneCaseDesc,
      apiMethod,
      apiUrl,
      domain,
      requestParams,
      headers,
      postStep,
      step,
      assertStep,
      userName,
      userEmail
    },
    headers: { 'Content-Type': 'application/json' }
  })
}

//删除脚本工具步骤接口
export function deleteCaseStep(id) {
  return request({
    url: '/scene/case/delete',
    method: 'post',
    data: { id },
    headers: { 'Content-Type': 'application/json' }
  })
}

//执行脚本工具
export function executeSceneCase(menuId, email) {
  return request({
    url: '/scene/case/execute',
    method: 'get',
    params: {
      ka: menuId,
      email
    }
  })
}

//生成脚本工具接口后置处理
export function producePostStep(jsonData, postStepParams) {
  return request({
    url: '/py_server/crm_tools/producePostStep',
    method: 'post',
    data: { jsonData, postStepParams },
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 根据订单号查询退款的流水信息
 * @param orderNumber
 * @returns {*}
 */
export function getRefundDetails(orderNumber) {
  return request({
    url: '/boss/crm/order/getRefundDetails',
    method: 'get',
    params: { orderNumber }
  })
}

/**
 * 更新有订单退款流水信息
 * @param data
 * @returns {*}
 */
export function updateOrderOnlineRefundStatus(data) {
  return request({
    url: '/boss/crm/order/updateOrderOnlineRefundStatus',
    method: 'post',
    data
  })
}

/**
 * 根据流水号查询退款信息
 * @param serialNumber 流水号
 * @returns {*}
 */
export function queryNoOrderRefundDetail(serialNumber) {
  return request({
    url: '/boss/crm/order/queryNoOrderRefundDetail',
    method: 'get',
    params: { serialNumber }
  })
}

/**
 * 无订单流水退款
 * @param processId bpm
 * @param status
 */
export function updateNoOrderRefundState({ processId, status }) {
  return request({
    url: '/boss/crm/order/updateNoOrderRefundState',
    method: 'post',
    data: { processId, status }
  })
}

/**
 * 导入银行流水
 * @param {*} param0
 * @returns
 */
export function importBankPay({ accountCompany, accountNumber, businessSummary, receivedPaidName, receivedPaidAccountNumber, payDay, payTime, creditAmount, email }) {
  return request({
    url: '/py_server/crm_tools/importBankPay',
    method: 'post',
    data: {
      accountCompany,
      accountNumber,
      businessSummary,
      receivedPaidName,
      receivedPaidAccountNumber,
      payDay,
      payTime,
      creditAmount,
      email
    }
  })
}

/**
 * 获取要匹配流水的订单信息
 * @param {*order_number}
 * @returns
 */
export function getOrderInfo({ order_number }) {
  return request({
    url: '/py_server/crm_tools/getOrderInfo',
    method: 'post',
    data: {
      order_number
    }
  })
}

/**
 * 给订单匹配流水
 * @param {*} param0
 * @returns
 */
export function orderMatchBankPay(data) {
  return request({
    url: '/py_server/crm_tools/orderMatchBankPay',
    method: 'post',
    data: {
      ...data,
      _: new Date().getTime()
    }
  })
}

/**
 * 根据批次号 分页查询用例执行详情
 * @param {batchNo,pageNum,pageSize}
 * @returns
 */
export function getCaseExecuteResult(params) {
  return request({
    url: '/boss/crm/tools/getCaseExecuteResult',
    method: 'get',
    params
  })
}

/**
 * 查询用例的实际执行结果
 * @param id
 */
export function getExecuteResultDetailById(id) {
  return request({
    url: '/boss/crm/tools/getResultDetail',
    method: 'get',
    params: { id }
  })
}

/**
 * 查询用例执行结果的列表
 * @param params batchNo 批次号
 *              opUser 从操作人
 *              pageNum 当前页码
 *              pageSize 每页条数
 *
 */
export function getBatchList(params) {
  return request({
    url: '/boss/crm/tools/getBatchList',
    method: 'get',
    params
  })
}

// 新增用例
export function saveCaseInfo(data) {
  return request({
    url: '/boss/crm/tools/save.json',
    method: 'post',
    data
  })
}

/**
 * 调试api
 * @param data
 */
export function debugApi(data) {
  return request({
    url: '/boss/crm/tools/debug',
    method: 'post',
    data
  })
}
