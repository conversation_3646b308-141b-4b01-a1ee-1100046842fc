import request from '@/utils/request'

export function getProjectList(params) {
  return request({
    url: '/py_server/dcg/project/list',
    method: 'get',
    params
  })
}

export function deleteProject(id) {
  return request({
    url: '/py_server/dcg/project',
    method: 'delete',
    params: { id }
  })
}

export function insertProject(data) {
  return request({
    url: '/py_server/dcg/project',
    method: 'post',
    data
  })
}

export function updateProject(data) {
  return request({
    url: '/py_server/dcg/project',
    method: 'put',
    data
  })
}

export function initProject(id) {
  return request({
    url: '/py_server/dcg/project/init',
    method: 'get',
    params: { id }
  })
}

export function syncProject(id) {
  return request({
    url: '/py_server/dcg/project/sync',
    method: 'get',
    params: { id }
  })
}

export function allProject() {
  return request({
    url: '/py_server/dcg/project/all',
    method: 'get'
  })
}

export function projectDetail(id) {
  return request({
    url: '/py_server/dcg/project',
    method: 'get',
    params: { id }
  })
}

export function installProject(id) {
  return request({
    url: '/py_server/dcg/project/install',
    method: 'get',
    params: { id }
  })
}

export function getInstallProject(id) {
  return request({
    url: '/py_server/dcg/project/getInstall',
    method: 'get',
    params: { id }
  })
}
