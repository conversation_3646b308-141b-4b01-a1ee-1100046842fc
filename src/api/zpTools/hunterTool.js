import request from '@/utils/request'
import qs from 'qs'
import axios from 'axios'
import { getInfo, getInfoIuc } from '@/api/common/user'
import Cookies from 'js-cookie'
import { getToken } from '@/utils/auth'
import { sendRequest } from '@/api/zpTools/tools'

const state = {
  token: getToken(),
  name: '',
  email: '',
  avatar: '',
  introduction: '',
  roles: [],
  menus: []
}

export function editflashmessage(data) {
  return request({
    url: '/py_server/hunter_flash_message',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'application/json' }
  })
}

export function crmAuto(data) {
  return request({
    url: '/py_server/hunter_crm_auto',
    method: 'post',
    data: data,
    headers: { 'Content-Type': 'application/json' }
  })
}

export function getEmailInfo() {
  return new Promise((resolve, reject) => {
    getInfoIuc('zhipinqa', Cookies.get('t_uc'), 'GSF3eJvz75SPAYt6WmBsnJqM', 'zhipin_qa')
      .then(response => {
        let userInfo = JSON.parse(response.data)
        let cookie = Cookies.get('t_uc')
        let testToken = Cookies.get('Test-Token')
        let info = { userinfo: userInfo, cookie: cookie, testToken: testToken }
        resolve(info)
      })
      .catch(error => {
        reject(error)
      })
  })
}

//获取审核职位
export function getAuditJob(data, env, page, pageSize) {
  return request({
    url: '/py_server/hunter_audit_list/' + data + '/' + env + '/' + page + '/' + pageSize,
    method: 'get'
  })
}

//审核职位
export function auditPassJob(encBossId, sjid, env, choice, cookie) {
  return request({
    url: '/py_server/hunter_audit_list',
    method: 'post',
    data: { encBossId: encBossId, sjid: sjid, env: env, choice: choice, adminCookie: cookie },
    headers: { 'Content-Type': 'application/json' }
  })
}

// 获取猎企公司
export function getHunterCom(comName, cookie) {
  return request({
    url: `/py_server/hunter_get_com?comName=${comName}&cookie=${cookie}`,
    method: 'get',
    headers: { 'Content-Type': 'application/json' }
  })
}

// 获取猎企管理员ID
export function getHunterManagerId(comName, cookie) {
  return request({
    url: `/py_server/hunter_get_managerId`,
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: { comName: comName, cookie: cookie }
  })
}

//获取用例列表
export function getSmokingCase(versionName, userName, cookie) {
  return request({
    url: '/py_server/hunter_smoking_case',
    method: 'post',
    data: { versionName: versionName, userName: userName, cookie: cookie },
    headers: { 'Content-Type': 'application/json' }
  })
}

export function hunterExecuteCase(versionName, userName, libraryId, caseIds) {
  return request({
    url: '/py_server/hunter_execute_case',
    method: 'post',
    data: { versionName: versionName, userName: userName, libraryId: libraryId, caseIds: caseIds },
    headers: { 'Content-Type': 'application/json' }
  })
}

//获取版本信息
export function getVersionList() {
  return request({
    url: '/py_server/hunter_get_versions',
    method: 'get',
    headers: { 'Content-Type': 'application/json' }
  })
}

//添加用例版本
export function addVersion(versionName, userName) {
  return request({
    url: '/py_server/hunter_get_versions',
    method: 'post',
    data: { versionName: versionName, userName: userName },
    headers: { 'Content-Type': 'application/json' }
  })
}

export function auditUnPassJob(encBossId, sjid, env, choice, cookie) {
  return request({
    url: '/py_server/hunter_audit_list',
    method: 'post',
    data: { encBossId: encBossId, sjid: sjid, env: env, choice: choice, adminCookie: cookie },
    headers: { 'Content-Type': 'application/json' }
  })
}

// 精准Call收藏牛人
export function addFavoriteGeek(bossId, env, securityId) {
  return request({
    url: '/py_server/hunter_accSearch',
    method: 'post',
    data: { bossId: bossId, env: env, securityId: securityId },
    headers: { 'Content-Type': 'application/json' }
  })
}

//精准Call进行Call一下
export function getTel(bossId, encJobId, env, securityId) {
  return request({
    url: '/py_server/hunter_accSearch_getTel',
    method: 'post',
    data: { bossId: bossId, env: env, encJobId: encJobId, securityId: securityId },
    headers: { 'Content-Type': 'application/json' }
  })
}

//意向沟通进行推荐Ta
export function recommendGeek(bossId, env, securityId) {
  return request({
    url: '/py_server/hunter_intent_recommend',
    method: 'post',
    data: { bossId: bossId, env: env, securityId: securityId },
    headers: { 'Content-Type': 'application/json' }
  })
}
// 获取猎头在线职位
export function getHunterJobList(bossId, env) {
  return request({
    url: '/py_server/hunter_acc_getJob/' + bossId + '/' + env,
    method: 'get',
    headers: { 'Content-Type': 'application/json' }
  })
}

// 获取Boss列表第一个职位Id
export function getBossJobId(bossId, env) {
  return request({
    url: `/py_server/hunter_intent_recommend?bossId=${bossId}&env=${env}`,
    method: 'get'
  })
}

// 精准Call批量Call一下
export function accBatchCall(bossId, jobName, encryptJobId, env, num) {
  return request({
    url: '/py_server/hunter_acc_batchGetTel',
    method: 'post',
    data: { bossId: bossId, jobName: jobName, encryptJobId: encryptJobId, env: env, num: num },
    headers: { 'Content-Type': 'application/json' }
  })
}

//更换公司获取公司名称
export function getCom(bossId, comName, env) {
  return request({
    url: '/py_server/hunter_getCom/' + bossId + '/' + env + '/' + comName,
    method: 'get',
    headers: { 'Content-Type': 'application/json' }
  })
}

//更换公司获取品牌名称
export function getBrand(bossId, comName, env) {
  return request({
    url: '/py_server/hunter_getBrand/' + bossId + '/' + env + '/' + comName,
    method: 'get',
    headers: { 'Content-Type': 'application/json' }
  })
}

//操作更换公司
export function changeCom(bossId, brandName, brandIndustry, brandScale, newEncryptComId, newEncryptBrandId, comName, env) {
  return request({
    url: '/py_server/hunter_changeCom',
    method: 'post',
    data: { bossId: bossId, brandName: brandName, brandIndustry: brandIndustry, env: env, brandScale: brandScale, newEncryptComId: newEncryptComId, newEncryptBrandId: newEncryptBrandId, comName: comName },
    headers: { 'Content-Type': 'application/json' }
  })
}

//获取已保存的缓存信息
export function getCache(operator) {
  return request({
    url: '/py_server/hunter_operateCache?creator=' + operator,
    method: 'get'
  })
}

//对缓存执行操作
export function opCache(cacheName, cacheRemark, creator, op, id) {
  return request({
    url: '/py_server/hunter_operateCache',
    method: 'post',
    data: { cacheName: cacheName, cacheRemark: cacheRemark, creator: creator, op: op, id: id },
    headers: { 'Content-Type': 'application/json' }
  })
}

//操作已保存的缓存信息
export function exeCache(keyName, cookie, env, op, second) {
  return request({
    url: '/py_server/hunter_executeCache',
    method: 'post',
    data: { keyName: keyName, cookie: cookie, env: env, op: op, second: second },
    headers: { 'Content-Type': 'application/json' }
  })
}

// 获取猎头缓存执行结果
export function getRedisResult(cookie, keyName) {
  return request({
    url: '/py_server/hunter_getRedisResult',
    method: 'post',
    data: { cookie: cookie, keyName: keyName },
    headers: { 'Content-Type': 'application/json' }
  })
}

// 加入搜索功能
export function searchCache(data, creator) {
  return request({
    url: '/py_server/hunter_searchCache?searchData=' + data + '&creator=' + creator,
    method: 'get'
  })
}

// bpm相关
export function getBpmInfo(cookie, startDate, endDate) {
  return request({
    url: '/py_server/getHunterBpm',
    method: 'get',
    params: {
      cookie,
      startDate,
      endDate
    }
  })
}

export function getTestCaseApi() {
  return request({
    url: '/py_server/getHunterTestCaseApi',
    method: 'get'
  })
}

export function getTestCaseResult(data) {
  return request({
    url: '/py_server/testcaseGen',
    method: 'get',
    params: data
  })
}
//获取账号相关
export function getProxyNum(env, coo) {
  return request({
    url: '/py_server/hunter_get_num/' + env + '/' + coo,
    method: 'get'
  })
}

//获取可以发职位的hunterid
export function getHunterId() {
  return request({
    url: '/py_server/hunter_get_id',
    method: 'get'
  })
}

//快速发布猎头职位
export function quickHunterJob(hunterid, coo, jobName) {
  return request({
    url: '/py_server/hunter_job/' + hunterid + '/' + coo + '/' + jobName,
    method: 'get'
  })
}

//解密方法
export async function dec(data) {
  let arr = {
    decData: data,
    decPassword: '',
    decType: 1,
    decSecurityId: ''
  }
  let parameters = JSON.stringify(arr).replace('}{', ',')
  let request = {
    url: 'https://admin-qa.weizhipin.com/tools/decrypt.json',
    method: 'post',
    parameter: parameters,
    env: 1
  }
  const res = await sendRequest(request)
  let dec_result = JSON.parse(res.data.response)['result']
  return dec_result
}

//加密方法
export async function enc(data) {
  let arr = {
    encData: data,
    encPassword: '',
    encType: 1
  }
  let parameters = JSON.stringify(arr).replace('}{', ',')
  let request = {
    url: 'https://admin-qa.weizhipin.com/tools/encrypt.json',
    method: 'post',
    parameter: parameters,
    env: 1
  }
  const res = await sendRequest(request)
  let enc_result = JSON.parse(res.data.response)['result']
  return enc_result
}

//获取工具执行记录
export function getExecuteInfo(toolName, unit) {
  return request({
    url: 'py_server/get_save_time',
    method: 'get',
    params: {
      toolName,
      unit
    }
  })
}

//更新猎头工具执行记录
export function updateExecuteCount(toolName) {
  return request({
    url: 'py_server/update_execute_statistic',
    method: 'get',
    params: {
      toolName
    }
  })
}

//创建推荐记录
export function createRecommendRecord(hunterPhone, projectId, status, hunterUid, candidateName) {
  return request({
    url: 'py_server/recommendRecord',
    method: 'get',
    params: {
      hunterPhone,
      projectId,
      status,
      hunterUid,
      candidateName
    }
  })
}
//获取推荐记录状态选项列表
export function getAllStatusEnum() {
  return request({
    url: 'py_server/getAllStatusEnum',
    method: 'get',
    params: {}
  })
}
//开启关闭猎企免授权状态
export function updateAuthStatus(comId, status) {
  return request({
    url: 'py_server/updateAuthStatus',
    method: 'get',
    params: {
      comId,
      status
    }
  })
}

//获取qa环境boss直聘用户wt2
export function getwt2(userId) {
  return request({
    url: 'boss/web/get_login_by_id',
    method: 'get',
    params: {
      userId
    }
  })
}

//根据手机号查询boss信息
export function getBossInfoByPhone(phone) {
  return request({
    url: '/py_server/getBossInfoByPhone',
    method: 'get',
    params: {
      phone
    }
  })
}

//根据手机号列表查询牛人信息(phone1,phone2,phone3)
export function getBatchGeekInfoByPhone(phoneList) {
  return request({
    url: '/py_server/getBatchGeekInfoByPhoneList',
    method: 'get',
    params: {
      phoneList
    }
  })
}

//根据id列表查询牛人信息(uid1,uid2,uid3)
export function getBatchGeekInfoById(userIdList) {
  return request({
    url: '/py_server/getBatchGeekInfoByIdList',
    method: 'get',
    params: {
      userIdList
    }
  })
}
//意向沟通根据bossId和comId查询客户名下待处理推荐记录
export function recommendHandleList(bossId, comId, hunterId) {
  return request({
    url: '/py_server/recommendHandleList',
    method: 'get',
    params: {
      bossId,
      comId,
      hunterId
    }
  })
}
//意向沟通提交推荐报告V4,请求参数为json字符串，以及表单bossId
export function handleRecommendV4(bossId, body) {
  return request({
    url: '/py_server/handleRecommendV4',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: body,
    params: {
      bossId: bossId
    }
  })
}
//意向沟通提交推荐报告V3,请求参数为json字符串，以及表单bossId
export function handleRecommendV3(bossId, body) {
  return request({
    url: '/py_server/handleRecommendV3',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: body,
    params: {
      bossId: bossId
    }
  })
}
//清理意向沟通消息卡片缓存
export function clearIntentionCardCache(bossId) {
  return request({
    url: '/py_server/clearIntentionCardCache',
    method: 'get',
    params: {
      bossId
    }
  })
}
//意向沟通添加数星测试数据
export function addDataStar(data) {
  return request({
    url: '/py_server/addDataStar',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
//意向沟通定时任务推送消息卡片数据准备
export function prepareIntentionCardData(bossId, geekId) {
  return request({
    url: '/py_server/prepareIntentionCardData',
    method: 'get',
    params: {
      bossId,
      geekId
    }
  })
}
//意向沟通促分配任务
export function distRemind(comId, remindStage, remindStatus, remindDdl) {
  return request({
    url: '/py_server/distRemind',
    method: 'get',
    params: {
      comId: comId,
      remindStage: remindStage,
      remindStatus: remindStatus,
      remindDdl: remindDdl
    }
  })
}
//意向沟通新boss引导任务
export function useRemind(bossId, comId, remindStage, remindStatus, remindDdl) {
  return request({
    url: '/py_server/useRemind',
    method: 'get',
    params: {
      bossId: bossId,
      comId: comId,
      remindStage: remindStage,
      remindStatus: remindStatus,
      remindDdl: remindDdl
    }
  })
}
//意向沟通转人工任务
export function manualRemind(bossId, comId) {
  return request({
    url: '/py_server/manualRemind',
    method: 'get',
    params: {
      bossId: bossId,
      comId: comId
    }
  })
}

//根据bossId获取沟通列表
export function getAllFriend(bossId) {
  return request({
    url: '/py_server/getAllFriend',
    method: 'get',
    params: {
      bossId: bossId
    }
  })
}
//执行定时任务
export function jobTrigger(data) {
  return request({
    url: '/boss/hunter/jobTrigger',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//执行sql语句，仅限zp_hunter库   {sql: "select * from t_hunter where id=1"}
export function executeSql(sql) {
  const sqlForm = { sql: sql }
  return request({
    url: '/boss/hunter/data/query',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(sqlForm)
  })
}

//随机成姓名
export function getRandomName() {
  const familyNames = new Array(
    '赵',
    '钱',
    '孙',
    '李',
    '周',
    '吴',
    '郑',
    '王',
    '冯',
    '陈',
    '褚',
    '卫',
    '蒋',
    '沈',
    '韩',
    '杨',
    '朱',
    '秦',
    '尤',
    '许',
    '何',
    '吕',
    '施',
    '张',
    '孔',
    '曹',
    '严',
    '华',
    '金',
    '魏',
    '陶',
    '姜',
    '戚',
    '谢',
    '邹',
    '喻',
    '柏',
    '水',
    '窦',
    '章',
    '云',
    '苏',
    '潘',
    '葛',
    '奚',
    '范',
    '彭',
    '郎',
    '鲁',
    '韦',
    '昌',
    '马',
    '苗',
    '凤',
    '花',
    '方',
    '俞',
    '任',
    '袁',
    '柳',
    '酆',
    '鲍',
    '史',
    '唐',
    '费',
    '廉',
    '岑',
    '薛',
    '雷',
    '贺',
    '倪',
    '汤',
    '滕',
    '殷',
    '罗',
    '毕',
    '郝',
    '邬',
    '安',
    '常',
    '乐',
    '于',
    '时',
    '傅',
    '皮',
    '卞',
    '齐',
    '康',
    '伍',
    '余',
    '元',
    '卜',
    '顾',
    '孟',
    '平',
    '黄',
    '和',
    '穆',
    '萧',
    '尹'
  )
  const givenNames = new Array(
    '子璇',
    '淼',
    '国栋',
    '夫子',
    '瑞堂',
    '甜',
    '敏',
    '尚',
    '国贤',
    '贺祥',
    '晨涛',
    '昊轩',
    '易轩',
    '益辰',
    '益帆',
    '益冉',
    '瑾春',
    '瑾昆',
    '春齐',
    '杨',
    '文昊',
    '东东',
    '雄霖',
    '浩晨',
    '熙涵',
    '溶溶',
    '冰枫',
    '欣欣',
    '宜豪',
    '欣慧',
    '建政',
    '美欣',
    '淑慧',
    '文轩',
    '文杰',
    '欣源',
    '忠林',
    '榕润',
    '欣汝',
    '慧嘉',
    '新建',
    '建林',
    '亦菲',
    '林',
    '冰洁',
    '佳欣',
    '涵涵',
    '禹辰',
    '淳美',
    '泽惠',
    '伟洋',
    '涵越',
    '润丽',
    '翔',
    '淑华',
    '晶莹',
    '凌晶',
    '苒溪',
    '雨涵',
    '嘉怡',
    '佳毅',
    '子辰',
    '佳琪',
    '紫轩',
    '瑞辰',
    '昕蕊',
    '萌',
    '明远',
    '欣宜',
    '泽远',
    '欣怡',
    '佳怡',
    '佳惠',
    '晨茜',
    '晨璐',
    '运昊',
    '汝鑫',
    '淑君',
    '晶滢',
    '润莎',
    '榕汕',
    '佳钰',
    '佳玉',
    '晓庆',
    '一鸣',
    '语晨',
    '添池',
    '添昊',
    '雨泽',
    '雅晗',
    '雅涵',
    '清妍',
    '诗悦',
    '嘉乐',
    '晨涵',
    '天赫',
    '玥傲',
    '佳昊',
    '天昊',
    '萌萌',
    '若萌',
    '者恒',
    '力行',
    '荒原',
    '晨曦',
    '日焕',
    '晨轩',
    '轩辕',
    '辰逸',
    '辰铭',
    '辰锟'
  )

  var i = parseInt(10 * Math.random()) * 10 + parseInt(10 * Math.random())
  var familyName = familyNames[i]

  var j = parseInt(10 * Math.random()) * 10 + parseInt(10 * Math.random())
  var givenName = givenNames[i]

  var name = familyName + givenName
  return name
}

//生成随机手机号
export function getRandomPhone() {
  var prefixArray = new Array('130', '131', '132', '133', '135', '137', '138', '170', '187', '189')
  var i = parseInt(10 * Math.random())
  var prefix = prefixArray[i]
  for (var j = 0; j < 8; j++) {
    prefix = prefix + Math.floor(Math.random() * 10)
  }
  return prefix
}

export function format() {
  if (arguments.length == 0) return null
  var str = arguments[0]
  for (var i = 1; i < arguments.length; i++) {
    var re = new RegExp('\\{' + (i - 1) + '\\}', 'gm')
    str = str.replace(re, arguments[i])
  }
  return str
}

//特殊符号(+  '   ")转换为url的编码形式，传与后端。后端进行参数解析
export function url_encode(sStr) {
  return escape(sStr).replace(/\+/g, '%2B').replace(/\"/g, '%22').replace(/\'/g, '%27').replace(/\//g, '%2F')
}
