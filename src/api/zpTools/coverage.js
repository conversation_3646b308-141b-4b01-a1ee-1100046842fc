import request from '@/utils/request'
import qs from 'qs'

//线上接口统计-接口查询
export function getOnlineInterfaces(serviceName, path, sortType, isNeedCoverage, isAutoInterface, pageIndex, pageSize, device, tester, name) {
  return request({
    url: '/tools/online/interface/list',
    method: 'get',
    params: {
      serviceName,
      path,
      sortType,
      pageIndex,
      pageSize,
      isNeedCoverage,
      isAutoInterface,
      device,
      tester,
      name
    }
  })
}

//线上接口统计-接口查询
export function getOnlineServiceNames() {
  return request({
    url: '/tools/online/interface/getServiceName',
    method: 'get',
    params: {}
  })
}

//删除
export function deleteOnlineInterfaces(id) {
  return request({
    url: '/tools/online/interface/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//接口跳转
export function skipsYapi(q) {
  return request({
    url: '/skip/Yapi',
    method: 'get',
    params: {
      q
    }
  })
}

export function skipsYapiInfo(id) {
  return request({
    url: '/skip/Yapi/info',
    method: 'get',
    params: {
      id
    }
  })
}

export function onlineInterfaceUpdateV2(serviceName, startTime, endTime) {
  return request({
    // url: '/tools/online/interface/updateV2',
    url: '/tools/online/api/updateV3',
    method: 'get',
    params: {
      serviceName,
      startTime,
      endTime
    },
    timeout: 180 * 1000
  })
}

export function onlineInterfaceNameUpdate(serviceName) {
  return request({
    url: '/tools/online/interface/nameUpdate',
    method: 'get',
    params: {
      serviceName
    },
    timeout: 60 * 1000
  })
}

export function isAutoOnlineInterface(serviceName) {
  return request({
    url: '/tools/online/interface/isAutoInterface',
    method: 'get',
    params: {
      serviceName
    },
    timeout: 60 * 1000
  })
}

//更新
export function editOnlineInterfaces(data) {
  return request({
    url: '/tools/online/interface/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data),
    timeout: 60 * 1000
  })
}

//添加
export function addOnlineInterfaces(data) {
  return request({
    url: '/tools/online/interface/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

export function getCoverageStatisticss(serviceName, startTime, endTime, requestsNumber, env, interfaceType, device, tester) {
  return request({
    url: '/tools/online/interface/coverageV2',
    method: 'get',
    params: {
      serviceName,
      startTime,
      endTime,
      requestsNumber,
      env,
      interfaceType,
      device,
      tester
    },
    timeout: 60 * 1000
  })
}

export function getCoverageStatisticsV3(serviceName, startTime, endTime, requestsNumber, env, interfaceType, device, tester) {
  return request({
    url: '/tools/online/interface/coverage/coverageV3',
    method: 'get',
    params: {
      serviceName,
      startTime,
      endTime,
      requestsNumber,
      env,
      interfaceType,
      device,
      tester
    },
    timeout: 60 * 1000
  })
}

export function isAutoInterfaceByPath(path) {
  return request({
    url: '/tools/online/interface/isAutoInterfaceByPath',
    method: 'get',
    params: {
      path
    },
    timeout: 60 * 1000
  })
}

export function deviceUpdate(serviceName, path, startTime, endTime) {
  return request({
    url: '/tools/online/interface/deviceUpdate',
    method: 'get',
    params: {
      serviceName,
      startTime,
      path,
      endTime
    },
    timeout: 300 * 1000
  })
}

export function startConsumer() {
  return request({
    url: '/boss/consumer/webBatchAccessInfo/start',
    method: 'get',
    params: {}
  })
}

export function stopConsumer() {
  return request({
    url: '/boss/consumer/webBatchAccessInfo/stop',
    method: 'get',
    params: {}
  })
}

export function statusConsumer() {
  return request({
    url: '/boss/consumer/webBatchAccessInfo/status',
    method: 'get',
    params: {}
  })
}

export function addUserId(data) {
  return request({
    url: '/tools/online/interface/addUserId',
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

//线上接口统计-服务
export function getInterfacesCoverageStatistics(env, device, startTime, endTime) {
  return request({
    url: '/tools/online/interface/coverage/statisticsByService',
    method: 'get',
    params: {
      env,
      device,
      startTime,
      endTime
    },
    timeout: 60 * 1000
  })
}

//线上接口统计
export function getInterfacesCoverageStatisticsByPerson(env, device, testGroup, startTime, endTime) {
  return request({
    url: '/tools/online/interface/coverage/statisticsByPerson',
    method: 'get',
    params: {
      env,
      device,
      testGroup,
      startTime,
      endTime
    },
    timeout: 60 * 1000
  })
}

export function testhubSyncTesterOperaLogs(name, userId, startTime, endTime) {
  return request({
    url: '/boss/testhub/syncTester/operationlogs',
    method: 'get',
    params: {
      name,
      userId,
      startTime,
      endTime
    },
    timeout: 60 * 1000
  })
}
