import request from '@/utils/request'
import qs from 'qs'
import project from '@/router/modules/project'

export function sendRequest(data) {
  return request({
    url: '/boss/new/common',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

// 获取所有项目列表
export function getProject() {
  return request({
    url: 'tools/script/get_all_project',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//根据项目查询分组信息
export function getGroupByProject(project) {
  return request({
    url: 'tools/script/get_group_by_project',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: { project: project }
  })
}

// 新建分组
export function addGroup(form) {
  return request({
    url: 'tools/script/del_group',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(form)
  })
}

// 删除分组
export function delGroup(id) {
  return request({
    url: 'tools/script/get_group_by_project',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: { id: id }
  })
}

// 根据id 查询脚本
export function getScriptById(id) {
  return request({
    url: 'tools/script/get_script_by_id',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: { id: id }
  })
}

//上传脚本
export function uploadScript(data) {
  return request({
    url: 'tools/script/upload/file',
    method: 'post',
    headers: { 'content-type': 'multipart/form-data' },
    data: data
  })
}

//新增脚本
export function addScript(form) {
  return request({
    url: 'tools/script/add_script',
    method: 'post',
    // headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(form)
  })
}

//更新脚本
export function updateScript(form) {
  return request({
    url: 'tools/script/update_script',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(form)
  })
}

export function getAllScript() {
  return request({
    url: 'tools/script/get_all_script',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getScriptByAuthor(author) {
  return request({
    url: 'tools/script/get_script_by_author',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    params: { author: author }
  })
}
//删除脚本
export function delScript(scriptName) {
  return request({
    url: 'tools/script/del_script',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: { script_name: scriptName }
  })
}

//查询脚本及其分组信息
export function getScriptGroup() {
  return request({
    url: 'tools/script/get_script_group',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getScriptByName(scriptName) {
  return request({
    url: 'tools/script/get_script_by_name',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: { script_name: scriptName }
  })
}

export function runScript(form) {
  return request({
    url: 'tools/script/run_script',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(form)
  })
}

export function editScript(form) {
  return request({
    url: 'tools/script/edit_script',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(form)
  })
}

//获取极值限制状态

export function getExtremeLimit(env, userId) {
  return request({
    url: '/tools/status/limit/record?userId=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//获取牛人状态
export function getUserState(env, userId) {
  return request({
    url: '/tools/status/geekget?geekid=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//获取boss 状态
export function getBossState(env, userId) {
  return request({
    url: '/tools/status/bossget?bossid=' + userId + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//公共数据库查询方法

export function connectDb(sql, pageIndex, env, db, ip) {
  return request({
    url: '/tools/data/query?env=' + env + '&db=' + db + '&ip=' + ip + '&pageIndex=' + pageIndex + '&pageSize=15' + '&sql=' + sql,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//公共数据库查询方法 带用户名和密码

export function connectDbWithPassword(sql, pageIndex, db, ip, passport, account) {
  return request({
    url: '/tools/data/query?&db=' + db + '&ip=' + ip + '&account=' + account + '&passport=' + passport + '&pageIndex=' + pageIndex + '&pageSize=' + pageIndex + '&sql=' + sql,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//四个参数
export function connectDbWithFour(sql, pageIndex, env, db) {
  return request({
    url: '/tools/data/query?env' + env + '&db=' + db + '&pageIndex=' + pageIndex + '&pageSize=15' + '&sql=' + sql,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}
// userId查询 * 加密 * 解密

export function encrypt(phone) {
  return request({
    url: '/boss/pass/encrypt?phone=' + phone,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function decrypt(phone) {
  return request({
    url: '/boss/pass/decrypt?phone=' + phone,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

// 微信解绑

export function unbinding(env, userId) {
  return request({
    url: '/tools/base/wechat',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: { env: env, userId: userId }
  })
}

//微信解绑并且删除授权信息
// export function wechatUnbindingAndDeleteAuthorize (env, userId){
//     return request({
//         url: '/tools/base/wechatUnbindingAndDeleteAuthorize',
//         method: 'get',
//         headers: { 'content-type': 'application/x-www-form-urlencoded' },
//         params: { "env": env, "userId": userId }
//     })
// }

export function wechatUnbindingAndDeleteAuthorize(env, userId) {
  return request({
    url: 'boss/tools/dubbo/wechatUnbindingAndDelAuth',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: { env: env, userId: userId }
  })
}

//获取牛人tickey

export function getGeekTicKey(envIp, db, phone) {
  return request({
    url: '/business/getSecretKeyV2?envIp=' + envIp + '&db=' + db + '&phone=' + phone + '&projectId=22' + '&envId=',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded', 'Access-Control-Allow-Origin': '*' }
  })
}

//获取boss tickey
export function getBossTicKey(envIp, db, phone) {
  return request({
    url: '/business/getSecretKeyV2?envIp=' + envIp + '&db=' + db + '&phone=' + phone + '&projectId=22' + '&envId=',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded', 'Access-Control-Allow-Origin': '*' }
  })
}

//获取Security

export function getSecurity(userId, identity) {
  return request({
    url: '/tools/security/getSecurity?userId=' + userId + '&identity=' + identity,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded', 'Access-Control-Allow-Origin': '*' }
  })
}

//公共接口调用

// export function commonRequest(url,type,parameters,signKey){
//   return request( qs.stringify({
//     url:'/request/key',
//     method:"post",
//     parameter:{ url: url, method: type, parameter: parameters, signKey: signKey, headers: "", cookie: "", },
//     headers: { 'content-type': 'application/x-www-form-urlencoded'},
//   }))
// }

//获取牛人简历预览链接
export function getResumeUrl(userId) {
  return request({
    url: '/tools//userId/encrypt?userId=' + userId,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//清除数据库
export function delDatabase() {
  return request({
    url: '/tools/Password/Complain',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//更换设备验证码查询

export function changeDevice(parameter) {
  return request({
    url: '/boss/dubbo/doSendWithTelnet',
    method: 'post',
    data: qs.stringify(parameter),
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//保存手机号

export function savePhone(parameter) {
  return request({
    url: '/boss/dubbo/doSendWithTelnet',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    data: qs.stringify(parameter)
  })
}

//删除 三参数执行

export function delIcp(env, db, sql) {
  return request({
    url: '/tools/data/query?env=' + env + '&db=' + db + '&sql=' + sql,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function doSendWithTelnet(parameter) {
  return request({
    url: '/boss/dubbo/doSendWithTelnet',
    method: 'post',
    data: qs.stringify(parameter),
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function beanBag(userId, env, db) {
  return request({
    url: '/business/delete/beanBag?userId=' + userId + '&env=' + env + '&db=' + db,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function addBeansNewsAll(userId, count, env, db) {
  return request({
    url: '/business/add/beanBag?userId=' + userId + '&count=' + count + '&env=' + env + '&db=' + db,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function addGiftNews(userId, count, env, db) {
  return request({
    url: '/business/add/giftBag?userId=' + userId + '&count=' + count + '&env=' + env + '&db=' + db,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function addFundNews(userId, count, env, db) {
  return request({
    url: '/business/add/fundBag?userId=' + userId + '&count=' + count + '&env=' + env + '&db=' + db,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getClientInfo(phone, env) {
  return request({
    url: '/tools/getclientinfo/bossget?id=' + phone + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//获取文件列表
export function getFileList(pageIndex, group) {
  return request({
    url: '/boss/file/filelist?pageSize=10&pageIndex=' + pageIndex + '&group=' + group,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//执行adb 命令
export function runAdb(cmd, device) {
  return request({
    url: '/tools/adb/run_adb?cmd=' + cmd + '&device=' + device,
    method: 'get',
    headers: { 'Content-Type': 'application/json;charset=UTF-8' }
  })
}

export function screenCap(phoneInfo) {
  return request({
    url: '/tools/adb/screenCap',
    method: 'post',
    data: qs.stringify(phoneInfo),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}
export function execCommand(phoneInfo) {
  return request({
    url: '/tools/adb/exec_command',
    method: 'post',
    data: qs.stringify(phoneInfo),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

//删除文件
export function delFile(name, group) {
  return request({
    url: '/boss/file/deletefile?filename=' + name + '&group=' + group,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//文件上传
export function addFile(formData) {
  return request({
    url: '/boss/dubbo/doSendWithTelnet',
    method: 'post',
    parameter: qs.stringify(formData),
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getShell(server, env) {
  return request({
    url: '/tools/shell/getshell?servername=' + server + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//抓包工具获取error

export function getError(startTime, endTime, env, pageIndex) {
  return request({
    url: '/boss/log/error?startTime=' + startTime + '&endTime=' + endTime + '&env=' + env + '&pageIndex=' + pageIndex + '&pageSize=100',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getSecretKeyV2(envIp, db, phone) {
  return request({
    url: '/business/getSecretKeyV2?envIp=' + envIp + '&db=' + db + '&phone=' + phone + '&projectId=2' + '&envId=',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getProjectItem(item) {
  return request({
    url: '/boss/project/list?item=' + item,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getProjectGroup(projectId) {
  return request({
    url: '/boss/interfaceGroup/list?projectId=' + projectId + '&type=0',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function setCode(parameter) {
  return request({
    url: '/dianzhang/setcode',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(parameter)
  })
}
//店长假开聊
export function testfake(parameter) {
  return request({
    url: '/testf/testfake',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(parameter)
  })
}
//人脸次数查询
export function getFaceNumber(uid, env) {
  return request({
    url: '/dianzhang/getFaceNumber?uid=' + uid + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}
//人脸次数清除
export function deleteFaceNumber(uid, env) {
  return request({
    url: '/dianzhang/deleteFaceNumber?uid=' + uid + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}
// //查看注销状态
// export function logoffStatus(phone,env) {
//   return request({
//     url:'/dianzhang/LogoffStatus?phone=' + phone + '&env=' + env ,
//     method:'get',
//     headers: { 'content-type': 'application/x-www-form-urlencoded'},
//   })
// }

//清除微信交换次数
export function clearWechatExchangeTimes(uid, env) {
  return request({
    url: '/dianzhang/ClearWechatExchangeTimes?uid=' + uid + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//清除微信修改次数
export function clearWechatModifyTimes(uid, env) {
  return request({
    url: '/dianzhang/ClearWechatModifyTimes?uid=' + uid + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//清除wap验证码次数
export function clearWapVerificationCode(phone, env) {
  return request({
    url: '/dianzhang/ClearWapVerificationCode?phone=' + phone + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//保存cookie
export function savecookie(parameter) {
  return request({
    url: '/savecookie',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(parameter)
  })
}

//提现重置密码
export function DzWithdrawalresetpassword(parameter) {
  return request({
    url: '/DzWithdrawalresetpassword',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(parameter)
  })
}

//提现重置密码
export function ChangeWeChataccountbinding(parameter) {
  return request({
    url: '/ChangeWeChataccountbinding',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(parameter)
  })
}
//缓存检测
export function CacheCheck(parameter) {
  return request({
    url: '/CacheCheck',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(parameter)
  })
}

//清除缓存
export function Clearcache(parameter) {
  return request({
    url: '/Clearcache',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(parameter)
  })
}

export function getLastJenkins(env) {
  return request({
    url: '/Jenkins/getlastJenkins?testenv=' + env + '&zuzhi=2',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getTestEnv2(env, zhipinName, index2) {
  return request({
    url: '/Jenkins/gettestenv2?testenv=' + env + '&service=' + zhipinName + '&pageIndex=' + index2 + '&zuzhi=2',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function setCookie(cookie) {
  return request({
    url: '/Jenkins/setCookie?Cookie=' + cookie,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getDayNewInterface(projectId, pageIndex, pageSize) {
  return request({
    url: '/boss/yapi/get_interface_on_day?projectId=' + projectId + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getIntervalInterface(bTime, eTime, projectId, pageIndex, pageSize) {
  return request({
    url: '/boss/yapi/get_interval_interface?bTime=' + bTime + '&eTime=' + eTime + '&projectId=' + projectId + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getSecurityId(userId, identity) {
  return request({
    url: '/boss/pass/getSecurityId?userId=' + userId + '&identity=' + identity,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getFormId(formId) {
  return request({
    url: '/boss/pass/getFormId?formId=' + formId,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getServerName() {
  return request({
    url: '/boss/yapi/get_all_project',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function addUserBlock(params) {
  return request({
    url: '/boss/new/monitor',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: params
  })
}

export function queryMonitor(type, value) {
  return request({
    url: '/boss/new/monitor/admin',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: { method: 'GET', type: type, value: value }
  })
}

export function isAutoInterfaceByPath(path, method) {
  return request({
    url: '/tools/online/interface/isAutoInterfaceByPath?path=' + path + '&method=' + method,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getRoleList() {
  return request({
    url: '/boss/authorisation/get_role_list',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function bindUserRole(parameter) {
  return request({
    url: '/boss/authorisation/bind_user_role',
    method: 'post',
    data: parameter
  })
}

//获取简历id
export function getResumeId(geekId) {
  return request({
    url: 'boss/resume/get_resume_id?geekId=' + geekId,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//获取 好友列表
export function getGeekList(bossId, count) {
  return request({
    url: 'boss/resume/get_geek_list?bossId=' + bossId + '&count=' + count,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function sendResume(parameter) {
  return request({
    url: '/boss/resume/send_resume',
    method: 'post',
    data: parameter
  })
}

export function getUserLoginInfo(geekId) {
  return request({
    url: 'boss/web/get_login_by_id?userId=' + geekId,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function delAllFriend(bossId) {
  return request({
    url: 'boss/resume/del_geek_list?bossId=' + bossId,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function acceptResume(bossId, geekId) {
  return request({
    url: 'boss/resume/accept_resume?bossId=' + bossId + '&geekId=' + geekId,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getSecurityIdV2(type, userId, identity, expectId, jobId, bossId) {
  return request({
    url: '/boss/pass/getSecurityIdV2',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
      userId: userId,
      expectId: expectId,
      identity: identity,
      type: type,
      jobId: jobId,
      bossId: bossId
    }
  })
}

export function decryptSecurityId(securityId) {
  return request({
    url: '/boss/pass/decryptSecurityId',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
      securityId: securityId
    }
  })
}

export function originalRefund(env, userId) {
  return request({
    url: '/tools/business/refund',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
      env: env,
      userId: userId
    }
  })
}

export function getAllServers(params) {
  return request({
    url: '/tools/check/get_all_servers',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: params
  })
}

export function getServerHealth(appName, systemName, env) {
  return request({
    url: '/tools/check/get_all_server_group_list',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
      appName: appName,
      systemName: systemName,
      env: env
    }
  })
}

export function updateServer(data) {
  return request({
    url: '/tools/check/update_server',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data
  })
}

export function insertServer(data) {
  return request({
    url: '/tools/check/insert_server',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data
  })
}

export function updateServerStatus(env) {
  return request({
    url: '/tools/check/updateServerStatus',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
      env: env
    }
  })
}

export function submitFeed(data) {
  return request({
    url: '/boss/tools/feed',
    method: 'post',
    data
  })
}
