import request from '@/utils/request'
import qs from 'qs'

//添加
export function addWorkTask(data) {
  return request({
    url: '/boss/workTask/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function editWorkTask(data) {
  return request({
    url: '/boss/workTask/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteWorkTask(id, name) {
  return request({
    url: '/boss/workTask/delete',
    method: 'get',
    params: {
      id,
      name
    }
  })
}

//获取
export function getWorkTaskById(id) {
  return request({
    url: '/boss/workTask/ById',
    method: 'get',
    params: {
      id
    }
  })
}

//获取
export function getTask(keyword, type, difficulty, platform, priority, director, startTime, endTime, updateStartTime, updateEndTime, pageIndex, pageSize) {
  return request({
    url: '/boss/workTask/list',
    method: 'get',
    params: {
      keyword,
      type,
      difficulty,
      platform,
      priority,
      director,
      startTime,
      endTime,
      updateStartTime,
      updateEndTime,
      pageIndex,
      pageSize
    }
  })
}

export function gitKpi(director) {
  return request({
    url: '/boss/workTask/get_kpi_by_name?director=' + director,
    method: 'get'
  })
}

export function gitAllScore(createTime) {
  return request({
    url: '/boss/workTask/get_score_by_Time?createTime=' + createTime,
    method: 'get'
  })
}

//移动
export function moveWorkTaskTask(frontId, afterId, type, name) {
  return request({
    url: '/boss/workTask/move',
    method: 'get',
    params: {
      frontId,
      afterId,
      type,
      name
    }
  })
}

//上传图片
export function imgAdd(data) {
  return request({
    url: '/common/upload',
    method: 'post',
    // headers: { 'content-type': 'multipart/form-data' },
    data: data
  })
}
