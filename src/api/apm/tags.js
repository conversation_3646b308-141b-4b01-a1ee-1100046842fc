import request from '@/utils/request'
import qs from 'qs'

//添加标签
export function addTag(data) {
  return request({
    url: '/boss/apmTags/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//编辑标签
export function updateTag(data) {
  return request({
    url: '/boss/apmTags/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除标签
export function deleteTag(id) {
  return request({
    url: '/boss/apmTags/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//按照特定条件查找标签id
export function listBy(message, urlMessage) {
  return request({
    url: '/boss/apmTags/listBy',
    method: 'get',
    params: {
      message,
      urlMessage
    }
  })
}

//查询单个标签
export function getTagById(id) {
  return request({
    url: '/boss/apmTags/listById',
    method: 'get',
    params: {
      id
    }
  })
}

//标签列表
export function getTagList(message, urlMessage, tags, appType, describes, author, pageIndex, pageSize) {
  return request({
    url: '/boss/apmTags/lists',
    method: 'get',
    params: {
      message,
      urlMessage,
      tags,
      appType,
      describes,
      author,
      pageIndex,
      pageSize
    }
  })
}
