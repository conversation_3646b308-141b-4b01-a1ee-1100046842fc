import request from '@/utils/request'
import qs from 'qs'

//获取爬虫外层数据
export function getList(taskId, userId, requestUrl, identity, version, deviceVersion, netType, appType, onceTag, message, startTime, endTime, pageIndex, pageSize) {
  return request({
    url: '/boss/apmCrawler/list',
    method: 'get',
    params: {
      taskId,
      userId,
      requestUrl,
      identity,
      version,
      deviceVersion,
      netType,
      appType,
      onceTag,
      message,
      startTime,
      endTime,
      pageIndex,
      pageSize
    }
  })
}

//获取全部版本号
export function getListFilter(appType) {
  return request({
    url: '/boss/apmCrawler/listFilter',
    method: 'get',
    params: {
      appType
    }
  })
}

//获取时间戳
export function getTimeStamp(startTime, endTime, appType, taskId) {
  return request({
    url: '/boss/apmCrawler/listTime',
    method: 'get',
    params: {
      startTime,
      endTime,
      appType,
      taskId
    }
  })
}

//获取全部TaskName
export function getTaskName(appType) {
  return request({
    url: '/boss/apmCrawler/listTask',
    method: 'get',
    params: {
      appType
    }
  })
}

//通过id查询item
export function getListById(id) {
  return request({
    url: '/boss/apmCrawler/listById',
    method: 'get',
    params: {
      id
    }
  })
}

//获取爬虫内层数据
export function getListDetail(taskId, userId, requestUrl, identity, version, deviceVersion, netType, message, appType, onceTag, startTime, endTime, pageIndexDetail, pageSize) {
  return request({
    timeout: 100000,
    url: '/boss/apmCrawler/listDetail',
    method: 'get',
    params: {
      taskId,
      userId,
      requestUrl,
      identity,
      version,
      deviceVersion,
      netType,
      message,
      appType,
      onceTag,
      startTime,
      endTime,
      pageIndexDetail,
      pageSize
    }
  })
}

//过滤列表
export function getFilterListResult(filterNameKey, filterName, service, filterStatus, pageIndex, pageSize) {
  return request({
    url: '/boss/apmFilter/list',
    method: 'get',
    params: {
      filterNameKey,
      filterName,
      service,
      filterStatus,
      pageIndex,
      pageSize
    }
  })
}

//skip跳转
export function skipYapi(q) {
  return request({
    url: '/skip/Yapi',
    method: 'get',
    params: {
      q
    }
  })
}

// 获取任务列表
export function getTaskList(id, taskName, client, pageIndex, pageSize) {
  return request({
    url: '/boss/crawler/list',
    method: 'get',
    params: {
      id,
      taskName,
      client,
      pageIndex,
      pageSize
    },
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//删除任务
export function delTask(id) {
  return request({
    url: '/boss/crawler/delete?id=' + id,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//新增任务
export function newTask(data) {
  return request({
    url: '/boss/crawler/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新任务
export function updateTask(data) {
  return request({
    url: '/boss/crawler/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

// 获取任务日志
export function getTaskLogList(id, taskName, appType, startTime, endTime, pageIndex, pageSize) {
  return request({
    url: '/boss/crawler/get_log_list',
    method: 'get',
    params: {
      id,
      taskName,
      appType,
      startTime,
      endTime,
      pageIndex,
      pageSize
    }
  })
}

//获取
export function getAlarm(taskId, taskName, pageIndex, pageSize) {
  return request({
    url: '/boss/apmAlarm/list',
    method: 'get',
    params: {
      taskId,
      taskName,
      pageIndex,
      pageSize
    }
  })
}

//添加
export function addAlarm(data) {
  return request({
    url: '/boss/apmAlarm/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function editAlarm(data) {
  return request({
    url: '/boss/apmAlarm/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteAlarm(id) {
  return request({
    url: '/boss/apmAlarm/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//获取所有任务
export function getAllTask() {
  return request({
    url: '/boss/crawler/get_all_task',
    method: 'get'
  })
}
export function updateAlarmStatus(taskId, status) {
  return request({
    url: '/boss/apmAlarm/update_alarm_status?taskId=' + taskId + '&status=' + status,
    method: 'get'
  })
}

// 获取任务日志
export function getApmFilterList(id) {
  return request({
    url: '/boss/crawler/get/filter',
    method: 'get',
    params: {
      id
    }
  })
}

export function searchTaskDistribution() {
  return request({
    url: '/boss/apmStistics/task/distribution',
    method: 'get',
    params: {}
  })
}

export function searchLogDistribution() {
  return request({
    url: '/boss/apmStistics/log/distribution',
    method: 'get',
    params: {}
  })
}
