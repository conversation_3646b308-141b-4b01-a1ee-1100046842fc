import request from '@/utils/request'
import qs from 'qs'

//过滤列表
export function getFilterList(filterNameKey, filterName, service, filterStatus, pageIndex, pageSize) {
  return request({
    url: '/boss/apmFilter/list',
    method: 'get',
    params: {
      filterNameKey,
      filterName,
      service,
      filterStatus,
      pageIndex,
      pageSize
    }
  })
}

//添加过滤
export function addFilter(data) {
  return request({
    url: '/boss/apmFilter/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//编辑过滤
export function updateFilter(data) {
  return request({
    url: '/boss/apmFilter/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteFilter(id) {
  return request({
    url: '/boss/apmFilter/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//查询单个过滤条件
export function getFilter(id) {
  return request({
    url: '/boss/apmFilter/listById',
    method: 'get',
    params: {
      id
    }
  })
}
