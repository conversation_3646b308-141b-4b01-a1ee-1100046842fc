import request from '@/utils/request'
import qs from 'qs'

//添加
export function addSceneCollection(data) {
  return request({
    url: '/boss/dataFactory/sceneCollection/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//查询
export function getSceneCollection(name, email, keyword, status, type, service, pageIndex, pageSize) {
  return request({
    url: '/boss/dataFactory/sceneCollection/list',
    method: 'get',
    params: {
      name,
      email,
      keyword,
      status,
      type,
      service,
      pageIndex,
      pageSize
    }
  })
}

//删除
export function deleteSceneCollection(sceneId, name) {
  return request({
    url: '/boss/dataFactory/sceneCollection/delete',
    method: 'get',
    params: {
      sceneId,
      name
    }
  })
}
