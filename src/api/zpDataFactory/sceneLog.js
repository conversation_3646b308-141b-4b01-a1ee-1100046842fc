import request from '@/utils/request'
import qs from 'qs'

//获取
export function getSceneLog(sceneId, serialId, env, userId, phone, pageIndex, pageSize) {
  return request({
    url: '/boss/dataFactory/SceneLog/list',
    method: 'get',
    params: {
      sceneId,
      serialId,
      env,
      userId,
      phone,
      pageIndex,
      pageSize
    }
  })
}

//获取
export function getExecuteLog(sceneId, serialId, executor, startTime, endTime, pageIndex, pageSize) {
  return request({
    url: '/boss/dataFactory/SceneLog/executeList',
    method: 'get',
    params: {
      sceneId,
      serialId,
      executor,
      startTime,
      endTime,
      pageIndex,
      pageSize
    }
  })
}

//获取
export function getserialIdBySceneId(sceneId, env) {
  return request({
    url: '/boss/dataFactory/SceneLog/getserialIdBySceneId',
    method: 'get',
    params: {
      sceneId,
      env
    }
  })
}

//删除
export function deleteSceneLog(serialId) {
  return request({
    url: '/boss/dataFactory/SceneLog/deleteSceneLog',
    method: 'get',
    params: {
      serialId
    }
  })
}
