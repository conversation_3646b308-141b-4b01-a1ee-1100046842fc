import request from '@/utils/request'
import qs from 'qs'

//获取
export function getSceneName(name, service, status, author, pageIndex, pageSize) {
  return request({
    url: '/boss/dataFactory/scene/list',
    method: 'get',
    params: {
      name,
      service,
      status,
      author,
      pageIndex,
      pageSize
    }
  })
}

//获取单个
export function getSceneNameById(id) {
  return request({
    url: '/boss/dataFactory/scene/listById',
    method: 'get',
    params: {
      id
    }
  })
}

//添加
export function addSceneName(data) {
  return request({
    url: '/boss/dataFactory/scene/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteSceneName(id, email) {
  return request({
    url: '/boss/dataFactory/scene/delete',
    method: 'get',
    params: {
      id,
      email
    }
  })
}

//更新
export function updateSceneName(data) {
  return request({
    url: '/boss/dataFactory/scene/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//执行
export function executeScene(data) {
  return request({
    url: '/boss/dataFactory/scene/execute',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: data,
    timeout: 60 * 1000
  })
}
