import request from '@/utils/request'
import qs from 'qs'

//获取
export function getSceneTask(sceneId, title, author, pageIndex, pageSize) {
  return request({
    url: '/boss/dataFactory/task/list',
    method: 'get',
    params: {
      sceneId,
      title,
      author,
      pageIndex,
      pageSize
    }
  })
}

//添加
export function addSceneTask(data) {
  return request({
    url: '/boss/dataFactory/task/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteSceneTask(id, name) {
  return request({
    url: '/boss/dataFactory/task/delete',
    method: 'get',
    params: {
      id,
      name
    }
  })
}

//更新
export function updateSceneTask(data) {
  return request({
    url: '/boss/dataFactory/task/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
