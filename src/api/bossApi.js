import Vue from 'vue'
import qs from 'qs'
export const bossAppVersion = ['V8.1.5', 'V8.1.4', 'V8.1.3', 'V8.1.2', 'V8.1.1', 'V8.1.0', 'V8.0.9', 'V8.0.8', 'V8.0.7', '非版本']

export const qaGroupList = [
  {
    value: '',
    label: '请选择'
  },
  {
    value: '测试一组',
    label: '测试一组（郭明）'
  },
  {
    value: '测试二组',
    label: '测试二组（彭贺庭）'
  },
  {
    value: '测试三组',
    label: '测试三组（王明静）'
  },
  {
    value: '测试四组',
    label: '测试四组（曹森泉）'
  },
  {
    value: '测试五组',
    label: '测试五组（李学庆）'
  },
  {
    value: '测试六组',
    label: '测试六组（殷志）'
  },
  {
    value: '测试八组',
    label: '测试八组（苏海峰）'
  }
]

export const shen<PERSON>henQaGroupList = [
  {
    value: '',
    label: '请选择'
  },
  {
    value: '测试一组',
    label: '测试一组'
  },
  {
    value: '测试二组',
    label: '测试二组'
  },
  {
    value: '测试三组',
    label: '测试三组'
  },
  {
    value: '测试四组',
    label: '测试四组'
  },
  {
    value: '测试五组',
    label: '测试五组'
  },
  {
    value: '测试六组',
    label: '测试六组'
  },
  {
    value: '测试八组',
    label: '测试八组'
  },
  {
    value: '看准研发组',
    label: '看准研发组'
  }
]

export const hzQaGroupList = [
  {
    value: '',
    label: '请选择'
  },
  {
    value: '测试一组',
    label: '测试一组（郭明）'
  },
  {
    value: '测试二组',
    label: '测试二组（彭贺庭）'
  },
  {
    value: '测试三组',
    label: '测试三组（王明静）'
  },
  {
    value: '测试四组',
    label: '测试四组（曹森泉）'
  },
  {
    value: '测试五组',
    label: '测试五组（李学庆）'
  },
  {
    value: '测试六组',
    label: '测试六组（殷志）'
  },
  {
    value: '测试八组',
    label: '测试八组（苏海峰）'
  },
  {
    value: '杭州测试组',
    label: '杭州测试组（叶宏钢）'
  }
]

export const developGroupList = [
  {
    value: '',
    label: '请选择'
  },
  {
    value: '研发四组',
    label: '研发四组（陈良柱）'
  },
  {
    value: '研发六组',
    label: '研发六组（刘亚平）'
  },
  {
    value: '研发九组',
    label: '研发九组（易雄）'
  },
  {
    value: '研发二十五组',
    label: '研发二十五组（魏兆辉）'
  },
  {
    value: '研发二十六组',
    label: '研发二十六组（刘亚平）'
  },
  {
    value: '研发七组',
    label: '研发七组（唐阿辉）'
  },
  {
    value: '研发八组',
    label: '研发八组（俞超飞）'
  },
  {
    value: '研发二十三组',
    label: '研发二十三组（周游）'
  },
  {
    value: '研发二十四组',
    label: '研发二十四组（俞超飞）'
  },
  {
    value: '研发一组',
    label: '研发一组（郭悦靖）'
  },
  {
    value: '研发二组',
    label: '研发二组（鲁建平）'
  },
  {
    value: '研发五组',
    label: '研发五组（廖科）'
  },
  {
    value: '研发十组',
    label: '研发十组（沈全财）'
  },
  {
    value: '研发十二组',
    label: '研发十二组（陈建锋）'
  },
  {
    value: '研发十三组',
    label: '研发十三组（徐成龙）'
  },
  {
    value: '研发十四组',
    label: '研发十四组（崔韬）'
  },
  {
    value: '研发十五组',
    label: '研发十五组（黄源）'
  },
  {
    value: '基础架构组',
    label: '基础架构组（林顺康）'
  },
  {
    value: '研发十八组',
    label: '研发十八组（陈霖）'
  },
  {
    value: '研发十九组',
    label: '研发十九组（崔培君）'
  },
  {
    value: '研发二十组',
    label: '研发二十组（张善良）'
  },
  {
    value: '研发二十二组',
    label: '研发二十二组（郑伟诗）'
  },
  {
    value: '研发二十七组',
    label: '研发二十七组（廖科）'
  },
  {
    value: '音视频研发中心',
    label: '音视频研发中心（王寅）'
  },
  {
    value: '研发二十九组',
    label: '研发二十九组（李强07）'
  },
  {
    value: '研发三十组',
    label: '研发三十组（刘静波01）'
  },
  {
    value: '研发三十四组',
    label: '研发三十四组（李川）'
  },
  {
    value: 'FE一组',
    label: 'FE一组（贾党伟）'
  },
  {
    value: 'FE二组',
    label: 'FE二组（孙世浪）'
  },
  {
    value: 'FE三组',
    label: 'FE三组（张国栋）'
  },
  {
    value: 'FE四组',
    label: 'FE四组（张凯达）'
  },
  {
    value: 'FE五组',
    label: 'FE五组（李合伟）'
  },
  {
    value: 'FE六组',
    label: 'FE六组（张艳涛）'
  },
  {
    value: 'FE七组',
    label: 'FE七组（李光）'
  },
  {
    value: 'FE八组',
    label: 'FE八组（刘军营）'
  },
  {
    value: 'FE九组',
    label: 'FE九组（吴战磊）'
  },
  {
    value: 'FE十组',
    label: 'FE十组（陈年华）'
  },
  {
    value: '产品九组',
    label: '产品九组（马昕02）'
  },
  {
    value: '产品四组',
    label: '产品四组（韩使男）'
  },
  {
    value: '用户运营一组',
    label: '用户运营一组'
  },
  {
    value: '产品五中心',
    label: '产品五中心'
  },
  {
    value: '产品二十八组',
    label: '产品二十八组（邓微）'
  },
  {
    value: '产品二十九组',
    label: '产品二十九组（金方璐）'
  },
  {
    value: '产品十五组',
    label: '产品十五组（张瑜）'
  },
  {
    value: '产品三十一组',
    label: '产品三十一组（金方璐）'
  },
  {
    value: '算法工程二十组',
    label: '算法工程二十组（王泽楷）'
  },
  {
    value: '算法工程二十三组',
    label: '算法工程二十三组（宋洋）'
  },
  {
    value: '算法工程十四组',
    label: '算法工程十四组（刘阔）'
  },
  {
    value: '数据工程二组',
    label: '数据工程二组（张洪华）'
  },
  {
    value: '策略产品四组',
    label: '策略产品四组（史骁雄）'
  },
  {
    value: '数据工程十六组',
    label: '数据工程十六组（张展华）'
  }
]

export const cascadeDevelopGroupList = {
  // value: [],
  options: [
    {
      value: '开发部',
      label: '开发部',
      children: [
        {
          value: 'Android技术中心',
          label: 'Android技术中心',
          children: [
            {
              value: '研发七组',
              label: '研发七组（唐阿辉）'
            },
            {
              value: '研发八组',
              label: '研发八组（俞超飞）'
            },
            {
              value: '研发二十三组',
              label: '研发二十三组（周游）'
            },
            {
              value: '研发二十四组',
              label: '研发二十四组（俞超飞）'
            }
          ]
        },
        {
          value: '测试中心',
          label: '测试中心',
          children: [
            {
              value: '测试一组',
              label: '测试一组（郭明）'
            },
            {
              value: '测试二组',
              label: '测试二组（彭贺庭）'
            },
            {
              value: '测试三组',
              label: '测试三组（王明静）'
            },
            {
              value: '测试四组',
              label: '测试四组（曹森泉）'
            },
            {
              value: '测试五组',
              label: '测试五组（李学庆）'
            },
            {
              value: '测试六组',
              label: '测试六组（殷志）'
            },
            {
              value: '测试八组',
              label: '测试八组（苏海峰）'
            }
          ]
        },
        {
          value: 'FE技术中心',
          label: 'FE技术中心',
          children: [
            {
              value: 'FE一组',
              label: 'FE一组（贾党伟）'
            },
            {
              value: 'FE二组',
              label: 'FE二组（孙世浪）'
            },
            {
              value: 'FE三组',
              label: 'FE三组（张国栋）'
            },
            {
              value: 'FE四组',
              label: 'FE四组（张凯达）'
            },
            {
              value: 'FE五组',
              label: 'FE五组（李合伟）'
            },
            {
              value: 'FE六组',
              label: 'FE六组（张艳涛）'
            },
            {
              value: 'FE七组',
              label: 'FE七组（李光）'
            },
            {
              value: 'FE十组',
              label: 'FE十组（陈年华）'
            }
          ]
        },
        {
          value: '后端研发一中心',
          label: '后端研发一中心',
          children: [
            {
              value: '基础架构组',
              label: '基础架构组（林顺康）'
            },
            {
              value: '研发一组',
              label: '研发一组（郭悦靖）'
            },
            {
              value: '研发二组',
              label: '研发二组（鲁建平）'
            },
            {
              value: '看准研发组',
              label: '看准研发组（陈良柱）'
            },
            {
              value: '研发五组',
              label: '研发五组（廖科）'
            },
            {
              value: '研发十组',
              label: '研发十组（沈全财）'
            },
            {
              value: '研发十二组',
              label: '研发十二组（陈建锋）'
            },
            {
              value: '研发十三组',
              label: '研发十三组（徐成龙）'
            },
            {
              value: '研发十四组',
              label: '研发十四组（崔韬）'
            },
            {
              value: '研发十五组',
              label: '研发十五组（黄源）'
            },
            {
              value: '研发十八组',
              label: '研发十八组（陈霖）'
            },
            {
              value: '研发二十二组',
              label: '研发二十二组（郑伟诗）'
            },
            {
              value: '研发二十组',
              label: '研发二十组（张善良）'
            },
            {
              value: '研发三十四组',
              label: '研发三十四组（李川）'
            }
          ]
        },
        {
          value: 'iOS技术中心',
          label: 'iOS技术中心',
          children: [
            {
              value: '研发六组',
              label: '研发六组（刘亚平）'
            },
            {
              value: '研发九组',
              label: '研发九组（易雄）'
            },
            {
              value: '研发二十五组',
              label: '研发二十五组（刘亚平）'
            }
          ]
        }
      ]
    },
    {
      value: '平台研发一部',
      label: '平台研发一部',
      children: [
        {
          value: '平台研发一中心',
          label: '平台研发一中心',
          children: [
            {
              value: '数据工程二组',
              label: '数据工程二组'
            },
            {
              value: '数据工程五组',
              label: '数据工程五组'
            },
            {
              value: '数据工程十二组',
              label: '数据工程十二组'
            },
            {
              value: '数据工程十七组',
              label: '数据工程十七组'
            },
            {
              value: '数据工程十五组',
              label: '数据工程十五组'
            },
            {
              value: '数据工程二十组',
              label: '数据工程二十组'
            },
            {
              value: '数据工程二十一组',
              label: '数据工程二十一组'
            }
          ]
        },
        {
          value: '平台研发二中心',
          label: '平台研发二中心',
          children: [
            {
              value: '大数据平台组',
              label: '大数据平台组'
            },
            {
              value: '平台研发二组',
              label: '平台研发二组'
            },
            {
              value: '平台研发三组',
              label: '平台研发三组'
            },
            {
              value: '平台研发四组',
              label: '平台研发四组'
            },
            {
              value: '数据仓库一组',
              label: '数据仓库一组'
            },
            {
              value: '数据仓库二组',
              label: '数据仓库二组'
            }
          ]
        },
        {
          value: '数据工程十七组',
          label: '数据工程十七组',
          children: [
            {
              value: '数据工程十七组',
              label: '数据工程十七组'
            }
          ]
        }
      ]
    },
    {
      value: '基础平台组',
      label: '基础平台组',
      children: [
        {
          value: '基础平台组',
          label: '基础平台组',
          children: [
            {
              value: 'SRE组',
              label: 'SRE组'
            },
            {
              value: '平台研发六组',
              label: '平台研发六组'
            },
            {
              value: '平台研发七组',
              label: '平台研发七组'
            },
            {
              value: '平台研发九组',
              label: '平台研发九组'
            },
            {
              value: '基础平台组',
              label: '基础平台组'
            }
          ]
        }
      ]
    },
    {
      value: '平台研发四中心',
      label: '平台研发四中心',
      children: [
        {
          value: '平台研发四中心',
          label: '平台研发四中心',
          children: [
            {
              value: '平台研发五组',
              label: '平台研发五组'
            },
            {
              value: '数据工程十四组',
              label: '数据工程十四组'
            },
            {
              value: '算法工程二十二组',
              label: '算法工程二十二组'
            },
            {
              value: '数据工程十八组',
              label: '数据工程十八组'
            }
          ]
        }
      ]
    },
    {
      value: '算法工程一部',
      label: '算法工程一部',
      children: [
        {
          value: '算法工程第一中心',
          label: '算法工程第一中心',
          children: [
            {
              value: '算法工程一组',
              label: '算法工程一组'
            },
            {
              value: '算法工程三组',
              label: '算法工程三组'
            },
            {
              value: '算法工程四组',
              label: '算法工程四组'
            },
            {
              value: '算法工程五组',
              label: '算法工程五组'
            },
            {
              value: '算法工程十三组',
              label: '算法工程十三组'
            },
            {
              value: '算法工程十五组',
              label: '算法工程十五组'
            },
            {
              value: '数据工程六组',
              label: '数据工程六组'
            }
          ]
        },
        {
          value: '数据工程四组',
          label: '数据工程四组',
          children: [
            {
              value: '数据工程四组',
              label: '数据工程四组'
            }
          ]
        }
      ]
    },
    {
      value: '算法工程二部',
      label: '算法工程二部',
      children: [
        {
          value: '算法工程第五中心',
          label: '算法工程第五中心',
          children: [
            {
              value: '算法工程八组',
              label: '算法工程八组'
            },
            {
              value: '算法工程十二组',
              label: '算法工程十二组'
            },
            {
              value: '算法工程十七组',
              label: '算法工程十七组'
            },
            {
              value: '算法工程十六组',
              label: '算法工程十六组'
            },
            {
              value: '算法工程二十一组',
              label: '算法工程二十一组'
            }
          ]
        },
        {
          value: '算法工程七组',
          label: '算法工程七组',
          children: [
            {
              value: '算法工程七组',
              label: '算法工程七组'
            }
          ]
        },
        {
          value: '算法工程十八组',
          label: '算法工程十八组',
          children: [
            {
              value: '算法工程十八组',
              label: '算法工程十八组'
            }
          ]
        },
        {
          value: '数据工程十五组',
          label: '数据工程十五组',
          children: [
            {
              value: '数据工程十五组',
              label: '数据工程十五组'
            }
          ]
        }
      ]
    },
    {
      value: '算法工程三部',
      label: '算法工程三部',
      children: [
        {
          value: '算法工程第二中心',
          label: '算法工程第二中心',
          children: [
            {
              value: '算法工程十一组',
              label: '算法工程十一组'
            },
            {
              value: '数据工程一组',
              label: '数据工程一组'
            }
          ]
        },
        {
          value: '算法工程十九组',
          label: '算法工程十九组',
          children: [
            {
              value: '算法工程十九组',
              label: '算法工程十九组'
            }
          ]
        }
      ]
    },
    {
      value: '基础系统中心',
      label: '基础系统中心',
      children: [
        {
          value: '基础系统中心',
          label: '基础系统中心',
          children: [
            {
              value: 'IT组',
              label: 'IT组'
            },
            {
              value: '数据库组',
              label: '数据库组'
            },
            {
              value: '平台研发八组',
              label: '平台研发八组'
            }
          ]
        }
      ]
    },
    {
      value: '音视频研发中心',
      label: '音视频研发中心',
      children: [
        {
          value: '音视频研发中心',
          label: '音视频研发中心',
          children: [
            {
              value: '研发二十九组',
              label: '研发二十九组'
            },
            {
              value: '研发三十组',
              label: '研发三十组'
            },
            {
              value: '音视频研发中心',
              label: '音视频研发中心'
            }
          ]
        }
      ]
    },
    {
      value: '算法工程第六中心',
      label: '算法工程第六中心',
      children: [
        {
          value: '算法工程第六中心',
          label: '算法工程第六中心',
          children: [
            {
              value: '产品八组',
              label: '产品八组'
            },
            {
              value: '算法工程二组',
              label: '算法工程二组'
            },
            {
              value: '算法工程十四组',
              label: '算法工程十四组'
            },
            {
              value: '算法工程十六组',
              label: '算法工程十六组'
            },
            {
              value: '数据工程九组',
              label: '数据工程九组'
            },
            {
              value: '数据工程十三组',
              label: '数据工程十三组'
            },
            {
              value: '研发十九组',
              label: '研发十九组'
            },
            {
              value: '研发二十八组',
              label: '研发二十八组'
            }
          ]
        }
      ]
    },
    {
      value: '技术安全中心',
      label: '技术安全中心',
      children: [
        {
          value: '技术安全中心',
          label: '技术安全中心',
          children: [
            {
              value: 'D一组',
              label: 'D一组'
            },
            {
              value: 'D二组',
              label: 'D二组'
            },
            {
              value: 'D三组',
              label: 'D三组'
            },
            {
              value: 'D四组',
              label: 'D四组'
            },
            {
              value: '安全研发一组',
              label: '安全研发一组'
            },
            {
              value: '安全研发二组',
              label: '安全研发二组'
            }
          ]
        }
      ]
    },
    {
      value: '杭州产研中心',
      label: '杭州产研中心',
      children: [
        {
          value: '杭州产研中心',
          label: '杭州产研中心',
          children: [
            {
              value: '数据分析十八组',
              label: '数据分析十八组'
            },
            {
              value: '测试七组',
              label: '测试七组'
            },
            {
              value: 'FE八组',
              label: 'FE八组'
            },
            {
              value: 'FE九组',
              label: 'FE九组'
            },
            {
              value: '研发二十一组',
              label: '研发二十一组'
            },
            {
              value: '研发三十一组',
              label: '研发三十一组'
            },
            {
              value: '研发三十二组',
              label: '研发三十二组'
            },
            {
              value: '研发三十三组',
              label: '研发三十三组'
            }
          ]
        }
      ]
    },
    {
      value: '香港产研中心',
      label: '香港产研中心',
      children: [
        {
          value: '香港产研中心',
          label: '香港产研中心',
          children: [
            {
              value: '香港产研中心',
              label: '香港产研中心'
            }
          ]
        }
      ]
    },
    {
      value: '深圳产研中心',
      label: '深圳产研中心',
      children: [
        {
          value: '深圳产研中心',
          label: '深圳产研中心',
          children: [
            {
              value: '研发三十六组',
              label: '研发三十六组'
            },
            {
              value: '研发三十七组',
              label: '研发三十七组'
            },
            {
              value: '研发三十八组',
              label: '研发三十八组'
            },
            {
              value: 'FE十一组',
              label: 'FE十一组'
            }
          ]
        }
      ]
    },
    {
      value: 'NBG LAB',
      label: 'NBG LAB',
      children: [
        {
          value: 'NBG LAB',
          label: 'NBG LAB',
          children: [
            {
              value: '算法工程二十组',
              label: '算法工程二十组'
            },
            {
              value: '科研三组',
              label: '科研三组'
            },
            {
              value: '科研四组',
              label: '科研四组'
            },
            {
              value: '算法工程二十三组',
              label: '算法工程二十三组'
            }
          ]
        }
      ]
    },
    {
      value: '产品一部',
      label: '产品一部',
      children: [
        {
          value: '产品一中心',
          label: '产品一中心',
          children: [
            {
              value: '产品四组',
              label: '产品四组'
            },
            {
              value: '运营六组',
              label: '运营六组'
            },
            {
              value: '产品运营八组',
              label: '产品运营八组'
            },
            {
              value: '产品二十六组',
              label: '产品二十六组'
            },
            {
              value: '产品九组',
              label: '产品九组'
            }
          ]
        },
        {
          value: '产品四中心',
          label: '产品四中心',
          children: [
            {
              value: '产品运营九组',
              label: '产品运营九组'
            },
            {
              value: '策略产品二组',
              label: '策略产品二组'
            },
            {
              value: '策略产品四组',
              label: '策略产品四组'
            }
          ]
        }
      ]
    },
    {
      value: '产品二部',
      label: '产品二部',
      children: [
        {
          value: '产品二中心',
          label: '产品二中心',
          children: [
            {
              value: '产品运营一组',
              label: '产品运营一组'
            },
            {
              value: '产品五组',
              label: '产品五组'
            },
            {
              value: '产品十组',
              label: '产品十组'
            },
            {
              value: '产品十一组',
              label: '产品十一组'
            },
            {
              value: '产品二十八组',
              label: '产品二十八组'
            },
            {
              value: '商业标注组',
              label: '商业标注组'
            }
          ]
        },
        {
          value: '产品五中心',
          label: '产品五中心',
          children: [
            {
              value: '产品十五组',
              label: '产品十五组'
            },
            {
              value: '产品二十九组',
              label: '产品二十九组'
            },
            {
              value: '产品三十一组',
              label: '产品三十一组'
            },
            {
              value: '产品五中心',
              label: '产品五中心'
            }
          ]
        }
      ]
    },
    {
      value: '产品三部',
      label: '产品三部',
      children: [
        {
          value: '产品三中心',
          label: '产品三中心',
          children: [
            {
              value: '产品二组',
              label: '产品二组'
            },
            {
              value: '产品十三组',
              label: '产品十三组'
            }
          ]
        },
        {
          value: '产品九中心',
          label: '产品九中心',
          children: [
            {
              value: '产品十四组',
              label: '产品十四组'
            },
            {
              value: '产品三十组',
              label: '产品三十组'
            },
            {
              value: '产品三十二组',
              label: '产品三十二组'
            }
          ]
        },
        {
          value: '产品十二组',
          label: '产品十二组',
          children: [
            {
              value: '产品十二组',
              label: '产品十二组'
            }
          ]
        },
        {
          value: '运营七组',
          label: '运营七组',
          children: [
            {
              value: '运营七组',
              label: '运营七组'
            }
          ]
        }
      ]
    },
    {
      value: '产品八中心',
      label: '产品八中心',
      children: [
        {
          value: '产品八中心',
          label: '产品八中心',
          children: [
            {
              value: '产品二十五组',
              label: '产品二十五组'
            },
            {
              value: '产品二十组',
              label: '产品二十组'
            },
            {
              value: '设计九组',
              label: '设计九组'
            },
            {
              value: '设计十组',
              label: '设计十组'
            },
            {
              value: '产品三十四组',
              label: '产品三十四组'
            }
          ]
        }
      ]
    },
    {
      value: '产品七组',
      label: '产品七组',
      children: [
        {
          value: '产品七组',
          label: '产品七组',
          children: [
            {
              value: '产品七组',
              label: '产品七组'
            }
          ]
        }
      ]
    },
    {
      value: 'O₂ Project',
      label: 'O₂ Project',
      children: [
        {
          value: 'O₂ Project',
          label: 'O₂ Project',
          children: [
            {
              value: '产品三组',
              label: '产品三组'
            }
          ]
        }
      ]
    },
    {
      value: 'UED中心',
      label: 'UED中心',
      children: [
        {
          value: 'UED中心',
          label: 'UED中心',
          children: [
            {
              value: '设计一组',
              label: '设计一组'
            },
            {
              value: '设计二组',
              label: '设计二组'
            },
            {
              value: '设计三组',
              label: '设计三组'
            },
            {
              value: '设计四组',
              label: '设计四组'
            },
            {
              value: '设计五组',
              label: '设计五组'
            },
            {
              value: '设计六组',
              label: '设计六组'
            },
            {
              value: '设计八组',
              label: '设计八组'
            }
          ]
        }
      ]
    }
  ]
}

export const bossProjectSimpleList = [
  {
    value: '10002',
    label: 'Boss直聘for iOS'
  },
  {
    value: '11202',
    label: 'Boss直聘for Android'
  },
  {
    value: '11101',
    label: 'Boss直聘PC&H5'
  },
  {
    value: '13201',
    label: 'BOSS直聘Web有了社区'
  }
]

export const bossProjectList = [
  {
    value: '10002',
    label: 'Boss直聘for iOS'
  },
  {
    value: '11202',
    label: 'Boss直聘for Android'
  },
  {
    value: '11101',
    label: 'Boss直聘PC&H5'
  },
  {
    value: '13201',
    label: 'BOSS直聘Web有了社区'
  },
  {
    value: '13102',
    label: 'BOSS直聘-桌面端'
  },
  {
    value: '11601',
    label: '直聘网小程序'
  },
  {
    value: '14100',
    label: 'BOSS直聘for 鸿蒙NEXT'
  },
  {
    value: '13305',
    label: '蓝白融合'
  },
  {
    value: '12401',
    label: 'ATS'
  },
  {
    value: '11207',
    label: 'CRM'
  },
  {
    value: '11707',
    label: 'Fabio'
  },
  {
    value: '11605',
    label: 'Hunter'
  },
  {
    value: '12002',
    label: 'Noah'
  },
  {
    value: '13206',
    label: 'IUC'
  },
  {
    value: '13003',
    label: 'BPM'
  },
  {
    value: '12704',
    label: '面霸for PC'
  },
  {
    value: '12804',
    label: '泰坦星'
  },
  {
    value: '12601',
    label: '零售项目'
  },
  {
    value: '12500',
    label: '客服系统'
  },
  {
    value: '13212',
    label: '的卢'
  },
  {
    value: '13312',
    label: '猎头快速版'
  },
  {
    value: '13324',
    label: 'AB平台'
  },
  {
    value: '14200',
    label: '优兰达+'
  }
]

export const dianzhangProjectList = [
  {
    value: '10800',
    label: '店长直聘 for Android'
  },
  {
    value: '11002',
    label: '店长直聘 for iOS'
  },
  {
    value: '11204',
    label: '店长直聘PC&H5'
  },
  {
    value: '13316',
    label: '店长直聘非App'
  },
  {
    value: '12708',
    label: '店长灵工'
  },
  {
    value: '12717',
    label: 'BOSS管家'
  },
  {
    value: '13302',
    label: '管家经纪人 for iOS'
  },
  {
    value: '13303',
    label: '管家经纪人 for Android'
  }
]

export const kanzhunProjectList = [
  {
    value: '10701',
    label: '看准for Android'
  },
  {
    value: '11203',
    label: '看准for iOS'
  },
  {
    value: '11501',
    label: '看准PC&H5'
  },
  {
    value: '10001',
    label: 'kanzhun-wap'
  },
  {
    value: '10000',
    label: 'kanzhun-web'
  },
  {
    value: '13004',
    label: '新管家'
  }
]

export const bossHiProjectList = [
  {
    value: '11709',
    label: 'BossHi for Android'
  },
  {
    value: '11710',
    label: 'BossHi for iOS'
  },
  {
    value: '11708',
    label: 'BossHi for PC'
  },
  {
    value: '12300',
    label: 'BH审批'
  },
  {
    value: '12706',
    label: 'BH铁壁'
  }
]

export const bossUPProjectList = [
  {
    value: '12715',
    label: 'B+work'
  },
  {
    value: '12710',
    label: 'U组结算中心'
  },
  {
    value: '12717',
    label: '财税系统项目'
  }
]

export const technologyDepartmentList = [
  {
    value: '13202',
    label: '校招ATS'
  }
]

export const victoriaProjectList = [
  {
    value: '13900',
    label: 'Victoria for Android'
  },
  {
    value: '13901',
    label: 'Victoria for iOS'
  },
  {
    value: '13902',
    label: 'Victoria for PC&H5'
  }
]

export const pulangkeProjectList = [
  {
    value: '13701',
    label: '普朗克'
  }
]

export const oProjectList = [
  {
    value: '13205',
    label: 'O项目for Android'
  },
  {
    value: '13204',
    label: 'O项目for iOS'
  },
  {
    value: '13207',
    label: 'O项目WEB'
  }
]

export const innerProjectList = [
  {
    value: '13315',
    label: 'QUAKE'
  }
]

export const outerProjectList = [
  {
    value: '13320',
    label: '瞰荐职测'
  },
  {
    value: '13323',
    label: 'SRC项目'
  }
]
