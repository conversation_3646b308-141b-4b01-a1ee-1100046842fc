import request from '@/utils/request'
import qs from 'qs'

// 执行考生考试Ui自动化
export function uiAutoExamApi({ email, bcookie }) {
  return request({
    url: `/py_server/kjzc/uiauto/stuExam?email=${email}&bcookie=${bcookie}`,
    method: 'get'
  })
}

// 检查操作B端的用户是否有菜单或系统权限
export function checkMenuApi({ bcookie, encryptId }) {
  return request({
    url: `/py_server/kjzc/tools/checkMenu?encryptId=${encryptId}&bcookie=${bcookie}`,
    method: 'get'
  })
}

// 检查B端添加账号的手机号是否存在
export function checkPhoneApi({ bcookie, managerPhone }) {
  return request({
    url: `/py_server/kjzc/tools/checkPhone?managerPhone=${managerPhone}&bcookie=${bcookie}`,
    method: 'get'
  })
}

// B端添加账号
export function addAccountApi({ bcookie, manager<PERSON><PERSON>, managerPhone, managerEmail, superAdmin }) {
  return request({
    url: '/py_server/kjzc/tools/addAccount',
    method: 'post',
    data: { bcookie, managerName, managerPhone, managerEmail, superAdmin },
    headers: { 'Content-Type': 'application/json' }
  })
}

// 查询B端cookie权限下的所有考试名称及对应id数据
export function getExamNameList({ bcookie }) {
  return request({
    url: `/py_server/kjzc/tools/getExamNameList?bcookie=${bcookie}`,
    method: 'get'
  })
}

// 查询B端cookie权限下考试关联考生的监考id数据
export function getStuInvigilateID({ bcookie, examId, keyword }) {
  return request({
    url: `/py_server/kjzc/tools/getInvigilateStuId?bcookie=${bcookie}&examId=${examId}&keyword=${keyword}`,
    method: 'get'
  })
}

// 查询B端cookie权限下考试的监考id数据
export function getTeacherInvigilateID({ bcookie, examId }) {
  return request({
    url: `/py_server/kjzc/tools/getInvigilateTeacherId?bcookie=${bcookie}&examId=${examId}`,
    method: 'get'
  })
}
