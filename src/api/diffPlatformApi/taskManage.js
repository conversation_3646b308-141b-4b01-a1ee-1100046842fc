import req from '@/utils/request'
import request from '@/utils/request'

// export const addTaskData = params => req('post', '/diff-server/taskManage/addTaskData', params)
// export const getTaskData = params => req('post', '/diff-server/taskManage/getTaskData', params)
// export const getTaskDataById = params => req('post', '/diff-server/taskManage/getTaskDataById', params)
// export const deleteTaskData = params => req('post', '/diff-server/taskManage/deleteTaskData', params)
// export const updateTaskData = params => req('post', '/diff-server/taskManage/updateTaskData', params)

export const addTaskData = params =>
  request({
    method: 'post',
    url: '/diff-server/taskManage/addTaskData',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const getTaskData = params =>
  request({
    method: 'post',
    url: '/diff-server/taskManage/getTaskData',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const getTaskDataById = params =>
  request({
    method: 'post',
    url: '/diff-server/taskManage/getTaskDataById',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const deleteTaskData = params =>
  request({
    method: 'post',
    url: '/diff-server/taskManage/deleteTaskData',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const updateTaskData = params =>
  request({
    method: 'post',
    url: '/diff-server/taskManage/updateTaskData',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })
