import req from '@/utils/request'
import request from '@/utils/request'

// export const getInterfaceData = params => req('post', '/diff-server/interfaceManage/getInterfaceData', params)
// export const getInterfaceDataById = params => req('post', '/diff-server/interfaceManage/getInterfaceDataById', params)
// export const addOrUpdateInterface = params => req('post', '/diff-server/interfaceManage/addOrUpdateInterface', params)
// export const deleteInterfaceData = params => req('post', '/diff-server/interfaceManage/deleteInterfaceData', params)
// export const getInterfaceAddressByService = params => req('post', '/diff-server/interfaceManage/getInterfaceAddress', params)
// export const getEnvByService = params => req('post', '/diff-server/interfaceManage/getEnvDomainByService', params)
// export const getInterfaceData = params => req('post', '/web/chat/index', params)

export const getInterfaceData = params =>
  request({
    method: 'post',
    url: '/diff-server/interfaceManage/getInterfaceData',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const getInterfaceDataById = params =>
  request({
    method: 'post',
    url: '/diff-server/interfaceManage/getInterfaceDataById',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const addOrUpdateInterface = params =>
  request({
    method: 'post',
    url: '/diff-server/interfaceManage/addOrUpdateInterface',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const deleteInterfaceData = params =>
  request({
    method: 'post',
    url: '/diff-server/interfaceManage/deleteInterfaceData',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const getInterfaceAddressByService = params =>
  request({
    method: 'post',
    url: '/diff-server/interfaceManage/getInterfaceAddress',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const getEnvByService = params =>
  request({
    method: 'post',
    url: '/diff-server/interfaceManage/getEnvDomainByService',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })
