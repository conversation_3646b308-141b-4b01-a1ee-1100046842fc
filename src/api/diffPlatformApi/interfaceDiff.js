import req from '@/utils/request'
import request from '@/utils/request'

// export const executeDiffById = params => req('post', '/diff-server/interfaceDiff/interfaceDiffById', params)
// export const executeDiffResult = params => req('post', '/diff-server/interfaceDiff/diffRecordList', params)
// export const getDiffResultById = params => req('post', '/diff-server/interfaceDiff/diffResultById', params)
// export const deleteDiffRecord = params => req('post', '/diff-server/interfaceDiff/deleteDiffRecord', params)

export const executeDiffById = params =>
  request({
    method: 'post',
    url: '/diff-server/interfaceDiff/interfaceDiffById',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const executeDiffResult = params =>
  request({
    method: 'post',
    url: '/diff-server/interfaceDiff/diffRecordList',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const getDiffResultById = params =>
  request({
    method: 'post',
    url: '/diff-server/interfaceDiff/diffResultById',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const deleteDiffRecord = params =>
  request({
    method: 'post',
    url: '/diff-server/interfaceDiff/deleteDiffRecord',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })
