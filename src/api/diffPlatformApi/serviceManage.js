// import req from '../common/utils/request'
import req from '@/utils/request'
import request from '@/utils/request'
import qs from 'qs'

export const serviceNameSug = params =>
  request({
    method: 'post',
    url: '/diff-server/serviceManage/serviceNameSug',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const addOrUpdateService = params =>
  request({
    method: 'post',
    url: '/diff-server/serviceManage/addOrUpdateService',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const getServiceInfo = params =>
  request({
    method: 'post',
    url: '/diff-server/serviceManage/getServiceInfo',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const getServiceDataById = params =>
  request({
    method: 'post',
    url: '/diff-server/serviceManage/getServiceDataById',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const deleteServiceData = params =>
  request({
    method: 'post',
    url: '/diff-server/serviceManage/deleteServiceData',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })
