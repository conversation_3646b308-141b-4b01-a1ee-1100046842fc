import request from '@/utils/request'

// export const getMonitorInfoList = params => req('post', '/diff-server/monitorAnalyze/getMonitorInfoList', params)
// export const monitorDataParse = params => req('post', '/diff-server/monitorAnalyze/monitorDataParse', params)
// export const monitorDataConfigSave = params => req('post', '/diff-server/monitorAnalyze/monitorDataConfigSave', params)

export const getMonitorInfoList = params =>
  request({
    method: 'post',
    url: '/diff-server/monitorAnalyze/getMonitorInfoList',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const monitorDataParse = params =>
  request({
    method: 'post',
    url: '/diff-server/monitorAnalyze/monitorDataParse',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })

export const monitorDataConfigSave = params =>
  request({
    method: 'post',
    url: '/diff-server/monitorAnalyze/monitorDataConfigSave',
    data: params,
    headers: { 'Content-Type': 'application/json' }
  })
