import request from '@/utils/request'

/**
 * 执行boss侧自动化用例
 * @returns Promise
 */
export function uiTestStart(data) {
  return request({
    url: '/diff-server/bossUiTest/uiTestStart',
    method: 'post',
    data
  })
}

/**
 * 获取执行记录列表
 * @param {ppageNum, pageSize} 分页参数
 * @returns
 */
export function getUseCaseList(pageNum, pageSize) {
  return request({
    url: '/diff-server/bossUiTest/recordList',
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  })
}

/**
 * 获取报告详情
 * @returns Promise
 */
export function getReportDetail(reportId) {
  return request({
    url: '/diff-server/bossUiTest/getReportDetail',
    method: 'get',
    params: {
      reportId
    }
  })
}
