import request from '@/utils/request'
import qs from 'qs'

// 使用现有的脚本执行接口
export function executeScript(data) {
  return request({
    url: '/api/agent/autoCase/execute', // 使用现有的用例执行接口
    method: 'post',
    data
  })
}

// 调试脚本
export function debugScript(data) {
  return request({
    url: '/api/automated-testing/debug',
    method: 'post',
    data
  })
}

// 单步执行
export function stepExecute(data) {
  return request({
    url: '/api/automated-testing/step',
    method: 'post',
    data
  })
}

// 获取页面截图
export function captureScreenshot(data) {
  return request({
    url: '/api/automated-testing/screenshot',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 验证选择器
export function validateSelector(data) {
  return request({
    url: '/api/automated-testing/validate-selector',
    method: 'post',
    data
  })
}

// 获取页面元素
export function getPageElements(data) {
  return request({
    url: '/api/automated-testing/elements',
    method: 'post',
    data
  })
}

// 录制操作
export function startRecording(data) {
  return request({
    url: '/api/automated-testing/record/start',
    method: 'post',
    data
  })
}

// 停止录制
export function stopRecording(data) {
  return request({
    url: '/api/automated-testing/record/stop',
    method: 'post',
    data
  })
}

// 获取录制的脚本
export function getRecordedScript(sessionId) {
  return request({
    url: `/api/automated-testing/record/${sessionId}`,
    method: 'get'
  })
}

// 格式化脚本 - 前端实现
export function formatScript(script) {
  try {
    // 简单的JavaScript代码格式化
    return Promise.resolve({
      data: {
        formattedScript: script
          .replace(/;/g, ';\n')
          .replace(/{/g, '{\n  ')
          .replace(/}/g, '\n}')
          .replace(/\n\s*\n/g, '\n')
      }
    })
  } catch (error) {
    return Promise.reject(error)
  }
}

// 获取脚本模板 - 前端静态数据
export function getScriptTemplates() {
  const templates = [
    {
      id: 1,
      name: '基础页面测试',
      description: '基本的页面导航和元素交互',
      category: 'basic',
      script: `const { test, expect } = require('@playwright/test');

test('基础页面测试', async ({ page }) => {
  // 导航到页面
  await page.goto('https://example.com');
  
  // 等待页面加载
  await page.waitForLoadState('networkidle');
  
  // 验证页面标题
  await expect(page).toHaveTitle(/Example/);
  
  // 点击元素
  await page.click('text=Click me');
  
  // 验证结果
  await expect(page.locator('.result')).toBeVisible();
});`
    },
    {
      id: 2,
      name: '表单填写测试',
      description: '表单输入和提交验证',
      category: 'form',
      script: `const { test, expect } = require('@playwright/test');

test('表单填写测试', async ({ page }) => {
  await page.goto('https://example.com/form');
  
  // 填写表单
  await page.fill('input[name="username"]', 'testuser');
  await page.fill('input[name="password"]', 'password123');
  await page.selectOption('select[name="country"]', 'China');
  await page.check('input[type="checkbox"]');
  
  // 提交表单
  await page.click('button[type="submit"]');
  
  // 验证提交结果
  await expect(page.locator('.success-message')).toBeVisible();
});`
    },
    {
      id: 3,
      name: 'API拦截测试',
      description: '拦截和模拟网络请求',
      category: 'api',
      script: `const { test, expect } = require('@playwright/test');

test('API拦截测试', async ({ page }) => {
  // 拦截API请求
  await page.route('**/api/users', route => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([
        { id: 1, name: 'Test User' }
      ])
    });
  });
  
  await page.goto('https://example.com');
  
  // 验证模拟数据显示
  await expect(page.locator('text=Test User')).toBeVisible();
});`
    }
  ]

  return Promise.resolve({
    data: templates
  })
}

// 保存脚本草稿 - 使用localStorage
export function saveScriptDraft(data) {
  try {
    const drafts = JSON.parse(localStorage.getItem('scriptDrafts') || '[]')
    const draft = {
      id: Date.now(),
      name: data.name || '未命名草稿',
      script: data.script,
      createdAt: new Date().toISOString()
    }
    drafts.push(draft)
    localStorage.setItem('scriptDrafts', JSON.stringify(drafts))

    return Promise.resolve({
      data: { id: draft.id }
    })
  } catch (error) {
    return Promise.reject(error)
  }
}

// 获取脚本草稿列表
export function getScriptDrafts() {
  try {
    const drafts = JSON.parse(localStorage.getItem('scriptDrafts') || '[]')
    return Promise.resolve({
      data: drafts
    })
  } catch (error) {
    return Promise.reject(error)
  }
}

// 获取单个脚本草稿
export function getScriptDraft(draftId) {
  try {
    const drafts = JSON.parse(localStorage.getItem('scriptDrafts') || '[]')
    const draft = drafts.find(d => d.id === draftId)
    return Promise.resolve({
      data: draft
    })
  } catch (error) {
    return Promise.reject(error)
  }
}

// 删除脚本草稿
export function deleteScriptDraft(draftId) {
  try {
    const drafts = JSON.parse(localStorage.getItem('scriptDrafts') || '[]')
    const filteredDrafts = drafts.filter(d => d.id !== draftId)
    localStorage.setItem('scriptDrafts', JSON.stringify(filteredDrafts))

    return Promise.resolve({
      data: { success: true }
    })
  } catch (error) {
    return Promise.reject(error)
  }
}

// 生成选择器建议 - 前端实现
export function generateSelector(element) {
  const suggestions = []

  if (element.id) {
    suggestions.push(`#${element.id}`)
  }

  if (element.className) {
    suggestions.push(`.${element.className.split(' ').join('.')}`)
  }

  if (element.tagName) {
    suggestions.push(element.tagName.toLowerCase())
  }

  if (element.textContent) {
    suggestions.push(`text="${element.textContent.trim()}"`)
  }

  return Promise.resolve({
    data: { suggestions }
  })
}

// 语法检查 - 前端实现
export function checkSyntax(script) {
  try {
    // 简单的语法检查
    const errors = []

    // 检查基本的语法错误
    if (!script.includes('test(')) {
      errors.push({ line: 1, message: '缺少 test() 函数定义' })
    }

    if (!script.includes('async ({ page })')) {
      errors.push({ line: 1, message: '缺少 page 参数' })
    }

    // 检查括号匹配
    const openBrackets = (script.match(/\{/g) || []).length
    const closeBrackets = (script.match(/\}/g) || []).length
    if (openBrackets !== closeBrackets) {
      errors.push({ line: 1, message: '括号不匹配' })
    }

    return Promise.resolve({
      data: {
        valid: errors.length === 0,
        errors
      }
    })
  } catch (error) {
    return Promise.reject(error)
  }
}

// 获取执行日志
export function getExecutionLogs(sessionId) {
  return request({
    url: `/api/automated-testing/logs/${sessionId}`,
    method: 'get'
  })
}

// 实时获取变量值
export function getVariableValues(data) {
  return request({
    url: '/api/automated-testing/variables',
    method: 'post',
    data
  })
}

// 脚本优化建议 - 前端分析
export function analyzeScript(script) {
  const suggestions = []

  // 检查是否有等待
  if (!script.includes('waitFor')) {
    suggestions.push({
      type: 'warning',
      category: '稳定性',
      message: '建议添加适当的等待，提高脚本稳定性',
      fix: 'await page.waitForLoadState("networkidle");'
    })
  }

  // 检查是否有断言
  if (!script.includes('expect')) {
    suggestions.push({
      type: 'info',
      category: '验证',
      message: '建议添加断言来验证测试结果',
      fix: 'await expect(page.locator("selector")).toBeVisible();'
    })
  }

  // 检查是否有注释
  if (!script.includes('//')) {
    suggestions.push({
      type: 'info',
      category: '可读性',
      message: '建议添加注释说明测试步骤',
      fix: '// 添加描述性注释'
    })
  }

  return Promise.resolve({
    data: { suggestions }
  })
}
