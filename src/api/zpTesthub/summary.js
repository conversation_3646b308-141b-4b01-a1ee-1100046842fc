import request from '@/utils/request'
import qs from 'qs'

//添加
export function addSummary(data) {
  return request({
    url: '/boss/testhub/summary/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function updateSummary(data) {
  return request({
    url: '/boss/testhub/summary/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteSummary(id, libraryId, email) {
  return request({
    url: '/boss/testhub/summary/delete',
    method: 'get',
    params: {
      id,
      libraryId,
      email
    }
  })
}

//获取
export function getSummaryList(id, type, libraryId, version, grouping, creator, name, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/summary/list',
    method: 'get',
    params: {
      id,
      type,
      libraryId,
      version,
      grouping,
      creator,
      name,
      pageIndex,
      pageSize
    }
  })
}
