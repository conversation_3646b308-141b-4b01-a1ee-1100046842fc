import request from '@/utils/request'
import qs from 'qs'

export function getErrorReport(query) {
  return request({
    url: '/boss/fastbot/get_error_report',
    method: 'get',
    params: query
  })
}

export function editStatus(query) {
  return request({
    url: '/boss/fastbot/edit_status',
    method: 'post',
    data: query
  })
}
export function getDetailReport(serialNo) {
  return request({
    url: '/boss/fastbot/get_report_detail?serialNo=' + serialNo,
    method: 'get'
  })
}

export function getDevices() {
  return request({
    url: '/boss/fastbot/get_device_list',
    method: 'get'
  })
}

export function updateDeviceStatus(status, deviceId) {
  return request({
    url: '/boss/fastbot/update_device_status?status=' + status + '&deviceId=' + deviceId,
    method: 'get'
  })
}

export function debugFastBot(data) {
  return request({
    url: '/boss/fastbot/run_debug',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data
  })
}

export function getPageListByActivity(params) {
  return request({
    url: '/boss/fastbot/pages',
    method: 'get',
    params: params
  })
}

export function updatePage(id, tag, status, updateUser) {
  return request({
    url: '/boss/fastbot/pages/' + id + '/update?tag=' + tag + '&status=' + status + '&updateUser=' + updateUser,
    method: 'get'
  })
}

export function addCase(data) {
  return request({
    url: '/boss/fastbot/fastbot_cases',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data
  })
}

export function updateCase(data) {
  return request({
    url: '/boss/fastbot/fastbot_cases/update',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data
  })
}

export function searchCase(params) {
  return request({
    url: '/boss/fastbot/fastbot_cases/search',
    method: 'get',
    params: params
  })
}

export function delCase(id) {
  return request({
    url: '/boss/fastbot/fastbot_cases/delete?id=' + id,
    method: 'get'
  })
}

export function autoDataStatistics() {
  return request({
    url: '/boss/auto/home/<USER>',
    method: 'get'
  })
}
