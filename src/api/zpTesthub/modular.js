import request from '@/utils/request'
import qs from 'qs'

//添加
export function addModular(data) {
  return request({
    url: '/boss/testhub/modular/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取
export function getModular(libraryId, type, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/modular/list',
    method: 'get',
    params: {
      libraryId,
      type,
      pageIndex,
      pageSize
    }
  })
}

//获取所有父级模块
export function getAllParentList(modularId) {
  return request({
    url: '/boss/testhub/modular/allParent/list',
    method: 'get',
    params: {
      modularId
    }
  })
}

//删除
export function deleteModular(id, creator) {
  return request({
    url: '/boss/testhub/modular/delete',
    method: 'get',
    params: {
      id,
      creator
    }
  })
}

//更新
export function updateModular(data) {
  return request({
    url: '/boss/testhub/modular/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//模块下添加wiki需求
export function addModularByWike(data) {
  return request({
    url: '/boss/testhub/modular/add/wiki',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//移动分组
export function moveModular(frontId, afterId, type) {
  return request({
    url: '/boss/testhub/modular/move',
    method: 'get',
    params: {
      frontId,
      afterId,
      type
    }
  })
}

//复制测试模块
export function testhubCopyModular(data) {
  return request({
    url: '/boss/testhub/modular/copyModule',
    method: 'post',
    data: data
  })
}
