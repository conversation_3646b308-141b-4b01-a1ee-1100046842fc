import request from '@/utils/request'
import qs from 'qs'

//获取
export function getGrayReleaseIssue(id, grayId, keyword, result, level, developerEmail, author, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/grayIssue/list',
    method: 'get',
    params: {
      id,
      grayId,
      keyword,
      result,
      level,
      developerEmail,
      author,
      pageIndex,
      pageSize
    }
  })
}

//添加
export function addGrayReleaseIssue(data) {
  return request({
    url: '/boss/testhub/grayIssue/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteGrayReleaseIssue(id, email) {
  return request({
    url: '/boss/testhub/grayIssue/delete',
    method: 'get',
    params: {
      id,
      email
    }
  })
}

//更新
export function updateGrayReleaseIssue(data) {
  return request({
    url: '/boss/testhub/grayIssue/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取
export function noticeGrayReleaseIssue(grayId) {
  return request({
    url: '/boss/testhub/grayIssue/notice',
    method: 'get',
    params: {
      grayId
    }
  })
}

//获取-统计
export function getPlatformStatistics(libraryId, platform, version, name) {
  return request({
    url: '/boss/testhub/grayIssue/problemStatistics',
    method: 'get',
    params: {
      libraryId,
      platform,
      version,
      name
    }
  })
}

//获取-列表
export function getPlatformStatisticsList(libraryId, platform, version, name, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/grayIssue/problemStatisticslist',
    method: 'get',
    params: {
      libraryId,
      platform,
      version,
      name,
      pageIndex,
      pageSize
    }
  })
}

//通知
// export function qaNoticeGrayReleaseIssue (grayId, checkedList) {
//     return request({
//         url: '/boss/testhub/grayIssue/qaNotice/send',
//         method: 'get',
//         params: {
//             grayId,
//             checkedList: checkedList.join(',')
//         }
//     })
// }
export function qaNoticeGrayReleaseIssue(data) {
  return request({
    url: '/boss/testhub/grayIssue/qaNotice/send',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//催办
export function sendUrging(libraryId, grayId, platform) {
  return request({
    url: '/boss/testhub/grayIssue/urging',
    method: 'get',
    params: {
      libraryId,
      grayId,
      platform
    }
  })
}
