import request from '@/utils/request'
import qs from 'qs'

//添加
export function addExecuteLog(data) {
  return request({
    url: '/boss/testhub/case/log/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//查询
export function selectCaseRecord(caseId, planId) {
  return request({
    url: '/boss/testhub/case/log/record',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
      caseId,
      planId
    }
  })
}

//执行结果统计
export function executeLogStatistics(id) {
  return request({
    url: '/boss/testhub/case/log/plan/statistics',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
      id
    }
  })
}
//每日执行结果统计
export function dailyExecutionCase(libraryId, planId) {
  return request({
    url: '/boss/testhub/case/log/plan/dailyExecutionCase/statistics',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
      libraryId,
      planId
    }
  })
}

//每个用户用例状态统计
export function casestatusStatistics(email, name) {
  return request({
    url: '/boss/testhub/case/log/casestatus/statistics',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
      email,
      name
    },
    timeout: 60 * 1000
  })
}
