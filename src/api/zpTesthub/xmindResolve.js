import request from '@/utils/request'
import qs from 'qs'

//上传
export function uploadXmind(data) {
  return request({
    url: '/boss/testhub/resolve/upload/xmind',
    method: 'post',
    headers: { 'content-type': 'multipart/form-data' },
    data: data
  })
}

export function resolveXmind(data) {
  return request({
    url: '/boss/testhub/resolve/xmind',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

export function checkXmind(data) {
  return request({
    url: '/boss/testhub/resolve/check',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

// XMind文件预处理
export function preprocessXmind(data) {
  return request({
    url: '/py_server/common/mind_predeal',
    method: 'post',
    headers: { 'content-type': 'multipart/form-data' },
    data: data,
    responseType: 'blob',
    transformResponse: [
      function (data) {
        return data
      }
    ]
  })
}
