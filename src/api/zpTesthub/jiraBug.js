import request from '@/utils/request'
import qs from 'qs'

//获取项目
export function getJiraProject() {
  return request({
    url: '/boss/testhub/jira/allProject',
    method: 'get',
    params: {}
  })
}

//获取模板
export function getJiraProjectVersion(projectKey) {
  return request({
    url: '/boss/testhub/jira/project/versions',
    method: 'get',
    params: {
      projectKey
    }
  })
}

//获取
export function getJiraProjectComponent(projectKey) {
  return request({
    url: '/boss/testhub/jira/project/component',
    method: 'get',
    params: {
      projectKey
    }
  })
}

//添加
export function addJiraBug(data) {
  return request({
    url: '/boss/testhub/jira/create',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
