import request from '@/utils/request'
import qs from 'qs'

//添加
export function addReview(data) {
  return request({
    url: '/boss/testhub/review/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取
export function getReview(id, libraryId, keyword, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/review/list',
    method: 'get',
    params: {
      libraryId,
      keyword,
      id,
      pageIndex,
      pageSize
    }
  })
}

//删除
export function deleteReview(id) {
  return request({
    url: '/boss/testhub/review/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//更新
export function updateReview(data) {
  return request({
    url: '/boss/testhub/review/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//查询评审下用例和分组
export function getReviewIndo(id, libraryId, modularId, keyword, modularType, maintainer, level, caseStatus, caseType, executeResult, executor, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/review/getReviewInfo',
    method: 'get',
    params: {
      id,
      libraryId,
      modularId,
      modularType,
      keyword,
      maintainer,
      level,
      caseStatus,
      caseType,
      executeResult,
      executor,
      pageIndex,
      pageSize
    }
  })
}

//移除用例
export function removeCases(id, cases) {
  return request({
    url: '/boss/testhub/review/removeCases',
    method: 'get',
    params: {
      cases,
      id
    }
  })
}
