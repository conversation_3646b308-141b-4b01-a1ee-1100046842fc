import request from '@/utils/request'
import qs from 'qs'

//添加
export function addCases(data) {
  return request({
    url: '/boss/testhub/case/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取
export function getCase(libraryId, modularId, id, keyword, type, maintainer, level, caseStatus, caseType, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/case/list',
    method: 'get',
    params: {
      id,
      libraryId,
      keyword,
      type,
      modularId,
      maintainer,
      level,
      caseType,
      caseStatus,
      pageIndex,
      pageSize
    }
  })
}

//获取单条用例
export function getCaseById(id) {
  return request({
    url: '/boss/testhub/case/listById',
    method: 'get',
    params: {
      id
    }
  })
}

//删除
export function deleteCase(id, name) {
  return request({
    url: '/boss/testhub/case/delete',
    method: 'get',
    params: {
      id,
      name
    }
  })
}

//更新
export function updateCases(data) {
  return request({
    url: '/boss/testhub/case/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//批量更新
export function batchUpdateCase(data) {
  return request({
    url: '/boss/testhub/case/batchUpdate',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//批量删除
export function batchDelete(data) {
  return request({
    url: '/boss/testhub/case/batchDelete',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//用例生成xmind格式
export function getXmind(libraryId, modularId, type) {
  return request({
    url: '/boss/testhub/case/xmind',
    method: 'get',
    params: {
      libraryId,
      modularId,
      type
    }
  })
}

//获取关联用例-计划
export function getRelationCase(libraryId, modularId, id, keyword, type, maintainer, level, caseStatus, caseType, planId, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/case/relationList',
    method: 'get',
    params: {
      id,
      libraryId,
      keyword,
      type,
      modularId,
      maintainer,
      level,
      caseType,
      caseStatus,
      planId,
      pageIndex,
      pageSize
    }
  })
}

//获取关联用例-评审
export function getReviewRelationCase(libraryId, modularId, id, keyword, type, maintainer, level, caseStatus, caseType, reviewId, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/caseRelation/reviewList',
    method: 'get',
    params: {
      id,
      libraryId,
      keyword,
      type,
      modularId,
      maintainer,
      level,
      caseType,
      caseStatus,
      reviewId,
      pageIndex,
      pageSize
    }
  })
}

//更新测试步骤
export function updateCaseStep(libraryId, modularId) {
  return request({
    url: '/boss/testhub/case/update/caseStep',
    method: 'get',
    params: {
      libraryId,
      modularId
    }
  })
}

//移动用例
export function moveCase(frontId, afterId, type) {
  return request({
    url: '/boss/testhub/case/move',
    method: 'get',
    params: {
      frontId,
      afterId,
      type
    }
  })
}

export function getVideoUrl() {
  return request({
    url: '/py_server/common/get_video_url',
    method: 'get'
  })
}

//更新
export function updateXmindCase(data) {
  return request({
    url: '/boss/testhub/resolve/updateCaseV2',
    method: 'post',
    data: data
  })
}
