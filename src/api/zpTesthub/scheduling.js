import request from '@/utils/request'
import qs from 'qs'

//添加
export function addScheduling(data) {
  return request({
    url: '/boss/testhub/scheduling/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteScheduling(id) {
  return request({
    url: '/boss/testhub/scheduling/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//获取
export function getScheduling(libraryId) {
  return request({
    url: '/boss/testhub/scheduling/list',
    method: 'get',
    params: {
      libraryId
    }
  })
}

//获取版本号
export function getSchedulingVersion(libraryId) {
  return request({
    url: '/boss/testhub/scheduling/list/version',
    method: 'get',
    params: {
      libraryId
    }
  })
}

//更新
export function updateScheduling(data) {
  return request({
    url: '/boss/testhub/scheduling/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取版本开始时间和结束时间
export function getVersionTime(version) {
  return request({
    url: '/boss/testhub/scheduling/time?version=' + version,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}
