import request from '@/utils/request'
import qs from 'qs'

//获取
export function getCase(libraryId, id, keyword, maintainer, level, caseStatus, caseType, operator, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/trash/list',
    method: 'get',
    params: {
      id,
      libraryId,
      keyword,
      maintainer,
      level,
      operator,
      caseType,
      caseStatus,
      pageIndex,
      pageSize
    }
  })
}

//删除
export function deleteCase(id, name) {
  return request({
    url: '/boss/testhub/trash/delete',
    method: 'get',
    params: {
      id,
      name
    }
  })
}

//批量恢复
export function batchRecovery(data) {
  return request({
    url: '/boss/testhub/trash/recovery',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
