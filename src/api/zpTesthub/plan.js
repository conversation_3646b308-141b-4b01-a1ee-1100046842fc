import request from '@/utils/request'
import qs from 'qs'

//添加
export function addPlan(data) {
  return request({
    url: '/boss/testhub/plan/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取
export function getPlan(id, libraryId, keyword, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/plan/list',
    method: 'get',
    params: {
      libraryId,
      keyword,
      id,
      pageIndex,
      pageSize
    }
  })
}

//删除
export function deletePlan(id, email) {
  return request({
    url: '/boss/testhub/plan/delete',
    method: 'get',
    params: {
      id,
      email
    }
  })
}

//更新
export function updatePlan(data) {
  return request({
    url: '/boss/testhub/plan/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//查询计划下用例和分组
export function getPlanIndo(id, libraryId, modularId, keyword, modularType, maintainer, level, caseStatus, caseType, executeResult, executor, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/plan/getPlanInfo',
    method: 'get',
    params: {
      id,
      libraryId,
      modularId,
      modularType,
      keyword,
      maintainer,
      level,
      caseStatus,
      caseType,
      executeResult,
      executor,
      pageIndex,
      pageSize
    }
  })
}

//移除用例
export function removeCases(id, cases, libraryId, email) {
  return request({
    url: '/boss/testhub/plan/removeCases',
    method: 'get',
    params: {
      cases,
      id,
      libraryId,
      email
    }
  })
}

//关联计划
export function caseRelationPlan(libraryId, caseId) {
  return request({
    url: '/boss/testhub/plan/caseRelationPlan',
    method: 'get',
    params: {
      caseId,
      libraryId
    }
  })
}
