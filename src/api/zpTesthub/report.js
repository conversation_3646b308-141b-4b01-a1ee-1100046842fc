import request from '@/utils/request'
import qs from 'qs'

//重要程度获取
export function getImportanceDistribution(libraryId, modularId, type, maintainer) {
  return request({
    url: '/boss/testhub/report/importance/distribution',
    method: 'get',
    params: {
      libraryId,
      type,
      modularId,
      maintainer
    }
  })
}

//用例状态获取
export function getCaseStatusDistribution(libraryId, modularId, type, maintainer) {
  return request({
    url: '/boss/testhub/report/caseStatus/distribution',
    method: 'get',
    params: {
      libraryId,
      type,
      modularId,
      maintainer
    }
  })
}

//用例数统计
export function getCaseMaintainerDistribution(libraryId, modularId, type, caseStatus, level) {
  return request({
    url: '/boss/testhub/report/caseMaintainer/distribution',
    method: 'get',
    params: {
      libraryId,
      type,
      modularId,
      caseStatus,
      level
    }
  })
}

//移动用例
export function getCaseCountLevelStatistics(libraryId, level, startTime, endTime) {
  return request({
    url: '/boss/testhub/report/caseLevel/distribution',
    method: 'get',
    params: {
      libraryId,
      level,
      startTime,
      endTime
    }
  })
}
