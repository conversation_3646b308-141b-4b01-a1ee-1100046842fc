import request from '@/utils/request'
import qs from 'qs'

//添加
export function addGrayReleaseCase(data) {
  return request({
    url: '/boss/testhub/grayCase/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取
export function getGrayReleaseCase(libraryId, id, platform, keyword, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/grayCase/list',
    method: 'get',
    params: {
      id,
      libraryId,
      keyword,
      platform,
      pageIndex,
      pageSize
    }
  })
}

//删除
export function deleteGrayReleaseCase(id, email) {
  return request({
    url: '/boss/testhub/grayCase/delete',
    method: 'get',
    params: {
      id,
      email
    }
  })
}

//更新
export function updateGrayReleaseCase(data) {
  return request({
    url: '/boss/testhub/grayCase/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//移动
export function moveGrayReleaseCase(libraryId, ids) {
  return request({
    url: '/boss/testhub/grayCase/move',
    method: 'get',
    params: {
      libraryId,
      ids
    }
  })
}

//关联
export function relationGrayReleaseCase(libraryId, grayId, ids, operatorEmail) {
  return request({
    url: '/boss/testhub/grayCase/relation',
    method: 'get',
    params: {
      libraryId,
      grayId,
      ids,
      operatorEmail
    }
  })
}
