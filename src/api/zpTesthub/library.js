import request from '@/utils/request'
import qs from 'qs'

//添加
export function addLibrary(data) {
  return request({
    url: '/boss/testhub/library/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取
export function getLibrary(libraryId, libraryName, email, team, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/library/list',
    method: 'get',
    params: {
      libraryId,
      libraryName,
      email,
      team,
      pageIndex,
      pageSize
    }
  })
}

//删除
export function deleteLibrary(id) {
  return request({
    url: '/boss/testhub/library/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//更新
export function updateLibrary(data) {
  return request({
    url: '/boss/testhub/library/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//查询权限
export function getPermission(libraryId, name, email) {
  return request({
    url: '/boss/testhub/library/permission',
    method: 'get',
    params: {
      libraryId,
      name,
      email
    }
  })
}
