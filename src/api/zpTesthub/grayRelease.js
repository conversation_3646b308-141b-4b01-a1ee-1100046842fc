import request from '@/utils/request'
import qs from 'qs'

//获取
export function getGrayRelease(libraryId, id, platform, keyword, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/gray/list',
    method: 'get',
    params: {
      id,
      libraryId,
      keyword,
      platform,
      pageIndex,
      pageSize
    }
  })
}

//添加
export function addGrayRelease(data) {
  return request({
    url: '/boss/testhub/gray/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//添加
export function createGrayRelease(data) {
  return request({
    url: '/boss/testhub/gray/create',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: data
  })
}

//删除
export function deleteGrayRelease(id, email) {
  return request({
    url: '/boss/testhub/gray/delete',
    method: 'get',
    params: {
      id,
      email
    }
  })
}

//更新
export function updateGrayRelease(data) {
  return request({
    url: '/boss/testhub/gray/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取
export function getGrayReleaseGroupByName(libraryId, platform) {
  return request({
    url: '/boss/testhub/gray/list/groupByName',
    method: 'get',
    params: {
      libraryId,
      platform
    }
  })
}
