import request from '@/utils/request'
import qs from 'qs'

//添加
export function addReviewLog(data) {
  return request({
    url: '/boss/testhub/review/case/log/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//统计
export function reviewLogStatistics(id) {
  return request({
    url: '/boss/testhub/review/case/log/statistics',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: {
      id
    }
  })
}
