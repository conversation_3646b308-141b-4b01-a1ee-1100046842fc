import request from '@/utils/request'
import qs from 'qs'

//添加
export function addRole(data) {
  return request({
    url: '/boss/testhub/role/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取
export function getRole(id, libraryId, username, email, role, keyword, pageIndex, pageSize) {
  return request({
    url: '/boss/testhub/role/list',
    method: 'get',
    params: {
      id,
      libraryId,
      username,
      email,
      role,
      keyword,
      pageIndex,
      pageSize
    }
  })
}

//删除
export function deleteRole(id, libraryId, email) {
  return request({
    url: '/boss/testhub/role/delete',
    method: 'get',
    params: {
      id,
      libraryId,
      email
    }
  })
}

//更新
export function updateRole(data) {
  return request({
    url: '/boss/testhub/role/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
