import request from '@/utils/request'
import qs from 'qs'

export function sendDubbo(data) {
  return request({
    url: '/boss/dubbo/doSendWithTelnet',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

export function getInterfaceName(conn) {
  return request({
    url: '/boss/dubbo/doListTelnet?conn=' + conn + '&telnetKey=ls',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getDubboInterface(zkHost, group) {
  return request({
    url: '/dubbo-invoker/get-service-name-list?zkHost=' + zkHost + '&group=' + group,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getMethodName(conn, interfaceName) {
  return request({
    url: '/boss/dubbo/doListTelnet?conn=' + conn + '&telnetKey=ls -l ' + interfaceName,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function getZhipinEnv(zhipinName, env) {
  return request({
    url: '/boss/dubbo/zhipin/address?zhipinName=' + zhipinName + '&env=' + env,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function doSendWithTelnet(data) {
  return request({
    url: '/boss/dubbo/doSendWithTelnet',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

export function reqDubbo(data) {
  return request({
    url: '/dubbo-invoker/index',
    method: 'post',
    data: data
  })
}
