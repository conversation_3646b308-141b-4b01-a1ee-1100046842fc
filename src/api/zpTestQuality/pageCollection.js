import request from '@/utils/request'
import qs from 'qs'

//添加
export function addPageCollection(data) {
  return request({
    url: '/boss/agreementCollection/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//查询
export function getPageCollection(name, email, keyword, status, type, service, pageIndex, pageSize) {
  return request({
    url: '/boss/agreementCollection/list',
    method: 'get',
    params: {
      name,
      email,
      keyword,
      status,
      type,
      service,
      pageIndex,
      pageSize
    }
  })
}

//删除
export function deletePageCollection(agreementId) {
  return request({
    url: '/boss/agreementCollection/delete',
    method: 'get',
    params: {
      agreementId
    }
  })
}
