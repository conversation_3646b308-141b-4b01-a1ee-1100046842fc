import request from '@/utils/requestAndroid'
import qs from 'qs'

//获取工单诊断列表
export function getWorkOrder(data) {
  return request({
    url: '/api/xlog/userId/analyze',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
  // return res
}

let res = {
  code: 0,
  message: 'success',
  zpData: {
    uploadData: 1721910198218,
    processes: [
      {
        pid: '15503',
        startTime: '2024-07-24 20:13:53',
        endTime: '2024-07-24 20:14:30',
        appVersion: '12.130',
        loginVersion: '1213010',
        insights: [
          {
            name: '权限诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [],
            subInsights: [
              {
                name: '定位权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [
                  {
                    tag: 'BaseInfoAnalytic',
                    pid: '15503',
                    tid: '115',
                    event: 'run_time_env',
                    time: '20:13:57.099',
                    lineNo: 342,
                    ts: 1721823237099,
                    args: {
                      NetProxy: 'false',
                      IPV6Only: 'false',
                      GPSService: 'true',
                      VPN: 'false',
                      LocPer: '1',
                      AutoTime: '1',
                      SysDark: 'false',
                      AlwaysFinishActivities: '0',
                      AppDark: 'false',
                      ExternalStorage: '144079/231604',
                      InnerStorage: '144079/231604'
                    }
                  }
                ],
                subInsights: []
              }
            ]
          },
          {
            name: '定位诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [],
            subInsights: [
              {
                name: '定位服务',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '定位权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [
                  {
                    tag: 'BaseInfoAnalytic',
                    pid: '15503',
                    tid: '115',
                    event: 'run_time_env',
                    time: '20:13:57.099',
                    lineNo: 342,
                    ts: 1721823237099,
                    args: {
                      NetProxy: 'false',
                      IPV6Only: 'false',
                      GPSService: 'true',
                      VPN: 'false',
                      LocPer: '1',
                      AutoTime: '1',
                      SysDark: 'false',
                      AlwaysFinishActivities: '0',
                      AppDark: 'false',
                      ExternalStorage: '144079/231604',
                      InnerStorage: '144079/231604'
                    }
                  }
                ],
                subInsights: []
              }
            ]
          },
          {
            name: '通知诊断',
            description: null,
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '15503',
                tid: '2*',
                event: 'push_env',
                time: '20:13:53.371',
                lineNo: 78,
                ts: 1721823233371,
                args: {
                  suggestPushType: '1'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '15503',
                tid: '186',
                event: 'push_env',
                time: '20:13:56.995',
                lineNo: 304,
                ts: 1721823236995,
                args: {
                  sysType: 'null',
                  suggestPushType: '1',
                  type: '1',
                  token: '58k6ayd1SjvNpn0BSa//OehQ43IpHsyAAItTuFOXXjjmHZC7g+g5e8ypENjJurrz'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '15503',
                tid: '115',
                event: 'push_env',
                time: '20:13:57.102',
                lineNo: 345,
                ts: 1721823237102,
                args: {
                  pushPer: 'false',
                  channel:
                    '{"Heytap PUSH":{"importance":"3","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"System Default Channel"},"boss_message_channel_id":{"importance":"4","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"聊天消息"},"channel_id_upgrade":{"importance":"2","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"升级"}}'
                }
              }
            ],
            subInsights: [
              {
                name: '通知渠道',
                description: '支持',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '通知权限',
                description: '未开启',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '类别：System Default Channel',
                description: '通知已开启 | 有声音',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '类别：聊天消息',
                description: '通知已开启 | 有声音',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '运行环境诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '15503',
                tid: '125',
                event: 'shu_zi',
                time: '20:13:53.465',
                lineNo: 92,
                ts: 1721823233465,
                args: {
                  processId: '1',
                  time: '87',
                  queryId: 'DUvBL3owcsvCyzY7zqaaPN7zun_-ZlRUfcd0RFV2Qkwzb3djc3ZDeXpZN3pxYWFQTjd6dW5fLVpsUlVmY2Qwc2h1'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '15503',
                tid: '115',
                event: 'run_time_env',
                time: '20:13:57.099',
                lineNo: 342,
                ts: 1721823237099,
                args: {
                  NetProxy: 'false',
                  IPV6Only: 'false',
                  GPSService: 'true',
                  VPN: 'false',
                  LocPer: '1',
                  AutoTime: '1',
                  SysDark: 'false',
                  AlwaysFinishActivities: '0',
                  AppDark: 'false',
                  ExternalStorage: '144079/231604',
                  InnerStorage: '144079/231604'
                }
              }
            ],
            subInsights: [
              {
                name: '数盟',
                description: '获取正常',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '网络代理设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '系统自动时间设置',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'Activity(页面)不保留活动设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'VPN设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '深色模式',
                description: '系统:未开启  App:未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'IPv6Only',
                description: '网络支持IPV4/IPV6',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '内置存储空间',
                description: '使用率：62.21%',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '外置存储空间',
                description: '使用率：62.21%',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '账号信息',
            description: '******** | BOSS',
            level: 'BASE',
            data: null,
            detailLog: [
              {
                tag: 'AccountAnalytic',
                pid: '15503',
                tid: '93',
                event: 'account_info',
                time: '20:13:53.193',
                lineNo: 38,
                ts: *************,
                args: {
                  account: 'Account{uid=********, identity=1, phone=***********, regionCode=+86, register=false,t=true,t2=false,wt=true,zpAt=false}'
                }
              }
            ],
            subInsights: []
          },
          {
            name: '启动任务耗时',
            description: '总耗时:3431ms',
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'StartupAnalytic',
                pid: '15503',
                tid: '161',
                event: 'pipeline',
                time: '20:13:53.572',
                lineNo: 124,
                ts: *************,
                args: {
                  thread: 'StartupPipeline',
                  time: '1',
                  class: 'DataKernelWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '15503',
                tid: '161',
                event: 'pipeline',
                time: '20:13:53.595',
                lineNo: 134,
                ts: 1721823233595,
                args: {
                  thread: 'StartupPipeline',
                  time: '23',
                  class: 'ApmWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '15503',
                tid: '2*',
                event: 'pipeline',
                time: '20:13:53.596',
                lineNo: 135,
                ts: 1721823233596,
                args: {
                  thread: 'main',
                  time: '0',
                  class: 'PublicInitWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '15503',
                tid: '2*',
                event: 'pipeline',
                time: '20:13:56.071',
                lineNo: 227,
                ts: 1721823236071,
                args: {
                  thread: 'main',
                  time: '2475',
                  class: 'DBUpgradeWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '15503',
                tid: '2*',
                event: 'pipeline',
                time: '20:13:56.073',
                lineNo: 230,
                ts: 1721823236073,
                args: {
                  thread: 'main',
                  time: '2',
                  class: 'ActiveWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '15503',
                tid: '161',
                event: 'pipeline',
                time: '20:13:56.088',
                lineNo: 234,
                ts: 1721823236088,
                args: {
                  thread: 'StartupPipeline',
                  time: '15',
                  class: 'BasicDataWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '15503',
                tid: '161',
                event: 'pipeline',
                time: '20:13:56.970',
                lineNo: 295,
                ts: 1721823236970,
                args: {
                  thread: 'StartupPipeline',
                  time: '881',
                  class: 'ContactWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '15503',
                tid: '2*',
                event: 'pipeline',
                time: '20:13:56.971',
                lineNo: 296,
                ts: 1721823236971,
                args: {
                  thread: 'main',
                  time: '1',
                  class: 'NetworkWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '15503',
                tid: '161',
                event: 'pipeline',
                time: '20:13:56.999',
                lineNo: 305,
                ts: 1721823236999,
                args: {
                  thread: 'StartupPipeline',
                  time: '28',
                  class: 'AppImeiWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '15503',
                tid: '161',
                event: 'pipeline',
                time: '20:13:57.000',
                lineNo: 306,
                ts: 1721823237000,
                args: {
                  thread: 'StartupPipeline',
                  time: '1',
                  class: 'AdvertWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '15503',
                tid: '2*',
                event: 'pipeline',
                time: '20:13:57.002',
                lineNo: 307,
                ts: 1721823237002,
                args: {
                  thread: 'main',
                  time: '2',
                  class: 'CompleteWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '15503',
                tid: '2*',
                event: 'pipeline',
                time: '20:13:57.002',
                lineNo: 308,
                ts: 1721823237002,
                args: {
                  totalTime: '3431'
                }
              }
            ],
            subInsights: [
              {
                name: 'DataKernelWork',
                description: '耗时:1ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ApmWork',
                description: '耗时:23ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'PublicInitWork',
                description: '耗时:0ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'DBUpgradeWork',
                description: '耗时:2475ms',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ActiveWork',
                description: '耗时:2ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'BasicDataWork',
                description: '耗时:15ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ContactWork',
                description: '耗时:881ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'NetworkWork',
                description: '耗时:1ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'AppImeiWork',
                description: '耗时:28ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'AdvertWork',
                description: '耗时:1ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'CompleteWork',
                description: '耗时:2ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '联系人存储',
            description: '异常(数据丢失)\t未完成任务个数：7541',
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:13:58.144',
                lineNo: 421,
                ts: 1721823238144,
                args: {
                  what: '11301',
                  delay: '936',
                  maxMsg: '7',
                  msgCount: '8',
                  time: '935',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:13:59.325',
                lineNo: 870,
                ts: 1721823239325,
                args: {
                  what: '11101',
                  delay: '1093',
                  maxMsg: '514',
                  msgCount: '515',
                  time: '141',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:13:59.539',
                lineNo: 1354,
                ts: 1721823239539,
                args: {
                  what: '29999',
                  delay: '1306',
                  maxMsg: '1115',
                  msgCount: '1115',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:00.134',
                lineNo: 1731,
                ts: 1721823240134,
                args: {
                  what: '11101',
                  delay: '1899',
                  maxMsg: '1545',
                  msgCount: '1546',
                  time: '131',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:00.646',
                lineNo: 2094,
                ts: 1721823240646,
                args: {
                  what: '11101',
                  delay: '2411',
                  maxMsg: '2013',
                  msgCount: '2014',
                  time: '118',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:01.320',
                lineNo: 2603,
                ts: 1721823241320,
                args: {
                  what: '29999',
                  delay: '3083',
                  maxMsg: '2500',
                  msgCount: '2500',
                  time: '0',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:01.879',
                lineNo: 3075,
                ts: 1721823241879,
                args: {
                  what: '29999',
                  delay: '3641',
                  maxMsg: '3032',
                  msgCount: '3032',
                  time: '0',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:02.281',
                lineNo: 3498,
                ts: 1721823242281,
                args: {
                  what: '29999',
                  delay: '4042',
                  maxMsg: '3537',
                  msgCount: '3537',
                  time: '0',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:02.717',
                lineNo: 4010,
                ts: 1721823242717,
                args: {
                  what: '29999',
                  delay: '4476',
                  maxMsg: '4123',
                  msgCount: '4123',
                  time: '0',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:03.410',
                lineNo: 4442,
                ts: 1721823243410,
                args: {
                  what: '11305',
                  delay: '5165',
                  maxMsg: '4685',
                  msgCount: '4686',
                  time: '324',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:04.797',
                lineNo: 4838,
                ts: 1721823244797,
                args: {
                  what: '11101',
                  delay: '6549',
                  maxMsg: '5132',
                  msgCount: '5133',
                  time: '85',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:05.135',
                lineNo: 5198,
                ts: 1721823245135,
                args: {
                  what: '11101',
                  delay: '6887',
                  maxMsg: '5563',
                  msgCount: '5564',
                  time: '119',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:05.602',
                lineNo: 5615,
                ts: 1721823245602,
                args: {
                  what: '11101',
                  delay: '7352',
                  maxMsg: '6097',
                  msgCount: '6098',
                  time: '129',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:10.741',
                lineNo: 6078,
                ts: 1721823250741,
                args: {
                  what: '11101',
                  delay: '12477',
                  maxMsg: '6502',
                  msgCount: '6503',
                  time: '155',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:11.096',
                lineNo: 6637,
                ts: 1721823251096,
                args: {
                  what: '11101',
                  delay: '12831',
                  maxMsg: '7167',
                  msgCount: '7168',
                  time: '81',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:11.432',
                lineNo: 7024,
                ts: 1721823251432,
                args: {
                  what: '11305',
                  delay: '13166',
                  maxMsg: '7606',
                  msgCount: '7607',
                  time: '114',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:29.503',
                lineNo: 8589,
                ts: 1721823269503,
                args: {
                  what: '29999',
                  delay: '30247',
                  maxMsg: '7724',
                  msgCount: '7499',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '15503',
                tid: '205',
                event: 'contact_async_unhandled_message',
                time: '20:14:29.717',
                lineNo: 8610,
                ts: 1721823269717,
                args: {
                  what: '11101',
                  delay: '30461',
                  maxMsg: '7724',
                  msgCount: '7541',
                  time: '67',
                  status: 'ing'
                }
              }
            ],
            subInsights: [
              {
                name: '耗时任务ID：11305',
                description: '延迟：13166\t耗时：114',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '耗时任务ID：29999',
                description: '延迟：30247\t耗时：1',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '耗时任务ID：11101',
                description: '延迟：30461\t耗时：67',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          }
        ],
        insightInfo:
          '进程:15503\t时间：20:13:53--20:14:30\t持续时间：37.626s\n【当前版本】\t12.130\t已登录\n【通知诊断】❗️\t\t\n\t[通知权限]\t未开启\t\n【账号信息】️\t******** | BOSS\t\n【启动任务耗时】❗️\t总耗时:3431ms\t\n\t[DBUpgradeWork]\t耗时:2475ms\t\n【联系人存储】❗️\t异常(数据丢失)\t未完成任务个数：7541\t\n\t[耗时任务ID：11305]\t延迟：13166\t耗时：114\t\n\t[耗时任务ID：29999]\t延迟：30247\t耗时：1\t\n\t[耗时任务ID：11101]\t延迟：30461\t耗时：67\t\n'
      },
      {
        pid: '26745',
        startTime: '2024-07-24 21:03:52',
        endTime: '2024-07-24 21:37:27',
        appVersion: '12.130',
        loginVersion: '1213010',
        insights: [
          {
            name: '权限诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [],
            subInsights: [
              {
                name: '定位权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [
                  {
                    tag: 'BaseInfoAnalytic',
                    pid: '26745',
                    tid: '122',
                    event: 'run_time_env',
                    time: '21:03:54.052',
                    lineNo: 8951,
                    ts: 1721826234052,
                    args: {
                      NetProxy: 'false',
                      IPV6Only: 'false',
                      GPSService: 'true',
                      VPN: 'false',
                      LocPer: '1',
                      AutoTime: '1',
                      SysDark: 'false',
                      AlwaysFinishActivities: '0',
                      AppDark: 'false',
                      ExternalStorage: '144065/231604',
                      InnerStorage: '144065/231604'
                    }
                  }
                ],
                subInsights: []
              }
            ]
          },
          {
            name: '定位诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [],
            subInsights: [
              {
                name: '定位服务',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '定位权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [
                  {
                    tag: 'BaseInfoAnalytic',
                    pid: '26745',
                    tid: '122',
                    event: 'run_time_env',
                    time: '21:03:54.052',
                    lineNo: 8951,
                    ts: 1721826234052,
                    args: {
                      NetProxy: 'false',
                      IPV6Only: 'false',
                      GPSService: 'true',
                      VPN: 'false',
                      LocPer: '1',
                      AutoTime: '1',
                      SysDark: 'false',
                      AlwaysFinishActivities: '0',
                      AppDark: 'false',
                      ExternalStorage: '144065/231604',
                      InnerStorage: '144065/231604'
                    }
                  }
                ],
                subInsights: []
              }
            ]
          },
          {
            name: '通知诊断',
            description: null,
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '26745',
                tid: '2*',
                event: 'push_env',
                time: '21:03:52.535',
                lineNo: 8758,
                ts: 1721826232535,
                args: {
                  suggestPushType: '1'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '26745',
                tid: '210',
                event: 'push_env',
                time: '21:03:53.860',
                lineNo: 8934,
                ts: 1721826233860,
                args: {
                  sysType: 'null',
                  suggestPushType: '1',
                  type: '1',
                  token: '58k6ayd1SjvNpn0BSa//OehQ43IpHsyAAItTuFOXXjjmHZC7g+g5e8ypENjJurrz'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '26745',
                tid: '122',
                event: 'push_env',
                time: '21:03:54.052',
                lineNo: 8953,
                ts: 1721826234052,
                args: {
                  pushPer: 'false',
                  channel:
                    '{"Heytap PUSH":{"importance":"3","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"System Default Channel"},"boss_message_channel_id":{"importance":"4","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"聊天消息"},"channel_id_upgrade":{"importance":"2","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"升级"}}'
                }
              }
            ],
            subInsights: [
              {
                name: '通知渠道',
                description: '支持',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '通知权限',
                description: '未开启',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '类别：System Default Channel',
                description: '通知已开启 | 有声音',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '类别：聊天消息',
                description: '通知已开启 | 有声音',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '运行环境诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '26745',
                tid: '113',
                event: 'shu_zi',
                time: '21:03:52.623',
                lineNo: 8779,
                ts: 1721826232623,
                args: {
                  processId: '1',
                  time: '81',
                  queryId: 'DUvBL3owcsvCyzY7zqaaPN7zun_-ZlRUfcd0RFV2Qkwzb3djc3ZDeXpZN3pxYWFQTjd6dW5fLVpsUlVmY2Qwc2h1'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '26745',
                tid: '122',
                event: 'run_time_env',
                time: '21:03:54.052',
                lineNo: 8951,
                ts: 1721826234052,
                args: {
                  NetProxy: 'false',
                  IPV6Only: 'false',
                  GPSService: 'true',
                  VPN: 'false',
                  LocPer: '1',
                  AutoTime: '1',
                  SysDark: 'false',
                  AlwaysFinishActivities: '0',
                  AppDark: 'false',
                  ExternalStorage: '144065/231604',
                  InnerStorage: '144065/231604'
                }
              }
            ],
            subInsights: [
              {
                name: '数盟',
                description: '获取正常',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '网络代理设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '系统自动时间设置',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'Activity(页面)不保留活动设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'VPN设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '深色模式',
                description: '系统:未开启  App:未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'IPv6Only',
                description: '网络支持IPV4/IPV6',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '内置存储空间',
                description: '使用率：62.2%',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '外置存储空间',
                description: '使用率：62.2%',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '账号信息',
            description: '******** | BOSS',
            level: 'BASE',
            data: null,
            detailLog: [
              {
                tag: 'AccountAnalytic',
                pid: '26745',
                tid: '91',
                event: 'account_info',
                time: '21:03:52.488',
                lineNo: 8739,
                ts: *************,
                args: {
                  account: 'Account{uid=********, identity=1, phone=***********, regionCode=+86, register=false,t=true,t2=false,wt=true,zpAt=false}'
                }
              }
            ],
            subInsights: []
          },
          {
            name: '启动任务耗时',
            description: '总耗时:1054ms',
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'StartupAnalytic',
                pid: '26745',
                tid: '149',
                event: 'pipeline',
                time: '21:03:52.802',
                lineNo: 8827,
                ts: *************,
                args: {
                  thread: 'StartupPipeline',
                  time: '3',
                  class: 'DataKernelWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '26745',
                tid: '149',
                event: 'pipeline',
                time: '21:03:52.829',
                lineNo: 8834,
                ts: 1721826232829,
                args: {
                  thread: 'StartupPipeline',
                  time: '27',
                  class: 'ApmWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '26745',
                tid: '2*',
                event: 'pipeline',
                time: '21:03:52.829',
                lineNo: 8835,
                ts: 1721826232829,
                args: {
                  thread: 'main',
                  time: '0',
                  class: 'PublicInitWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '26745',
                tid: '2*',
                event: 'pipeline',
                time: '21:03:52.992',
                lineNo: 8847,
                ts: 1721826232992,
                args: {
                  thread: 'main',
                  time: '163',
                  class: 'DBUpgradeWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '26745',
                tid: '2*',
                event: 'pipeline',
                time: '21:03:52.993',
                lineNo: 8850,
                ts: 1721826232993,
                args: {
                  thread: 'main',
                  time: '1',
                  class: 'ActiveWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '26745',
                tid: '149',
                event: 'pipeline',
                time: '21:03:52.997',
                lineNo: 8853,
                ts: 1721826232997,
                args: {
                  thread: 'StartupPipeline',
                  time: '4',
                  class: 'BasicDataWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '26745',
                tid: '149',
                event: 'pipeline',
                time: '21:03:53.839',
                lineNo: 8918,
                ts: 1721826233839,
                args: {
                  thread: 'StartupPipeline',
                  time: '842',
                  class: 'ContactWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '26745',
                tid: '2*',
                event: 'pipeline',
                time: '21:03:53.840',
                lineNo: 8919,
                ts: 1721826233840,
                args: {
                  thread: 'main',
                  time: '1',
                  class: 'NetworkWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '26745',
                tid: '149',
                event: 'pipeline',
                time: '21:03:53.850',
                lineNo: 8925,
                ts: 1721826233850,
                args: {
                  thread: 'StartupPipeline',
                  time: '10',
                  class: 'AppImeiWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '26745',
                tid: '149',
                event: 'pipeline',
                time: '21:03:53.850',
                lineNo: 8926,
                ts: 1721826233850,
                args: {
                  thread: 'StartupPipeline',
                  time: '0',
                  class: 'AdvertWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '26745',
                tid: '2*',
                event: 'pipeline',
                time: '21:03:53.853',
                lineNo: 8928,
                ts: 1721826233853,
                args: {
                  thread: 'main',
                  time: '3',
                  class: 'CompleteWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '26745',
                tid: '2*',
                event: 'pipeline',
                time: '21:03:53.853',
                lineNo: 8929,
                ts: 1721826233853,
                args: {
                  totalTime: '1054'
                }
              }
            ],
            subInsights: [
              {
                name: 'DataKernelWork',
                description: '耗时:3ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ApmWork',
                description: '耗时:27ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'PublicInitWork',
                description: '耗时:0ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'DBUpgradeWork',
                description: '耗时:163ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ActiveWork',
                description: '耗时:1ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'BasicDataWork',
                description: '耗时:4ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ContactWork',
                description: '耗时:842ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'NetworkWork',
                description: '耗时:1ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'AppImeiWork',
                description: '耗时:10ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'AdvertWork',
                description: '耗时:0ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'CompleteWork',
                description: '耗时:3ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '新增联系人存储异常',
            description: '未完成任务：2763个',
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_message_corruption',
                time: '21:03:56.869',
                lineNo: 9487,
                ts: 1721826236869,
                args: {
                  count: '2763'
                }
              }
            ],
            subInsights: []
          },
          {
            name: '联系人存储',
            description: '正常',
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:03:57.088',
                lineNo: 9809,
                ts: 1721826237088,
                args: {
                  what: '11101',
                  delay: '516',
                  maxMsg: '508',
                  msgCount: '509',
                  time: '18',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:03:57.278',
                lineNo: 10065,
                ts: 1721826237278,
                args: {
                  what: '11101',
                  delay: '606',
                  maxMsg: '1058',
                  msgCount: '1059',
                  time: '94',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:03:58.465',
                lineNo: 10452,
                ts: 1721826238465,
                args: {
                  what: '11101',
                  delay: '1712',
                  maxMsg: '1680',
                  msgCount: '1681',
                  time: '1167',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:03:58.921',
                lineNo: 10773,
                ts: 1721826238921,
                args: {
                  what: '11101',
                  delay: '2163',
                  maxMsg: '2005',
                  msgCount: '2006',
                  time: '405',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:03:59.627',
                lineNo: 11298,
                ts: 1721826239627,
                args: {
                  what: '11101',
                  delay: '2851',
                  maxMsg: '2617',
                  msgCount: '2618',
                  time: '537',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:00.492',
                lineNo: 11681,
                ts: 1721826240492,
                args: {
                  what: '29999',
                  delay: '3626',
                  maxMsg: '3030',
                  msgCount: '3030',
                  time: '0',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:02.463',
                lineNo: 12070,
                ts: 1721826242463,
                args: {
                  what: '11101',
                  delay: '5594',
                  maxMsg: '3573',
                  msgCount: '3574',
                  time: '237',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:04.433',
                lineNo: 12510,
                ts: 1721826244433,
                args: {
                  what: '29999',
                  delay: '7561',
                  maxMsg: '4007',
                  msgCount: '4008',
                  time: '0',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:07.413',
                lineNo: 13005,
                ts: 1721826247413,
                args: {
                  what: '11101',
                  delay: '10537',
                  maxMsg: '4570',
                  msgCount: '4571',
                  time: '139',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:09.576',
                lineNo: 13488,
                ts: 1721826249576,
                args: {
                  what: '11305',
                  delay: '12698',
                  maxMsg: '5100',
                  msgCount: '5101',
                  time: '80',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:10.799',
                lineNo: 13807,
                ts: 1721826250799,
                args: {
                  what: '11101',
                  delay: '13919',
                  maxMsg: '5374',
                  msgCount: '5375',
                  time: '721',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:30.925',
                lineNo: 15962,
                ts: 1721826270925,
                args: {
                  what: '11305',
                  delay: '33806',
                  maxMsg: '5420',
                  msgCount: '4999',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:31.222',
                lineNo: 16136,
                ts: 1721826271222,
                args: {
                  what: '11101',
                  delay: '33902',
                  maxMsg: '5420',
                  msgCount: '4499',
                  time: '0',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:31.433',
                lineNo: 16284,
                ts: 1721826271433,
                args: {
                  what: '11301',
                  delay: '32803',
                  maxMsg: '5420',
                  msgCount: '3999',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:31.648',
                lineNo: 16462,
                ts: 1721826271648,
                args: {
                  what: '29999',
                  delay: '32672',
                  maxMsg: '5420',
                  msgCount: '3499',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:31.875',
                lineNo: 16626,
                ts: 1721826271875,
                args: {
                  what: '29999',
                  delay: '31682',
                  maxMsg: '5420',
                  msgCount: '2999',
                  time: '0',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:32.117',
                lineNo: 16772,
                ts: 1721826272117,
                args: {
                  what: '11101',
                  delay: '30610',
                  maxMsg: '5420',
                  msgCount: '2499',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:32.360',
                lineNo: 16929,
                ts: 1721826272360,
                args: {
                  what: '11101',
                  delay: '29659',
                  maxMsg: '5420',
                  msgCount: '1999',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:32.584',
                lineNo: 17086,
                ts: 1721826272584,
                args: {
                  what: '11301',
                  delay: '27448',
                  maxMsg: '5420',
                  msgCount: '1499',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:32.839',
                lineNo: 17242,
                ts: 1721826272839,
                args: {
                  what: '11101',
                  delay: '25423',
                  maxMsg: '5420',
                  msgCount: '999',
                  time: '0',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:33.066',
                lineNo: 17410,
                ts: 1721826273066,
                args: {
                  what: '11101',
                  delay: '23556',
                  maxMsg: '5420',
                  msgCount: '499',
                  time: '0',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '26745',
                tid: '261',
                event: 'contact_async_unhandled_message',
                time: '21:04:33.292',
                lineNo: 17569,
                ts: 1721826273292,
                args: {
                  what: '11101',
                  delay: '2294',
                  maxMsg: '5420',
                  msgCount: '0',
                  time: '1',
                  status: 'end'
                }
              }
            ],
            subInsights: [
              {
                name: '耗时任务ID：11305',
                description: '延迟：33806\t耗时：1',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '耗时任务ID：29999',
                description: '延迟：31682\t耗时：0',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '耗时任务ID：11301',
                description: '延迟：27448\t耗时：1',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '耗时任务ID：11101',
                description: '延迟：23556\t耗时：0',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          }
        ],
        insightInfo: '进程:26745\t时间：21:03:52--21:37:27\t持续时间：2014.922s\n【当前版本】\t12.130\t已登录\n【通知诊断】❗️\t\t\n\t[通知权限]\t未开启\t\n【账号信息】️\t******** | BOSS\t\n【新增联系人存储异常】❗️\t未完成任务：2763个\t\n'
      },
      {
        pid: '24120',
        startTime: '2024-07-24 21:45:19',
        endTime: '2024-07-24 22:23:41',
        appVersion: '12.130',
        loginVersion: '1213010',
        insights: [
          {
            name: '权限诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [],
            subInsights: [
              {
                name: '定位权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [
                  {
                    tag: 'BaseInfoAnalytic',
                    pid: '24120',
                    tid: '123',
                    event: 'run_time_env',
                    time: '21:45:19.410',
                    lineNo: 27377,
                    ts: 1721828719410,
                    args: {
                      NetProxy: 'false',
                      IPV6Only: 'false',
                      GPSService: 'false',
                      VPN: 'false',
                      LocPer: '1',
                      AutoTime: '1',
                      SysDark: 'false',
                      AlwaysFinishActivities: '0',
                      AppDark: 'false',
                      ExternalStorage: '144803/231604',
                      InnerStorage: '144803/231604'
                    }
                  }
                ],
                subInsights: []
              }
            ]
          },
          {
            name: '定位诊断',
            description: null,
            level: 'WARN',
            data: null,
            detailLog: [],
            subInsights: [
              {
                name: '定位服务',
                description: '未开启',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '定位权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [
                  {
                    tag: 'BaseInfoAnalytic',
                    pid: '24120',
                    tid: '123',
                    event: 'run_time_env',
                    time: '21:45:19.410',
                    lineNo: 27377,
                    ts: 1721828719410,
                    args: {
                      NetProxy: 'false',
                      IPV6Only: 'false',
                      GPSService: 'false',
                      VPN: 'false',
                      LocPer: '1',
                      AutoTime: '1',
                      SysDark: 'false',
                      AlwaysFinishActivities: '0',
                      AppDark: 'false',
                      ExternalStorage: '144803/231604',
                      InnerStorage: '144803/231604'
                    }
                  }
                ],
                subInsights: []
              }
            ]
          },
          {
            name: '通知诊断',
            description: null,
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '24120',
                tid: '2*',
                event: 'push_env',
                time: '21:45:19.159',
                lineNo: 27304,
                ts: 1721828719159,
                args: {
                  suggestPushType: '1'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '24120',
                tid: '151',
                event: 'push_env',
                time: '21:45:19.404',
                lineNo: 27373,
                ts: 1721828719404,
                args: {
                  sysType: 'null',
                  suggestPushType: '1',
                  type: '1',
                  token: '58k6ayd1SjvNpn0BSa//OehQ43IpHsyAAItTuFOXXjjmHZC7g+g5e8ypENjJurrz'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '24120',
                tid: '123',
                event: 'push_env',
                time: '21:45:19.413',
                lineNo: 27379,
                ts: 1721828719413,
                args: {
                  pushPer: 'false',
                  channel:
                    '{"Heytap PUSH":{"importance":"3","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"System Default Channel"},"boss_message_channel_id":{"importance":"4","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"聊天消息"},"channel_id_upgrade":{"importance":"2","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"升级"}}'
                }
              }
            ],
            subInsights: [
              {
                name: '通知渠道',
                description: '支持',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '通知权限',
                description: '未开启',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '类别：System Default Channel',
                description: '通知已开启 | 有声音',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '类别：聊天消息',
                description: '通知已开启 | 有声音',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '运行环境诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '24120',
                tid: '113',
                event: 'shu_zi',
                time: '21:45:19.247',
                lineNo: 27325,
                ts: 1721828719247,
                args: {
                  processId: '1',
                  time: '81',
                  queryId: 'DUvBL3owcsvCyzY7zqaaPN7zun_-ZlRUfcd0RFV2Qkwzb3djc3ZDeXpZN3pxYWFQTjd6dW5fLVpsUlVmY2Qwc2h1'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '24120',
                tid: '123',
                event: 'run_time_env',
                time: '21:45:19.410',
                lineNo: 27377,
                ts: 1721828719410,
                args: {
                  NetProxy: 'false',
                  IPV6Only: 'false',
                  GPSService: 'false',
                  VPN: 'false',
                  LocPer: '1',
                  AutoTime: '1',
                  SysDark: 'false',
                  AlwaysFinishActivities: '0',
                  AppDark: 'false',
                  ExternalStorage: '144803/231604',
                  InnerStorage: '144803/231604'
                }
              }
            ],
            subInsights: [
              {
                name: '数盟',
                description: '获取正常',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '网络代理设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '系统自动时间设置',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'Activity(页面)不保留活动设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'VPN设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '深色模式',
                description: '系统:未开启  App:未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'IPv6Only',
                description: '网络支持IPV4/IPV6',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '内置存储空间',
                description: '使用率：62.52%',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '外置存储空间',
                description: '使用率：62.52%',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '账号信息',
            description: '******** | BOSS',
            level: 'BASE',
            data: null,
            detailLog: [
              {
                tag: 'AccountAnalytic',
                pid: '24120',
                tid: '91',
                event: 'account_info',
                time: '21:45:19.118',
                lineNo: 27285,
                ts: *************,
                args: {
                  account: 'Account{uid=********, identity=1, phone=***********, regionCode=+86, register=false,t=true,t2=false,wt=true,zpAt=false}'
                }
              }
            ],
            subInsights: []
          },
          {
            name: '新增联系人存储异常',
            description: '未完成任务：1个',
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'ChatAnalytic',
                pid: '24120',
                tid: '238',
                event: 'contact_message_corruption',
                time: '21:45:22.009',
                lineNo: 27545,
                ts: *************,
                args: {
                  count: '1'
                }
              }
            ],
            subInsights: []
          },
          {
            name: '联系人存储',
            description: '正常',
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'ChatAnalytic',
                pid: '24120',
                tid: '238',
                event: 'contact_async_unhandled_message',
                time: '21:46:59.305',
                lineNo: 27987,
                ts: 1721828819305,
                args: {
                  what: '11101',
                  delay: '7013',
                  maxMsg: '31',
                  msgCount: '1',
                  time: '7012',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '24120',
                tid: '238',
                event: 'contact_async_unhandled_message',
                time: '21:47:00.262',
                lineNo: 28029,
                ts: 1721828820262,
                args: {
                  what: '11301',
                  delay: '197',
                  maxMsg: '31',
                  msgCount: '0',
                  time: '1',
                  status: 'end'
                }
              }
            ],
            subInsights: [
              {
                name: '耗时任务ID：11101',
                description: '延迟：7013\t耗时：7012',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          }
        ],
        insightInfo: '进程:24120\t时间：21:45:19--22:23:41\t持续时间：2302.107s\n【当前版本】\t12.130\t已登录\n【定位诊断】❗️\t\t\n\t[定位服务]\t未开启\t\n【通知诊断】❗️\t\t\n\t[通知权限]\t未开启\t\n【账号信息】️\t******** | BOSS\t\n【新增联系人存储异常】❗️\t未完成任务：1个\t\n'
      },
      {
        pid: '29608',
        startTime: '2024-07-24 22:25:34',
        endTime: '2024-07-24 22:27:20',
        appVersion: '12.130',
        loginVersion: '1213010',
        insights: [
          {
            name: '权限诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '29608',
                tid: '2*',
                event: 'permission_data',
                time: '22:26:44.243',
                lineNo: 29808,
                ts: 1721831204243,
                args: {
                  request: 'EvoPermissionHelper',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.CAMERA'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '29608',
                tid: '2*',
                event: 'permission_data',
                time: '22:26:51.090',
                lineNo: 29852,
                ts: 1721831211090,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              }
            ],
            subInsights: [
              {
                name: '摄像头权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '定位权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '定位诊断',
            description: null,
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '29608',
                tid: '2*',
                event: 'location_data',
                time: '22:26:51.166',
                lineNo: 29853,
                ts: 1721831211166,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              }
            ],
            subInsights: [
              {
                name: '定位服务',
                description: '未开启',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '定位权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [
                  {
                    tag: 'BaseInfoAnalytic',
                    pid: '29608',
                    tid: '124',
                    event: 'run_time_env',
                    time: '22:25:36.241',
                    lineNo: 29297,
                    ts: 1721831136241,
                    args: {
                      NetProxy: 'false',
                      IPV6Only: 'false',
                      GPSService: 'false',
                      VPN: 'false',
                      LocPer: '1',
                      AutoTime: '1',
                      SysDark: 'false',
                      AlwaysFinishActivities: '0',
                      AppDark: 'false',
                      ExternalStorage: '144796/231604',
                      InnerStorage: '144796/231604'
                    }
                  }
                ],
                subInsights: []
              },
              {
                name: '定位数据',
                description: '失败原因(定位服务没有开启，请在设置中打开定位服务开关)',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '通知诊断',
            description: null,
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '29608',
                tid: '2*',
                event: 'push_env',
                time: '22:25:34.740',
                lineNo: 29103,
                ts: 1721831134740,
                args: {
                  suggestPushType: '1'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '29608',
                tid: '210',
                event: 'push_env',
                time: '22:25:36.114',
                lineNo: 29274,
                ts: 1721831136114,
                args: {
                  sysType: 'null',
                  suggestPushType: '1',
                  type: '1',
                  token: '58k6ayd1SjvNpn0BSa//OehQ43IpHsyAAItTuFOXXjjmHZC7g+g5e8ypENjJurrz'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '29608',
                tid: '124',
                event: 'push_env',
                time: '22:25:36.242',
                lineNo: 29299,
                ts: 1721831136242,
                args: {
                  pushPer: 'false',
                  channel:
                    '{"Heytap PUSH":{"importance":"3","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"System Default Channel"},"boss_message_channel_id":{"importance":"4","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"聊天消息"},"channel_id_upgrade":{"importance":"2","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"升级"}}'
                }
              }
            ],
            subInsights: [
              {
                name: '通知渠道',
                description: '支持',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '通知权限',
                description: '未开启',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '类别：System Default Channel',
                description: '通知已开启 | 有声音',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '类别：聊天消息',
                description: '通知已开启 | 有声音',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '运行环境诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '29608',
                tid: '114',
                event: 'shu_zi',
                time: '22:25:34.820',
                lineNo: 29123,
                ts: 1721831134820,
                args: {
                  processId: '1',
                  time: '71',
                  queryId: 'DUvBL3owcsvCyzY7zqaaPN7zun_-ZlRUfcd0RFV2Qkwzb3djc3ZDeXpZN3pxYWFQTjd6dW5fLVpsUlVmY2Qwc2h1'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '29608',
                tid: '124',
                event: 'run_time_env',
                time: '22:25:36.241',
                lineNo: 29297,
                ts: 1721831136241,
                args: {
                  NetProxy: 'false',
                  IPV6Only: 'false',
                  GPSService: 'false',
                  VPN: 'false',
                  LocPer: '1',
                  AutoTime: '1',
                  SysDark: 'false',
                  AlwaysFinishActivities: '0',
                  AppDark: 'false',
                  ExternalStorage: '144796/231604',
                  InnerStorage: '144796/231604'
                }
              }
            ],
            subInsights: [
              {
                name: '数盟',
                description: '获取正常',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '网络代理设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '系统自动时间设置',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'Activity(页面)不保留活动设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'VPN设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '深色模式',
                description: '系统:未开启  App:未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'IPv6Only',
                description: '网络支持IPV4/IPV6',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '内置存储空间',
                description: '使用率：62.52%',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '外置存储空间',
                description: '使用率：62.52%',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '账号信息',
            description: '******** | BOSS',
            level: 'BASE',
            data: null,
            detailLog: [
              {
                tag: 'AccountAnalytic',
                pid: '29608',
                tid: '92',
                event: 'account_info',
                time: '22:25:34.690',
                lineNo: 29084,
                ts: *************,
                args: {
                  account: 'Account{uid=********, identity=1, phone=***********, regionCode=+86, register=false,t=true,t2=false,wt=true,zpAt=false}'
                }
              }
            ],
            subInsights: []
          },
          {
            name: '启动任务耗时',
            description: '总耗时:1101ms',
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'StartupAnalytic',
                pid: '29608',
                tid: '150',
                event: 'pipeline',
                time: '22:25:34.996',
                lineNo: 29173,
                ts: *************,
                args: {
                  thread: 'StartupPipeline',
                  time: '4',
                  class: 'DataKernelWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '29608',
                tid: '150',
                event: 'pipeline',
                time: '22:25:35.020',
                lineNo: 29180,
                ts: 1721831135020,
                args: {
                  thread: 'StartupPipeline',
                  time: '20',
                  class: 'ApmWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '29608',
                tid: '2*',
                event: 'pipeline',
                time: '22:25:35.020',
                lineNo: 29181,
                ts: 1721831135020,
                args: {
                  thread: 'main',
                  time: '0',
                  class: 'PublicInitWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '29608',
                tid: '2*',
                event: 'pipeline',
                time: '22:25:35.175',
                lineNo: 29192,
                ts: 1721831135175,
                args: {
                  thread: 'main',
                  time: '155',
                  class: 'DBUpgradeWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '29608',
                tid: '2*',
                event: 'pipeline',
                time: '22:25:35.177',
                lineNo: 29194,
                ts: 1721831135177,
                args: {
                  thread: 'main',
                  time: '2',
                  class: 'ActiveWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '29608',
                tid: '150',
                event: 'pipeline',
                time: '22:25:35.195',
                lineNo: 29198,
                ts: 1721831135195,
                args: {
                  thread: 'StartupPipeline',
                  time: '18',
                  class: 'BasicDataWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '29608',
                tid: '150',
                event: 'pipeline',
                time: '22:25:36.080',
                lineNo: 29259,
                ts: 1721831136080,
                args: {
                  thread: 'StartupPipeline',
                  time: '885',
                  class: 'ContactWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '29608',
                tid: '2*',
                event: 'pipeline',
                time: '22:25:36.081',
                lineNo: 29260,
                ts: 1721831136081,
                args: {
                  thread: 'main',
                  time: '1',
                  class: 'NetworkWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '29608',
                tid: '150',
                event: 'pipeline',
                time: '22:25:36.086',
                lineNo: 29266,
                ts: 1721831136086,
                args: {
                  thread: 'StartupPipeline',
                  time: '5',
                  class: 'AppImeiWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '29608',
                tid: '150',
                event: 'pipeline',
                time: '22:25:36.086',
                lineNo: 29267,
                ts: 1721831136086,
                args: {
                  thread: 'StartupPipeline',
                  time: '0',
                  class: 'AdvertWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '29608',
                tid: '2*',
                event: 'pipeline',
                time: '22:25:36.090',
                lineNo: 29269,
                ts: 1721831136090,
                args: {
                  thread: 'main',
                  time: '4',
                  class: 'CompleteWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '29608',
                tid: '2*',
                event: 'pipeline',
                time: '22:25:36.090',
                lineNo: 29270,
                ts: 1721831136090,
                args: {
                  totalTime: '1101'
                }
              }
            ],
            subInsights: [
              {
                name: 'DataKernelWork',
                description: '耗时:4ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ApmWork',
                description: '耗时:20ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'PublicInitWork',
                description: '耗时:0ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'DBUpgradeWork',
                description: '耗时:155ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ActiveWork',
                description: '耗时:2ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'BasicDataWork',
                description: '耗时:18ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ContactWork',
                description: '耗时:885ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'NetworkWork',
                description: '耗时:1ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'AppImeiWork',
                description: '耗时:5ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'AdvertWork',
                description: '耗时:0ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'CompleteWork',
                description: '耗时:4ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '每日更新联系人',
            description: '接口信息: BOSS:11013 | 店长:3',
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '120',
                event: 'default_refresh',
                time: '22:25:37.028',
                lineNo: 29446,
                ts: 1721831137028,
                args: {
                  localFriends: '10722',
                  dzFriendIdList: '3',
                  delContacts: '0',
                  zpFriendIdList: '11013',
                  dzNewAddCount: '0',
                  zpNewAddCount: '294',
                  status: 'init'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '259',
                event: 'default_refresh',
                time: '22:25:37.051',
                lineNo: 29455,
                ts: 1721831137051,
                args: {
                  monitor: '1',
                  time: '0'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '120',
                event: 'default_refresh',
                time: '22:25:37.622',
                lineNo: 29523,
                ts: 1721831137622,
                args: {
                  contactPage: '{"startIndex":0,"source":0,"pageSize":1000,"retryCount":0}',
                  baseInfoList: '1000',
                  time: '593',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '120',
                event: 'default_refresh',
                time: '22:25:38.268',
                lineNo: 29533,
                ts: 1721831138268,
                args: {
                  contactPage: '{"startIndex":1000,"source":0,"pageSize":1000,"retryCount":0}',
                  baseInfoList: '1000',
                  time: '579',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '168',
                event: 'default_refresh',
                time: '22:25:38.910',
                lineNo: 29545,
                ts: 1721831138910,
                args: {
                  contactPage: '{"startIndex":2000,"source":0,"pageSize":1000,"retryCount":0}',
                  baseInfoList: '1000',
                  time: '576',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '168',
                event: 'default_refresh',
                time: '22:25:39.540',
                lineNo: 29552,
                ts: 1721831139540,
                args: {
                  contactPage: '{"startIndex":3000,"source":0,"pageSize":1000,"retryCount":0}',
                  baseInfoList: '1000',
                  time: '565',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '168',
                event: 'default_refresh',
                time: '22:25:40.375',
                lineNo: 29559,
                ts: 1721831140375,
                args: {
                  contactPage: '{"startIndex":4000,"source":0,"pageSize":1000,"retryCount":0}',
                  baseInfoList: '1000',
                  time: '771',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '168',
                event: 'default_refresh',
                time: '22:25:41.114',
                lineNo: 29566,
                ts: 1721831141114,
                args: {
                  contactPage: '{"startIndex":5000,"source":0,"pageSize":1000,"retryCount":0}',
                  baseInfoList: '1000',
                  time: '676',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '168',
                event: 'default_refresh',
                time: '22:25:41.764',
                lineNo: 29573,
                ts: 1721831141764,
                args: {
                  contactPage: '{"startIndex":6000,"source":0,"pageSize":1000,"retryCount":0}',
                  baseInfoList: '1000',
                  time: '585',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '168',
                event: 'default_refresh',
                time: '22:25:42.440',
                lineNo: 29580,
                ts: 1721831142440,
                args: {
                  contactPage: '{"startIndex":7000,"source":0,"pageSize":1000,"retryCount":0}',
                  baseInfoList: '1000',
                  time: '611',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '168',
                event: 'default_refresh',
                time: '22:25:43.185',
                lineNo: 29596,
                ts: 1721831143185,
                args: {
                  contactPage: '{"startIndex":8000,"source":0,"pageSize":1000,"retryCount":0}',
                  baseInfoList: '1000',
                  time: '682',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '168',
                event: 'default_refresh',
                time: '22:25:43.983',
                lineNo: 29603,
                ts: 1721831143983,
                args: {
                  contactPage: '{"startIndex":9000,"source":0,"pageSize":1000,"retryCount":0}',
                  baseInfoList: '1000',
                  time: '733',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '168',
                event: 'default_refresh',
                time: '22:25:44.625',
                lineNo: 29610,
                ts: 1721831144625,
                args: {
                  contactPage: '{"startIndex":10000,"source":0,"pageSize":1000,"retryCount":0}',
                  baseInfoList: '1000',
                  time: '577',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '168',
                event: 'default_refresh',
                time: '22:25:44.834',
                lineNo: 29617,
                ts: 1721831144834,
                args: {
                  contactPage: '{"startIndex":11000,"source":0,"pageSize":13,"retryCount":0}',
                  baseInfoList: '13',
                  time: '145',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '168',
                event: 'default_refresh',
                time: '22:25:45.042',
                lineNo: 29624,
                ts: 1721831145042,
                args: {
                  contactPage: '{"startIndex":0,"source":1,"pageSize":3,"retryCount":0}',
                  baseInfoList: '3',
                  time: '148',
                  status: 'start'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '259',
                event: 'default_refresh',
                time: '22:25:45.064',
                lineNo: 29628,
                ts: 1721831145064,
                args: {
                  monitor: '2',
                  time: '0'
                }
              }
            ],
            subInsights: [
              {
                name: '联系人数据',
                description: '本地:10722  | 新增BOSS:294 | 新增店长:0 | 需删除:0',
                level: 'BASE',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '联系人存储',
                description: '延迟:0ms存储结束',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '联系人存储',
            description: '正常',
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '259',
                event: 'contact_async_unhandled_message',
                time: '22:25:46.408',
                lineNo: 29642,
                ts: 1721831146408,
                args: {
                  what: '11333',
                  delay: '879',
                  maxMsg: '7',
                  msgCount: '1',
                  time: '879',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '29608',
                tid: '259',
                event: 'contact_async_unhandled_message',
                time: '22:25:46.408',
                lineNo: 29643,
                ts: 1721831146408,
                args: {
                  what: '11333',
                  delay: '879',
                  maxMsg: '7',
                  msgCount: '0',
                  time: '0',
                  status: 'end'
                }
              }
            ],
            subInsights: []
          }
        ],
        insightInfo: '进程:29608\t时间：22:25:34--22:27:20\t持续时间：105.671s\n【当前版本】\t12.130\t已登录\n【定位诊断】❗️\t\t\n\t[定位服务]\t未开启\t\n\t[定位数据]\t失败原因(定位服务没有开启，请在设置中打开定位服务开关)\t\n【通知诊断】❗️\t\t\n\t[通知权限]\t未开启\t\n【账号信息】️\t******** | BOSS\t\n'
      },
      {
        pid: '5873',
        startTime: '2024-07-24 23:00:27',
        endTime: '2024-07-24 23:01:13',
        appVersion: '12.130',
        loginVersion: '1213010',
        insights: [
          {
            name: '权限诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [],
            subInsights: [
              {
                name: '定位权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [
                  {
                    tag: 'BaseInfoAnalytic',
                    pid: '5873',
                    tid: '195',
                    event: 'run_time_env',
                    time: '23:00:31.266',
                    lineNo: 30396,
                    ts: 1721833231266,
                    args: {
                      NetProxy: 'false',
                      IPV6Only: 'false',
                      GPSService: 'false',
                      VPN: 'false',
                      LocPer: '1',
                      AutoTime: '1',
                      SysDark: 'false',
                      AlwaysFinishActivities: '0',
                      AppDark: 'false',
                      ExternalStorage: '144752/231604',
                      InnerStorage: '144752/231604'
                    }
                  }
                ],
                subInsights: []
              }
            ]
          },
          {
            name: '定位诊断',
            description: null,
            level: 'WARN',
            data: null,
            detailLog: [],
            subInsights: [
              {
                name: '定位服务',
                description: '未开启',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '定位权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [
                  {
                    tag: 'BaseInfoAnalytic',
                    pid: '5873',
                    tid: '195',
                    event: 'run_time_env',
                    time: '23:00:31.266',
                    lineNo: 30396,
                    ts: 1721833231266,
                    args: {
                      NetProxy: 'false',
                      IPV6Only: 'false',
                      GPSService: 'false',
                      VPN: 'false',
                      LocPer: '1',
                      AutoTime: '1',
                      SysDark: 'false',
                      AlwaysFinishActivities: '0',
                      AppDark: 'false',
                      ExternalStorage: '144752/231604',
                      InnerStorage: '144752/231604'
                    }
                  }
                ],
                subInsights: []
              }
            ]
          },
          {
            name: '通知诊断',
            description: null,
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '5873',
                tid: '2*',
                event: 'push_env',
                time: '23:00:27.279',
                lineNo: 30115,
                ts: 1721833227279,
                args: {
                  suggestPushType: '1'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '5873',
                tid: '207',
                event: 'push_env',
                time: '23:00:31.192',
                lineNo: 30380,
                ts: 1721833231192,
                args: {
                  sysType: 'null',
                  suggestPushType: '1',
                  type: '1',
                  token: '58k6ayd1SjvNpn0BSa//OehQ43IpHsyAAItTuFOXXjjmHZC7g+g5e8ypENjJurrz'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '5873',
                tid: '195',
                event: 'push_env',
                time: '23:00:31.267',
                lineNo: 30398,
                ts: 1721833231267,
                args: {
                  pushPer: 'false',
                  channel:
                    '{"Heytap PUSH":{"importance":"3","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"System Default Channel"},"boss_message_channel_id":{"importance":"4","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"聊天消息"},"channel_id_upgrade":{"importance":"2","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"升级"}}'
                }
              }
            ],
            subInsights: [
              {
                name: '通知渠道',
                description: '支持',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '通知权限',
                description: '未开启',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '类别：System Default Channel',
                description: '通知已开启 | 有声音',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '类别：聊天消息',
                description: '通知已开启 | 有声音',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '运行环境诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '5873',
                tid: '127',
                event: 'shu_zi',
                time: '23:00:27.449',
                lineNo: 30156,
                ts: 1721833227449,
                args: {
                  processId: '1',
                  time: '161',
                  queryId: 'DUvBL3owcsvCyzY7zqaaPN7zun_-ZlRUfcd0RFV2Qkwzb3djc3ZDeXpZN3pxYWFQTjd6dW5fLVpsUlVmY2Qwc2h1'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '5873',
                tid: '195',
                event: 'run_time_env',
                time: '23:00:31.266',
                lineNo: 30396,
                ts: 1721833231266,
                args: {
                  NetProxy: 'false',
                  IPV6Only: 'false',
                  GPSService: 'false',
                  VPN: 'false',
                  LocPer: '1',
                  AutoTime: '1',
                  SysDark: 'false',
                  AlwaysFinishActivities: '0',
                  AppDark: 'false',
                  ExternalStorage: '144752/231604',
                  InnerStorage: '144752/231604'
                }
              }
            ],
            subInsights: [
              {
                name: '数盟',
                description: '获取正常',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '网络代理设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '系统自动时间设置',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'Activity(页面)不保留活动设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'VPN设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '深色模式',
                description: '系统:未开启  App:未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'IPv6Only',
                description: '网络支持IPV4/IPV6',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '内置存储空间',
                description: '使用率：62.5%',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '外置存储空间',
                description: '使用率：62.5%',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '账号信息',
            description: '******** | BOSS',
            level: 'BASE',
            data: null,
            detailLog: [
              {
                tag: 'AccountAnalytic',
                pid: '5873',
                tid: '93',
                event: 'account_info',
                time: '23:00:27.080',
                lineNo: 30075,
                ts: *************,
                args: {
                  account: 'Account{uid=********, identity=1, phone=***********, regionCode=+86, register=false,t=true,t2=false,wt=true,zpAt=false}'
                }
              }
            ],
            subInsights: []
          },
          {
            name: '启动任务耗时',
            description: '总耗时:3674ms',
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'StartupAnalytic',
                pid: '5873',
                tid: '163',
                event: 'pipeline',
                time: '23:00:27.489',
                lineNo: 30163,
                ts: *************,
                args: {
                  thread: 'StartupPipeline',
                  time: '6',
                  class: 'DataKernelWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '5873',
                tid: '163',
                event: 'pipeline',
                time: '23:00:27.520',
                lineNo: 30173,
                ts: 1721833227520,
                args: {
                  thread: 'StartupPipeline',
                  time: '25',
                  class: 'ApmWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '5873',
                tid: '2*',
                event: 'pipeline',
                time: '23:00:27.520',
                lineNo: 30174,
                ts: 1721833227520,
                args: {
                  thread: 'main',
                  time: '0',
                  class: 'PublicInitWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '5873',
                tid: '2*',
                event: 'pipeline',
                time: '23:00:30.171',
                lineNo: 30232,
                ts: 1721833230171,
                args: {
                  thread: 'main',
                  time: '2651',
                  class: 'DBUpgradeWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '5873',
                tid: '2*',
                event: 'pipeline',
                time: '23:00:30.175',
                lineNo: 30234,
                ts: 1721833230175,
                args: {
                  thread: 'main',
                  time: '4',
                  class: 'ActiveWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '5873',
                tid: '163',
                event: 'pipeline',
                time: '23:00:30.215',
                lineNo: 30238,
                ts: 1721833230215,
                args: {
                  thread: 'StartupPipeline',
                  time: '40',
                  class: 'BasicDataWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '5873',
                tid: '163',
                event: 'pipeline',
                time: '23:00:31.118',
                lineNo: 30331,
                ts: 1721833231118,
                args: {
                  thread: 'StartupPipeline',
                  time: '903',
                  class: 'ContactWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '5873',
                tid: '2*',
                event: 'pipeline',
                time: '23:00:31.122',
                lineNo: 30334,
                ts: 1721833231122,
                args: {
                  thread: 'main',
                  time: '4',
                  class: 'NetworkWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '5873',
                tid: '163',
                event: 'pipeline',
                time: '23:00:31.153',
                lineNo: 30371,
                ts: 1721833231153,
                args: {
                  thread: 'StartupPipeline',
                  time: '31',
                  class: 'AppImeiWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '5873',
                tid: '163',
                event: 'pipeline',
                time: '23:00:31.153',
                lineNo: 30372,
                ts: 1721833231153,
                args: {
                  thread: 'StartupPipeline',
                  time: '0',
                  class: 'AdvertWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '5873',
                tid: '2*',
                event: 'pipeline',
                time: '23:00:31.157',
                lineNo: 30374,
                ts: 1721833231157,
                args: {
                  thread: 'main',
                  time: '4',
                  class: 'CompleteWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '5873',
                tid: '2*',
                event: 'pipeline',
                time: '23:00:31.157',
                lineNo: 30375,
                ts: 1721833231157,
                args: {
                  totalTime: '3674'
                }
              }
            ],
            subInsights: [
              {
                name: 'DataKernelWork',
                description: '耗时:6ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ApmWork',
                description: '耗时:25ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'PublicInitWork',
                description: '耗时:0ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'DBUpgradeWork',
                description: '耗时:2651ms',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ActiveWork',
                description: '耗时:4ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'BasicDataWork',
                description: '耗时:40ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ContactWork',
                description: '耗时:903ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'NetworkWork',
                description: '耗时:4ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'AppImeiWork',
                description: '耗时:31ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'AdvertWork',
                description: '耗时:0ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'CompleteWork',
                description: '耗时:4ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '联系人存储',
            description: '正常',
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'ChatAnalytic',
                pid: '5873',
                tid: '197',
                event: 'contact_async_unhandled_message',
                time: '23:00:31.773',
                lineNo: 30497,
                ts: 1721833231773,
                args: {
                  what: '11101',
                  delay: '723',
                  maxMsg: '280',
                  msgCount: '281',
                  time: '638',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '5873',
                tid: '197',
                event: 'contact_async_unhandled_message',
                time: '23:00:32.437',
                lineNo: 30639,
                ts: 1721833232437,
                args: {
                  what: '11101',
                  delay: '1386',
                  maxMsg: '532',
                  msgCount: '533',
                  time: '599',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '5873',
                tid: '197',
                event: 'contact_async_unhandled_message',
                time: '23:00:33.024',
                lineNo: 30993,
                ts: 1721833233024,
                args: {
                  what: '11101',
                  delay: '1972',
                  maxMsg: '1030',
                  msgCount: '1031',
                  time: '85',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '5873',
                tid: '197',
                event: 'contact_async_unhandled_message',
                time: '23:00:35.445',
                lineNo: 31371,
                ts: 1721833235445,
                args: {
                  what: '11101',
                  delay: '4392',
                  maxMsg: '1538',
                  msgCount: '1539',
                  time: '253',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '5873',
                tid: '197',
                event: 'contact_async_unhandled_message',
                time: '23:00:37.062',
                lineNo: 31804,
                ts: 1721833237062,
                args: {
                  what: '11305',
                  delay: '6005',
                  maxMsg: '2214',
                  msgCount: '2215',
                  time: '563',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '5873',
                tid: '197',
                event: 'contact_async_unhandled_message',
                time: '23:00:52.055',
                lineNo: 33504,
                ts: 1721833252055,
                args: {
                  what: '11101',
                  delay: '19574',
                  maxMsg: '2380',
                  msgCount: '1999',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '5873',
                tid: '197',
                event: 'contact_async_unhandled_message',
                time: '23:00:52.365',
                lineNo: 33690,
                ts: 1721833252365,
                args: {
                  what: '11301',
                  delay: '18904',
                  maxMsg: '2380',
                  msgCount: '1499',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '5873',
                tid: '197',
                event: 'contact_async_unhandled_message',
                time: '23:00:52.685',
                lineNo: 33853,
                ts: 1721833252685,
                args: {
                  what: '11101',
                  delay: '16870',
                  maxMsg: '2380',
                  msgCount: '999',
                  time: '0',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '5873',
                tid: '197',
                event: 'contact_async_unhandled_message',
                time: '23:00:52.951',
                lineNo: 34052,
                ts: 1721833252951,
                args: {
                  what: '11101',
                  delay: '16445',
                  maxMsg: '2380',
                  msgCount: '499',
                  time: '2',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '5873',
                tid: '197',
                event: 'contact_async_unhandled_message',
                time: '23:00:53.294',
                lineNo: 34162,
                ts: 1721833253294,
                args: {
                  what: '11101',
                  delay: '55',
                  maxMsg: '2380',
                  msgCount: '0',
                  time: '1',
                  status: 'end'
                }
              }
            ],
            subInsights: [
              {
                name: '耗时任务ID：11301',
                description: '延迟：18904\t耗时：1',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '耗时任务ID：11101',
                description: '延迟：16445\t耗时：2',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          }
        ],
        insightInfo: '进程:5873\t时间：23:00:27--23:01:13\t持续时间：46.728s\n【当前版本】\t12.130\t已登录\n【定位诊断】❗️\t\t\n\t[定位服务]\t未开启\t\n【通知诊断】❗️\t\t\n\t[通知权限]\t未开启\t\n【账号信息】️\t******** | BOSS\t\n【启动任务耗时】❗️\t总耗时:3674ms\t\n\t[DBUpgradeWork]\t耗时:2651ms\t\n'
      },
      {
        pid: '12565',
        startTime: '2024-07-24 23:32:57',
        endTime: '2024-07-24 23:59:59',
        appVersion: '12.130',
        loginVersion: '1213010',
        insights: [
          {
            name: '权限诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:33:14.016',
                lineNo: 41234,
                ts: 1721835194016,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:33:19.465',
                lineNo: 41585,
                ts: 1721835199465,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:33:24.605',
                lineNo: 41716,
                ts: 1721835204605,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:33:30.740',
                lineNo: 41842,
                ts: 1721835210740,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:33:35.701',
                lineNo: 42015,
                ts: 1721835215701,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:33:41.190',
                lineNo: 42227,
                ts: 1721835221190,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:33:46.169',
                lineNo: 42441,
                ts: 1721835226169,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:33:50.765',
                lineNo: 42637,
                ts: 1721835230765,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:33:55.055',
                lineNo: 42762,
                ts: 1721835235055,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:34:00.381',
                lineNo: 42989,
                ts: 1721835240381,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:34:08.137',
                lineNo: 43184,
                ts: 1721835248137,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:34:18.872',
                lineNo: 43420,
                ts: 1721835258872,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:34:23.334',
                lineNo: 43525,
                ts: 1721835263334,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:34:27.922',
                lineNo: 43634,
                ts: 1721835267922,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:34:39.977',
                lineNo: 43880,
                ts: 1721835279977,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:35:10.507',
                lineNo: 44217,
                ts: 1721835310507,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:35:15.807',
                lineNo: 44359,
                ts: 1721835315807,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:35:35.783',
                lineNo: 44581,
                ts: 1721835335783,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:36:25.743',
                lineNo: 44917,
                ts: 1721835385743,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:36:32.297',
                lineNo: 45071,
                ts: 1721835392297,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:36:37.873',
                lineNo: 45183,
                ts: 1721835397873,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:36:42.865',
                lineNo: 45290,
                ts: 1721835402865,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:37:03.042',
                lineNo: 45705,
                ts: 1721835423042,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:37:08.376',
                lineNo: 45838,
                ts: 1721835428376,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:37:25.925',
                lineNo: 46076,
                ts: 1721835445925,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:37:37.116',
                lineNo: 46423,
                ts: 1721835457116,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:37:42.129',
                lineNo: 46564,
                ts: 1721835462129,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:37:47.317',
                lineNo: 46711,
                ts: 1721835467317,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:37:52.287',
                lineNo: 46822,
                ts: 1721835472287,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:37:59.849',
                lineNo: 47073,
                ts: 1721835479849,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:38:05.391',
                lineNo: 47234,
                ts: 1721835485391,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:38:13.956',
                lineNo: 47386,
                ts: 1721835493956,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:38:22.969',
                lineNo: 47706,
                ts: 1721835502969,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:38:31.125',
                lineNo: 47956,
                ts: 1721835511125,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:38:35.990',
                lineNo: 48139,
                ts: 1721835515990,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:38:41.304',
                lineNo: 48348,
                ts: 1721835521304,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:38:49.992',
                lineNo: 48622,
                ts: 1721835529992,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:38:54.213',
                lineNo: 48794,
                ts: 1721835534213,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:38:59.819',
                lineNo: 49009,
                ts: 1721835539819,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:39:04.714',
                lineNo: 49160,
                ts: 1721835544714,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:39:09.531',
                lineNo: 49311,
                ts: 1721835549531,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:39:13.688',
                lineNo: 49435,
                ts: 1721835553688,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:39:19.274',
                lineNo: 49614,
                ts: 1721835559274,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:39:27.114',
                lineNo: 49856,
                ts: 1721835567114,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:39:33.088',
                lineNo: 50011,
                ts: 1721835573088,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:39:42.000',
                lineNo: 50157,
                ts: 1721835582000,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:39:47.748',
                lineNo: 50411,
                ts: 1721835587748,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:39:53.234',
                lineNo: 50531,
                ts: 1721835593234,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:39:58.675',
                lineNo: 50692,
                ts: 1721835598675,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:40:03.850',
                lineNo: 50959,
                ts: 1721835603850,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:40:10.086',
                lineNo: 51208,
                ts: 1721835610086,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:40:16.058',
                lineNo: 51314,
                ts: 1721835616058,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:40:20.605',
                lineNo: 51444,
                ts: 1721835620605,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:40:24.365',
                lineNo: 51708,
                ts: 1721835624365,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:40:28.248',
                lineNo: 51838,
                ts: 1721835628248,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:40:34.130',
                lineNo: 51998,
                ts: 1721835634130,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:40:39.765',
                lineNo: 52208,
                ts: 1721835639765,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:40:43.871',
                lineNo: 52323,
                ts: 1721835643871,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:40:48.735',
                lineNo: 52528,
                ts: 1721835648735,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:40:53.490',
                lineNo: 52727,
                ts: 1721835653490,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:40:58.683',
                lineNo: 52847,
                ts: 1721835658683,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:41:07.622',
                lineNo: 53016,
                ts: 1721835667622,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:41:17.006',
                lineNo: 53233,
                ts: 1721835677006,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:41:23.189',
                lineNo: 53402,
                ts: 1721835683189,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:41:29.054',
                lineNo: 53559,
                ts: 1721835689054,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:41:33.597',
                lineNo: 53718,
                ts: 1721835693597,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:41:41.527',
                lineNo: 53879,
                ts: 1721835701527,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:41:46.420',
                lineNo: 54154,
                ts: 1721835706420,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:41:51.494',
                lineNo: 54267,
                ts: 1721835711494,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:41:56.473',
                lineNo: 54423,
                ts: 1721835716473,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:42:03.069',
                lineNo: 54619,
                ts: 1721835723069,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:42:18.524',
                lineNo: 54973,
                ts: 1721835738524,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:42:23.219',
                lineNo: 55152,
                ts: 1721835743219,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:42:31.033',
                lineNo: 55293,
                ts: 1721835751033,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:42:37.425',
                lineNo: 55478,
                ts: 1721835757425,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:42:45.656',
                lineNo: 55636,
                ts: 1721835765656,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:42:52.114',
                lineNo: 55882,
                ts: 1721835772114,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:42:56.906',
                lineNo: 56051,
                ts: 1721835776906,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:43:01.273',
                lineNo: 56211,
                ts: 1721835781273,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:43:07.061',
                lineNo: 56360,
                ts: 1721835787061,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:43:15.055',
                lineNo: 56527,
                ts: 1721835795055,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:43:19.693',
                lineNo: 56640,
                ts: 1721835799693,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:43:30.341',
                lineNo: 56785,
                ts: 1721835810341,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:43:32.485',
                lineNo: 56932,
                ts: 1721835812485,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:44:33.384',
                lineNo: 57536,
                ts: 1721835873384,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:45:11.298',
                lineNo: 57870,
                ts: 1721835911298,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:46:27.626',
                lineNo: 58513,
                ts: 1721835987626,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:46:35.854',
                lineNo: 58740,
                ts: 1721835995854,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:46:40.747',
                lineNo: 58890,
                ts: 1721836000747,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:46:46.592',
                lineNo: 59047,
                ts: 1721836006592,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:46:54.434',
                lineNo: 59222,
                ts: 1721836014434,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:47:06.248',
                lineNo: 59413,
                ts: 1721836026248,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:47:16.895',
                lineNo: 59611,
                ts: 1721836036895,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:47:18.889',
                lineNo: 59754,
                ts: 1721836038889,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:47:31.682',
                lineNo: 60048,
                ts: 1721836051682,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:47:45.784',
                lineNo: 60375,
                ts: 1721836065784,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:47:47.586',
                lineNo: 60508,
                ts: 1721836067586,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:47:55.233',
                lineNo: 60694,
                ts: 1721836075233,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:48:00.684',
                lineNo: 60854,
                ts: 1721836080684,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:48:06.047',
                lineNo: 61036,
                ts: 1721836086047,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:48:22.493',
                lineNo: 61312,
                ts: 1721836102493,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:48:28.404',
                lineNo: 61463,
                ts: 1721836108404,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:48:36.507',
                lineNo: 61600,
                ts: 1721836116507,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:48:46.942',
                lineNo: 61817,
                ts: 1721836126942,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:48:53.642',
                lineNo: 62133,
                ts: 1721836133642,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:48:59.854',
                lineNo: 62277,
                ts: 1721836139854,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:49:05.002',
                lineNo: 62543,
                ts: 1721836145002,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:49:10.172',
                lineNo: 62786,
                ts: 1721836150172,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:49:15.158',
                lineNo: 62974,
                ts: 1721836155158,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:49:20.507',
                lineNo: 63117,
                ts: 1721836160507,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:49:25.426',
                lineNo: 63247,
                ts: 1721836165426,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:49:29.396',
                lineNo: 63357,
                ts: 1721836169396,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:49:34.357',
                lineNo: 63500,
                ts: 1721836174357,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:49:45.447',
                lineNo: 63688,
                ts: 1721836185447,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:49:57.732',
                lineNo: 63903,
                ts: 1721836197732,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:50:25.444',
                lineNo: 64461,
                ts: 1721836225444,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:50:30.983',
                lineNo: 64643,
                ts: 1721836230983,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:50:36.618',
                lineNo: 64867,
                ts: 1721836236618,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:50:41.235',
                lineNo: 65037,
                ts: 1721836241235,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:50:46.084',
                lineNo: 65195,
                ts: 1721836246084,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:50:51.096',
                lineNo: 65479,
                ts: 1721836251096,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:50:56.419',
                lineNo: 65811,
                ts: 1721836256419,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:51:00.516',
                lineNo: 65956,
                ts: 1721836260516,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:51:05.231',
                lineNo: 66081,
                ts: 1721836265231,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:51:11.078',
                lineNo: 66292,
                ts: 1721836271078,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:51:15.507',
                lineNo: 66426,
                ts: 1721836275507,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:51:20.294',
                lineNo: 66588,
                ts: 1721836280294,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:51:27.026',
                lineNo: 66804,
                ts: 1721836287026,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:51:40.396',
                lineNo: 67081,
                ts: 1721836300396,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:51:46.333',
                lineNo: 67218,
                ts: 1721836306333,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:51:59.167',
                lineNo: 67507,
                ts: 1721836319167,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:52:04.498',
                lineNo: 67693,
                ts: 1721836324498,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:52:09.171',
                lineNo: 67865,
                ts: 1721836329171,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:52:16.592',
                lineNo: 68096,
                ts: 1721836336592,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:52:22.692',
                lineNo: 68314,
                ts: 1721836342692,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:52:26.992',
                lineNo: 68487,
                ts: 1721836346992,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:52:36.825',
                lineNo: 68671,
                ts: 1721836356825,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:53:09.045',
                lineNo: 69105,
                ts: 1721836389045,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:54:13.788',
                lineNo: 69640,
                ts: 1721836453788,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:54:30.082',
                lineNo: 70000,
                ts: 1721836470082,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:54:37.452',
                lineNo: 70232,
                ts: 1721836477452,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:54:43.575',
                lineNo: 70451,
                ts: 1721836483575,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:54:48.785',
                lineNo: 70661,
                ts: 1721836488785,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:55:02.670',
                lineNo: 70883,
                ts: 1721836502670,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:55:10.332',
                lineNo: 71128,
                ts: 1721836510332,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:55:21.129',
                lineNo: 71461,
                ts: 1721836521129,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:56:09.871',
                lineNo: 72022,
                ts: 1721836569871,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:56:23.804',
                lineNo: 72372,
                ts: 1721836583804,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:56:36.694',
                lineNo: 72716,
                ts: 1721836596694,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:57:33.288',
                lineNo: 73293,
                ts: 1721836653288,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:57:43.953',
                lineNo: 73654,
                ts: 1721836663953,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:57:47.922',
                lineNo: 73796,
                ts: 1721836667922,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:57:54.402',
                lineNo: 73939,
                ts: 1721836674402,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:58:01.377',
                lineNo: 74079,
                ts: 1721836681377,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:58:07.990',
                lineNo: 74303,
                ts: 1721836687990,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:58:18.440',
                lineNo: 74655,
                ts: 1721836698440,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:58:24.947',
                lineNo: 74904,
                ts: 1721836704947,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:58:31.434',
                lineNo: 75106,
                ts: 1721836711434,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:58:53.719',
                lineNo: 75432,
                ts: 1721836733719,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:59:13.785',
                lineNo: 75909,
                ts: 1721836753785,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:59:19.310',
                lineNo: 76085,
                ts: 1721836759310,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:59:29.815',
                lineNo: 76327,
                ts: 1721836769815,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:59:50.616',
                lineNo: 76873,
                ts: 1721836790616,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'permission_data',
                time: '23:59:56.762',
                lineNo: 77090,
                ts: 1721836796762,
                args: {
                  request: 'b',
                  hasPermission: 'true',
                  permissionData: '{appAgree=1, hasPermission=true, showRequestPermissionRationale=true}',
                  permission: 'android.permission.ACCESS_COARSE_LOCATION#android.permission.ACCESS_FINE_LOCATION'
                }
              }
            ],
            subInsights: [
              {
                name: '定位权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '定位诊断',
            description: null,
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:33:14.216',
                lineNo: 41301,
                ts: 1721835194216,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:33:19.555',
                lineNo: 41609,
                ts: 1721835199555,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:33:24.699',
                lineNo: 41741,
                ts: 1721835204699,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:33:30.832',
                lineNo: 41867,
                ts: 1721835210832,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:33:35.791',
                lineNo: 42028,
                ts: 1721835215791,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:33:41.283',
                lineNo: 42241,
                ts: 1721835221283,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:33:46.255',
                lineNo: 42454,
                ts: 1721835226255,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:33:50.853',
                lineNo: 42659,
                ts: 1721835230853,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:33:55.142',
                lineNo: 42784,
                ts: 1721835235142,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:34:00.574',
                lineNo: 43017,
                ts: 1721835240574,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:34:08.174',
                lineNo: 43192,
                ts: 1721835248174,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:34:18.958',
                lineNo: 43442,
                ts: 1721835258958,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:34:23.422',
                lineNo: 43549,
                ts: 1721835263422,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:34:28.002',
                lineNo: 43655,
                ts: 1721835268002,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:34:40.075',
                lineNo: 43894,
                ts: 1721835280075,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:35:10.606',
                lineNo: 44231,
                ts: 1721835310606,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:35:15.897',
                lineNo: 44382,
                ts: 1721835315897,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:35:35.874',
                lineNo: 44594,
                ts: 1721835335874,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:36:25.859',
                lineNo: 44930,
                ts: 1721835385859,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:36:32.374',
                lineNo: 45093,
                ts: 1721835392374,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:36:37.951',
                lineNo: 45205,
                ts: 1721835397951,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:36:42.950',
                lineNo: 45312,
                ts: 1721835402950,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:37:03.153',
                lineNo: 45715,
                ts: 1721835423153,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:37:08.458',
                lineNo: 45852,
                ts: 1721835428458,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:37:26.019',
                lineNo: 46090,
                ts: 1721835446019,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:37:37.206',
                lineNo: 46446,
                ts: 1721835457206,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:37:42.246',
                lineNo: 46593,
                ts: 1721835462246,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:37:47.390',
                lineNo: 46732,
                ts: 1721835467390,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:37:52.359',
                lineNo: 46843,
                ts: 1721835472359,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:37:59.931',
                lineNo: 47083,
                ts: 1721835479931,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:38:05.464',
                lineNo: 47251,
                ts: 1721835485464,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:38:13.999',
                lineNo: 47394,
                ts: 1721835493999,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:38:23.042',
                lineNo: 47727,
                ts: 1721835503042,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:38:31.200',
                lineNo: 47979,
                ts: 1721835511200,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:38:36.098',
                lineNo: 48151,
                ts: 1721835516098,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:38:41.398',
                lineNo: 48362,
                ts: 1721835521398,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:38:50.068',
                lineNo: 48643,
                ts: 1721835530068,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:38:54.306',
                lineNo: 48819,
                ts: 1721835534306,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:38:59.895',
                lineNo: 49022,
                ts: 1721835539895,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:39:04.791',
                lineNo: 49182,
                ts: 1721835544791,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:39:09.606',
                lineNo: 49334,
                ts: 1721835549606,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:39:13.766',
                lineNo: 49450,
                ts: 1721835553766,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:39:19.374',
                lineNo: 49637,
                ts: 1721835559374,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:39:27.192',
                lineNo: 49878,
                ts: 1721835567192,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:39:33.164',
                lineNo: 50033,
                ts: 1721835573164,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:39:42.077',
                lineNo: 50177,
                ts: 1721835582077,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:39:47.841',
                lineNo: 50434,
                ts: 1721835587841,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:39:53.314',
                lineNo: 50544,
                ts: 1721835593314,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:39:58.759',
                lineNo: 50705,
                ts: 1721835598759,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:40:03.929',
                lineNo: 50972,
                ts: 1721835603929,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:40:10.177',
                lineNo: 51231,
                ts: 1721835610177,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:40:16.129',
                lineNo: 51336,
                ts: 1721835616129,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:40:20.736',
                lineNo: 51473,
                ts: 1721835620736,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:40:24.441',
                lineNo: 51730,
                ts: 1721835624441,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:40:28.326',
                lineNo: 51851,
                ts: 1721835628326,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:40:34.220',
                lineNo: 52022,
                ts: 1721835634220,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:40:39.847',
                lineNo: 52228,
                ts: 1721835639847,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:40:43.943',
                lineNo: 52335,
                ts: 1721835643943,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:40:48.808',
                lineNo: 52542,
                ts: 1721835648808,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:40:53.562',
                lineNo: 52742,
                ts: 1721835653562,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:40:58.771',
                lineNo: 52863,
                ts: 1721835658771,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:41:07.696',
                lineNo: 53033,
                ts: 1721835667696,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:41:17.106',
                lineNo: 53258,
                ts: 1721835677106,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:41:23.278',
                lineNo: 53416,
                ts: 1721835683278,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:41:29.144',
                lineNo: 53582,
                ts: 1721835689144,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:41:33.678',
                lineNo: 53733,
                ts: 1721835693678,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:41:41.610',
                lineNo: 53901,
                ts: 1721835701610,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:41:46.498',
                lineNo: 54171,
                ts: 1721835706498,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:41:51.570',
                lineNo: 54280,
                ts: 1721835711570,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:41:56.554',
                lineNo: 54445,
                ts: 1721835716554,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:42:03.172',
                lineNo: 54633,
                ts: 1721835723172,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:42:18.611',
                lineNo: 54996,
                ts: 1721835738611,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:42:23.296',
                lineNo: 55174,
                ts: 1721835743296,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:42:31.109',
                lineNo: 55314,
                ts: 1721835751109,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:42:37.522',
                lineNo: 55499,
                ts: 1721835757522,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:42:45.732',
                lineNo: 55658,
                ts: 1721835765732,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:42:52.195',
                lineNo: 55908,
                ts: 1721835772195,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:42:56.981',
                lineNo: 56066,
                ts: 1721835776981,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:43:01.401',
                lineNo: 56237,
                ts: 1721835781401,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:43:07.141',
                lineNo: 56377,
                ts: 1721835787141,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:43:15.130',
                lineNo: 56545,
                ts: 1721835795130,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:43:19.769',
                lineNo: 56663,
                ts: 1721835799769,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:43:30.382',
                lineNo: 56793,
                ts: 1721835810382,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:43:32.525',
                lineNo: 56939,
                ts: 1721835812525,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:44:33.464',
                lineNo: 57547,
                ts: 1721835873464,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:45:11.389',
                lineNo: 57884,
                ts: 1721835911389,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:46:27.702',
                lineNo: 58527,
                ts: 1721835987702,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:46:36.014',
                lineNo: 58753,
                ts: 1721835996014,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:46:40.829',
                lineNo: 58903,
                ts: 1721836000829,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:46:46.668',
                lineNo: 59070,
                ts: 1721836006668,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:46:54.519',
                lineNo: 59240,
                ts: 1721836014519,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:47:06.322',
                lineNo: 59434,
                ts: 1721836026322,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:47:16.932',
                lineNo: 59618,
                ts: 1721836036932,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:47:18.925',
                lineNo: 59762,
                ts: 1721836038925,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:47:31.757',
                lineNo: 60069,
                ts: 1721836051757,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:47:45.861',
                lineNo: 60398,
                ts: 1721836065861,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:47:47.685',
                lineNo: 60535,
                ts: 1721836067685,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:47:55.317',
                lineNo: 60707,
                ts: 1721836075317,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:48:00.785',
                lineNo: 60877,
                ts: 1721836080785,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:48:06.121',
                lineNo: 61057,
                ts: 1721836086121,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:48:22.597',
                lineNo: 61335,
                ts: 1721836102597,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:48:28.478',
                lineNo: 61479,
                ts: 1721836108478,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:48:36.591',
                lineNo: 61621,
                ts: 1721836116591,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:48:47.018',
                lineNo: 61839,
                ts: 1721836127018,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:48:53.716',
                lineNo: 62145,
                ts: 1721836133716,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:48:59.948',
                lineNo: 62290,
                ts: 1721836139948,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:49:05.102',
                lineNo: 62557,
                ts: 1721836145102,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:49:10.249',
                lineNo: 62807,
                ts: 1721836150249,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:49:15.245',
                lineNo: 62989,
                ts: 1721836155245,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:49:20.578',
                lineNo: 63137,
                ts: 1721836160578,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:49:25.525',
                lineNo: 63270,
                ts: 1721836165525,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:49:29.471',
                lineNo: 63372,
                ts: 1721836169471,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:49:34.432',
                lineNo: 63521,
                ts: 1721836174432,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:49:45.522',
                lineNo: 63709,
                ts: 1721836185522,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:49:57.803',
                lineNo: 63920,
                ts: 1721836197803,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:50:25.534',
                lineNo: 64475,
                ts: 1721836225534,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:50:31.058',
                lineNo: 64656,
                ts: 1721836231058,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:50:36.797',
                lineNo: 64880,
                ts: 1721836236797,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:50:41.341',
                lineNo: 65051,
                ts: 1721836241341,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:50:46.180',
                lineNo: 65209,
                ts: 1721836246180,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:50:51.172',
                lineNo: 65500,
                ts: 1721836251172,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:50:56.494',
                lineNo: 65825,
                ts: 1721836256494,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:51:00.587',
                lineNo: 65977,
                ts: 1721836260587,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:51:05.313',
                lineNo: 66096,
                ts: 1721836265313,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:51:11.160',
                lineNo: 66305,
                ts: 1721836271160,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:51:15.581',
                lineNo: 66443,
                ts: 1721836275581,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:51:20.372',
                lineNo: 66609,
                ts: 1721836280372,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:51:27.103',
                lineNo: 66816,
                ts: 1721836287103,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:51:40.497',
                lineNo: 67104,
                ts: 1721836300497,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:51:46.406',
                lineNo: 67239,
                ts: 1721836306406,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:51:59.259',
                lineNo: 67522,
                ts: 1721836319259,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:52:04.574',
                lineNo: 67704,
                ts: 1721836324574,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:52:09.246',
                lineNo: 67878,
                ts: 1721836329246,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:52:16.702',
                lineNo: 68111,
                ts: 1721836336702,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:52:22.784',
                lineNo: 68331,
                ts: 1721836342784,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:52:27.069',
                lineNo: 68501,
                ts: 1721836347069,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:52:36.952',
                lineNo: 68699,
                ts: 1721836356952,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:53:09.131',
                lineNo: 69122,
                ts: 1721836389131,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:54:13.889',
                lineNo: 69652,
                ts: 1721836453889,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:54:30.296',
                lineNo: 70035,
                ts: 1721836470296,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:54:37.551',
                lineNo: 70246,
                ts: 1721836477551,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:54:43.729',
                lineNo: 70485,
                ts: 1721836483729,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:54:48.862',
                lineNo: 70676,
                ts: 1721836488862,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:55:02.759',
                lineNo: 70911,
                ts: 1721836502759,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:55:10.539',
                lineNo: 71147,
                ts: 1721836510539,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:55:21.208',
                lineNo: 71476,
                ts: 1721836521208,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:56:10.078',
                lineNo: 72038,
                ts: 1721836570078,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:56:23.906',
                lineNo: 72386,
                ts: 1721836583906,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:56:36.798',
                lineNo: 72732,
                ts: 1721836596798,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:57:33.464',
                lineNo: 73309,
                ts: 1721836653464,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:57:44.040',
                lineNo: 73683,
                ts: 1721836664040,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:57:47.999',
                lineNo: 73813,
                ts: 1721836667999,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:57:54.479',
                lineNo: 73966,
                ts: 1721836674479,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:58:01.452',
                lineNo: 74106,
                ts: 1721836681452,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:58:08.068',
                lineNo: 74316,
                ts: 1721836688068,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:58:18.531',
                lineNo: 74667,
                ts: 1721836698531,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:58:25.050',
                lineNo: 74933,
                ts: 1721836705050,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:58:31.513',
                lineNo: 75133,
                ts: 1721836711513,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:58:53.798',
                lineNo: 75444,
                ts: 1721836733798,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:59:13.880',
                lineNo: 75923,
                ts: 1721836753880,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:59:19.389',
                lineNo: 76113,
                ts: 1721836759389,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:59:29.898',
                lineNo: 76339,
                ts: 1721836769898,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:59:50.740',
                lineNo: 76887,
                ts: 1721836790740,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'location_data',
                time: '23:59:56.869',
                lineNo: 77119,
                ts: 1721836796869,
                args: {
                  location: 'latitude=0.0#longitude=0.0#province=#coordType=GCJ02#city=#district=#cityCode=#adCode=#address=#country=#road=#poiName=#street=#streetNum=#aoiName=#poiid=#floor=#errorCode=12#errorInfo=缺少定位权限#locationDetail=定位服务没有开启，请在设置中打开定位服务开关#1206#pm111111#description=#locationType=0#conScenario=0'
                }
              }
            ],
            subInsights: [
              {
                name: '定位服务',
                description: '未开启',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '定位权限',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [
                  {
                    tag: 'BaseInfoAnalytic',
                    pid: '12565',
                    tid: '112',
                    event: 'run_time_env',
                    time: '23:32:58.715',
                    lineNo: 38268,
                    ts: 1721835178715,
                    args: {
                      NetProxy: 'false',
                      IPV6Only: 'false',
                      GPSService: 'false',
                      VPN: 'false',
                      LocPer: '1',
                      AutoTime: '1',
                      SysDark: 'false',
                      AlwaysFinishActivities: '0',
                      AppDark: 'false',
                      ExternalStorage: '144748/231604',
                      InnerStorage: '144748/231604'
                    }
                  }
                ],
                subInsights: []
              },
              {
                name: '定位数据',
                description: '失败原因(定位服务没有开启，请在设置中打开定位服务开关)',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '通知诊断',
            description: null,
            level: 'WARN',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'push_env',
                time: '23:32:57.197',
                lineNo: 38000,
                ts: 1721835177197,
                args: {
                  suggestPushType: '1'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '195',
                event: 'push_env',
                time: '23:32:58.568',
                lineNo: 38202,
                ts: 1721835178568,
                args: {
                  sysType: 'null',
                  suggestPushType: '1',
                  type: '1',
                  token: '58k6ayd1SjvNpn0BSa//OehQ43IpHsyAAItTuFOXXjjmHZC7g+g5e8ypENjJurrz'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '112',
                event: 'push_env',
                time: '23:32:58.716',
                lineNo: 38270,
                ts: 1721835178716,
                args: {
                  pushPer: 'false',
                  channel:
                    '{"Heytap PUSH":{"importance":"3","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"System Default Channel"},"boss_message_channel_id":{"importance":"4","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"聊天消息"},"channel_id_upgrade":{"importance":"2","sound":"content:\\/\\/settings\\/system\\/notification_sound","name":"升级"}}'
                }
              }
            ],
            subInsights: [
              {
                name: '通知渠道',
                description: '支持',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '通知权限',
                description: '未开启',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '类别：System Default Channel',
                description: '通知已开启 | 有声音',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '类别：聊天消息',
                description: '通知已开启 | 有声音',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '运行环境诊断',
            description: null,
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '114',
                event: 'shu_zi',
                time: '23:32:57.298',
                lineNo: 38027,
                ts: 1721835177298,
                args: {
                  processId: '1',
                  time: '94',
                  queryId: 'DUvBL3owcsvCyzY7zqaaPN7zun_-ZlRUfcd0RFV2Qkwzb3djc3ZDeXpZN3pxYWFQTjd6dW5fLVpsUlVmY2Qwc2h1'
                }
              },
              {
                tag: 'BaseInfoAnalytic',
                pid: '12565',
                tid: '112',
                event: 'run_time_env',
                time: '23:32:58.715',
                lineNo: 38268,
                ts: 1721835178715,
                args: {
                  NetProxy: 'false',
                  IPV6Only: 'false',
                  GPSService: 'false',
                  VPN: 'false',
                  LocPer: '1',
                  AutoTime: '1',
                  SysDark: 'false',
                  AlwaysFinishActivities: '0',
                  AppDark: 'false',
                  ExternalStorage: '144748/231604',
                  InnerStorage: '144748/231604'
                }
              }
            ],
            subInsights: [
              {
                name: '数盟',
                description: '获取正常',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '网络代理设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '系统自动时间设置',
                description: '已开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'Activity(页面)不保留活动设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'VPN设置',
                description: '未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '深色模式',
                description: '系统:未开启  App:未开启',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'IPv6Only',
                description: '网络支持IPV4/IPV6',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '内置存储空间',
                description: '使用率：62.5%',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: '外置存储空间',
                description: '使用率：62.5%',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '账号信息',
            description: '******** | BOSS',
            level: 'BASE',
            data: null,
            detailLog: [
              {
                tag: 'AccountAnalytic',
                pid: '12565',
                tid: '92',
                event: 'account_info',
                time: '23:32:57.156',
                lineNo: 37981,
                ts: *************,
                args: {
                  account: 'Account{uid=********, identity=1, phone=***********, regionCode=+86, register=false,t=true,t2=false,wt=true,zpAt=false}'
                }
              }
            ],
            subInsights: []
          },
          {
            name: '启动任务耗时',
            description: '总耗时:1117ms',
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'StartupAnalytic',
                pid: '12565',
                tid: '150',
                event: 'pipeline',
                time: '23:32:57.440',
                lineNo: 38066,
                ts: *************,
                args: {
                  thread: 'StartupPipeline',
                  time: '1',
                  class: 'DataKernelWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '12565',
                tid: '150',
                event: 'pipeline',
                time: '23:32:57.454',
                lineNo: 38074,
                ts: 1721835177454,
                args: {
                  thread: 'StartupPipeline',
                  time: '14',
                  class: 'ApmWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'pipeline',
                time: '23:32:57.456',
                lineNo: 38075,
                ts: 1721835177456,
                args: {
                  thread: 'main',
                  time: '2',
                  class: 'PublicInitWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'pipeline',
                time: '23:32:57.592',
                lineNo: 38090,
                ts: 1721835177592,
                args: {
                  thread: 'main',
                  time: '135',
                  class: 'DBUpgradeWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'pipeline',
                time: '23:32:57.593',
                lineNo: 38092,
                ts: 1721835177593,
                args: {
                  thread: 'main',
                  time: '1',
                  class: 'ActiveWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '12565',
                tid: '150',
                event: 'pipeline',
                time: '23:32:57.602',
                lineNo: 38096,
                ts: 1721835177602,
                args: {
                  thread: 'StartupPipeline',
                  time: '9',
                  class: 'BasicDataWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '12565',
                tid: '150',
                event: 'pipeline',
                time: '23:32:58.547',
                lineNo: 38188,
                ts: 1721835178547,
                args: {
                  thread: 'StartupPipeline',
                  time: '944',
                  class: 'ContactWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'pipeline',
                time: '23:32:58.548',
                lineNo: 38189,
                ts: 1721835178548,
                args: {
                  thread: 'main',
                  time: '1',
                  class: 'NetworkWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '12565',
                tid: '150',
                event: 'pipeline',
                time: '23:32:58.554',
                lineNo: 38196,
                ts: 1721835178554,
                args: {
                  thread: 'StartupPipeline',
                  time: '6',
                  class: 'AppImeiWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '12565',
                tid: '150',
                event: 'pipeline',
                time: '23:32:58.554',
                lineNo: 38197,
                ts: 1721835178554,
                args: {
                  thread: 'StartupPipeline',
                  time: '0',
                  class: 'AdvertWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'pipeline',
                time: '23:32:58.556',
                lineNo: 38198,
                ts: 1721835178556,
                args: {
                  thread: 'main',
                  time: '2',
                  class: 'CompleteWork'
                }
              },
              {
                tag: 'StartupAnalytic',
                pid: '12565',
                tid: '2*',
                event: 'pipeline',
                time: '23:32:58.556',
                lineNo: 38199,
                ts: 1721835178556,
                args: {
                  totalTime: '1117'
                }
              }
            ],
            subInsights: [
              {
                name: 'DataKernelWork',
                description: '耗时:1ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ApmWork',
                description: '耗时:14ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'PublicInitWork',
                description: '耗时:2ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'DBUpgradeWork',
                description: '耗时:135ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ActiveWork',
                description: '耗时:1ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'BasicDataWork',
                description: '耗时:9ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'ContactWork',
                description: '耗时:944ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'NetworkWork',
                description: '耗时:1ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'AppImeiWork',
                description: '耗时:6ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'AdvertWork',
                description: '耗时:0ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              },
              {
                name: 'CompleteWork',
                description: '耗时:2ms',
                level: 'INFO',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          },
          {
            name: '联系人存储',
            description: '正常',
            level: 'INFO',
            data: null,
            detailLog: [
              {
                tag: 'ChatAnalytic',
                pid: '12565',
                tid: '203',
                event: 'contact_async_unhandled_message',
                time: '23:33:04.075',
                lineNo: 38935,
                ts: 1721835184075,
                args: {
                  what: '11101',
                  delay: '2512',
                  maxMsg: '515',
                  msgCount: '516',
                  time: '106',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '12565',
                tid: '203',
                event: 'contact_async_unhandled_message',
                time: '23:33:07.718',
                lineNo: 39296,
                ts: 1721835187718,
                args: {
                  what: '29999',
                  delay: '6152',
                  maxMsg: '541',
                  msgCount: '499',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '12565',
                tid: '203',
                event: 'contact_async_unhandled_message',
                time: '23:33:08.213',
                lineNo: 39487,
                ts: 1721835188213,
                args: {
                  what: '11101',
                  delay: '6645',
                  maxMsg: '830',
                  msgCount: '831',
                  time: '269',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '12565',
                tid: '203',
                event: 'contact_async_unhandled_message',
                time: '23:33:12.929',
                lineNo: 40547,
                ts: 1721835192929,
                args: {
                  what: '11101',
                  delay: '11348',
                  maxMsg: '1003',
                  msgCount: '1004',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '12565',
                tid: '203',
                event: 'contact_async_unhandled_message',
                time: '23:33:13.828',
                lineNo: 41089,
                ts: 1721835193828,
                args: {
                  what: '11101',
                  delay: '5864',
                  maxMsg: '1381',
                  msgCount: '999',
                  time: '1',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '12565',
                tid: '203',
                event: 'contact_async_unhandled_message',
                time: '23:33:14.490',
                lineNo: 41337,
                ts: 1721835194490,
                args: {
                  what: '29999',
                  delay: '1707',
                  maxMsg: '1381',
                  msgCount: '499',
                  time: '0',
                  status: 'ing'
                }
              },
              {
                tag: 'ChatAnalytic',
                pid: '12565',
                tid: '203',
                event: 'contact_async_unhandled_message',
                time: '23:33:14.765',
                lineNo: 41522,
                ts: 1721835194765,
                args: {
                  what: '11301',
                  delay: '546',
                  maxMsg: '1381',
                  msgCount: '0',
                  time: '0',
                  status: 'end'
                }
              }
            ],
            subInsights: [
              {
                name: '耗时任务ID：11101',
                description: '延迟：11348\t耗时：1',
                level: 'WARN',
                data: null,
                detailLog: [],
                subInsights: []
              }
            ]
          }
        ],
        insightInfo: '进程:12565\t时间：23:32:57--23:59:59\t持续时间：1622.852s\n【当前版本】\t12.130\t已登录\n【定位诊断】❗️\t\t\n\t[定位服务]\t未开启\t\n\t[定位数据]\t失败原因(定位服务没有开启，请在设置中打开定位服务开关)\t\n【通知诊断】❗️\t\t\n\t[通知权限]\t未开启\t\n【账号信息】️\t******** | BOSS\t\n'
      }
    ],
    fileName: 'tlog2_main_20240724_772055807214985286.xlog',
    date8: '20240724',
    notifier:
      '日志诊断\t用户：********\t日期：07-24\n====================\n进程:15503\t时间：20:13:53--20:14:30\t持续时间：37.626s\n【当前版本】\t12.130\t已登录\n【通知诊断】❗️\t\t\n\t[通知权限]\t未开启\t\n【账号信息】️\t******** | BOSS\t\n【启动任务耗时】❗️\t总耗时:3431ms\t\n\t[DBUpgradeWork]\t耗时:2475ms\t\n【联系人存储】❗️\t异常(数据丢失)\t未完成任务个数：7541\t\n\t[耗时任务ID：11305]\t延迟：13166\t耗时：114\t\n\t[耗时任务ID：29999]\t延迟：30247\t耗时：1\t\n\t[耗时任务ID：11101]\t延迟：30461\t耗时：67\t\n——————————————————————————\n进程:26745\t时间：21:03:52--21:37:27\t持续时间：2014.922s\n【当前版本】\t12.130\t已登录\n【通知诊断】❗️\t\t\n\t[通知权限]\t未开启\t\n【账号信息】️\t******** | BOSS\t\n【新增联系人存储异常】❗️\t未完成任务：2763个\t\n——————————————————————————\n进程:24120\t时间：21:45:19--22:23:41\t持续时间：2302.107s\n【当前版本】\t12.130\t已登录\n【定位诊断】❗️\t\t\n\t[定位服务]\t未开启\t\n【通知诊断】❗️\t\t\n\t[通知权限]\t未开启\t\n【账号信息】️\t******** | BOSS\t\n【新增联系人存储异常】❗️\t未完成任务：1个\t\n——————————————————————————\n进程:29608\t时间：22:25:34--22:27:20\t持续时间：105.671s\n【当前版本】\t12.130\t已登录\n【定位诊断】❗️\t\t\n\t[定位服务]\t未开启\t\n\t[定位数据]\t失败原因(定位服务没有开启，请在设置中打开定位服务开关)\t\n【通知诊断】❗️\t\t\n\t[通知权限]\t未开启\t\n【账号信息】️\t******** | BOSS\t\n——————————————————————————\n进程:5873\t时间：23:00:27--23:01:13\t持续时间：46.728s\n【当前版本】\t12.130\t已登录\n【定位诊断】❗️\t\t\n\t[定位服务]\t未开启\t\n【通知诊断】❗️\t\t\n\t[通知权限]\t未开启\t\n【账号信息】️\t******** | BOSS\t\n【启动任务耗时】❗️\t总耗时:3674ms\t\n\t[DBUpgradeWork]\t耗时:2651ms\t\n——————————————————————————\n进程:12565\t时间：23:32:57--23:59:59\t持续时间：1622.852s\n【当前版本】\t12.130\t已登录\n【定位诊断】❗️\t\t\n\t[定位服务]\t未开启\t\n\t[定位数据]\t失败原因(定位服务没有开启，请在设置中打开定位服务开关)\t\n【通知诊断】❗️\t\t\n\t[通知权限]\t未开启\t\n【账号信息】️\t******** | BOSS\t\n——————————————————————————\n',
    time: 6414,
    fileId: '8360AA40D35A471ABFD86B4580D8A7B9'
  }
}
