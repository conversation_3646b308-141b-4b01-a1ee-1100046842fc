import request from '@/utils/request'
import qs from 'qs'

export function insertVersion(data) {
  return request({
    url: '/boss/deployment/insert_version',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data
  })
}

export function updateVersion(data) {
  return request({
    url: '/boss/deployment/update_version',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data
  })
}

export function getListByVersion(params) {
  return request({
    url: '/boss/deployment/get_list_by_deploymentVersion',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: params
  })
}

export function findAllByVersion() {
  return request({
    url: '/boss/deployment/find_all_by_version',
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

export function deleteVersion(id) {
  return request({
    url: '/boss/deployment/delete/' + id,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}
