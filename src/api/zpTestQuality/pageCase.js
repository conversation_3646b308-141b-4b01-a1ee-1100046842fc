import request from '@/utils/request'
import qs from 'qs'

//添加
export function addPageCase(data) {
  return request({
    url: '/boss/agreementCase/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//查询
export function getPageCase(id, agreementId, pageIndex, pageSize) {
  return request({
    url: '/boss/agreementCase/list',
    method: 'get',
    params: {
      id,
      agreementId,
      pageIndex,
      pageSize
    }
  })
}

//删除
export function deletePageCase(id) {
  return request({
    url: '/boss/agreementCase/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//修改
export function updatePageCase(data) {
  return request({
    url: '/boss/agreementCase/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
