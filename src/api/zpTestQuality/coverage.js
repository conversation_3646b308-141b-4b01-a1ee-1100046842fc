import request from '@/utils/request'
import qs from 'qs'

//获取diff代码
export function getCodeDiffList(baseVersion, gitUrl, nowVersion) {
  return request({
    url: '/boss/code/diff/git/list',
    method: 'get',
    params: {
      baseVersion,
      gitUrl,
      nowVersion
    },
    timeout: 1000 * 1000
  })
}

//获取git分支
export function getBranchList(gitUrl) {
  return request({
    url: '/boss/code/diff/git/branch',
    method: 'get',
    params: {
      gitUrl
    },
    timeout: 1000 * 1000
  })
}

//获取覆盖率结果报告
export function getCoverResultList(uuid, pageIndex, pageSize) {
  return request({
    url: '/boss/code/cov/getCoverResultList',
    method: 'get',
    params: {
      uuid,
      pageIndex,
      pageSize
    }
  })
}

//生成增量代码覆盖率
export function triggerEnvCov(data) {
  return request({
    url: '/boss/code/cov/triggerEnvCov',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data,
    timeout: 1000 * 1000
  })
}
