import request from '@/utils/request'
import qs from 'qs'

//查询
export function getEchoFeedback(searchType, keyword, isBug, version, device, sideName, startTime, endTime, pageIndex, pageSize) {
  return request({
    url: '/boss/aicraft/echo/list',
    method: 'get',
    params: {
      searchType,
      keyword,
      isBug,
      device,
      version,
      sideName,
      startTime,
      endTime,
      pageIndex,
      pageSize
    }
  })
}

//更新反馈
export function editEchoFeedback(data) {
  return request({
    url: '/boss/aicraft/echo/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//校验
export function checkContent(content, contents) {
  return request({
    url: '/boss/aicraft/echo/checkContent',
    method: 'get',
    params: {
      content,
      contents
    }
  })
}

//查询问题的相似度
export function getEchoFeedbackSimilar(title, threshold, time) {
  return request({
    url: '/ai_server/ai_aicraft/get_feedback_similar',
    method: 'get',
    params: {
      title,
      threshold,
      time
    }
  })
}

//查询问题的相似度
export function getEchoFeedbackSimilarByGroup(keyword, threshold, time) {
  return request({
    url: '/ai_server/ai_aicraft/get_feedback_similar_by_keyword',
    method: 'get',
    params: {
      keyword,
      threshold,
      time
    }
  })
}

//添加监测
export function addEchoFeedbackMonitor(data) {
  return request({
    url: '/boss/aicraft/echo/monitor/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新监测
export function editEchoFeedbackMonitor(data) {
  return request({
    url: '/boss/aicraft/echo/monitor/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteEchoFeedbackMonitor(id, name) {
  return request({
    url: '/boss/aicraft/echo/monitor/delete',
    method: 'get',
    params: {
      id,
      name
    }
  })
}

//查询
export function getEchoFeedbackMonitor(keyword, startTime, endTime) {
  return request({
    url: '/boss/aicraft/echo/monitor/list',
    method: 'get',
    params: {
      keyword,
      startTime,
      endTime
    },
    timeout: 60 * 1000
  })
}

//查询单个
export function getEchoFeedbackMonitorById(id, keyword, startTime, endTime, pageIndex, pageSize) {
  return request({
    url: '/boss/aicraft/echo/monitor/listById',
    method: 'get',
    params: {
      id,
      keyword,
      startTime,
      endTime,
      pageIndex,
      pageSize
    }
  })
}

//查询分类
export function getEchoFeedbackMonitorCountByCategory(id, startTime, endTime) {
  return request({
    url: '/boss/aicraft/echo/monitor/countByCategory',
    method: 'get',
    params: {
      id,
      startTime,
      endTime
    }
  })
}

//查询版本
export function getEchoFeedbackVersion() {
  return request({
    url: '/boss/aicraft/echo/version',
    method: 'get',
    params: {}
  })
}
