import request from '@/utils/requestPrecision'
import qs from 'qs'

//获取上线记录，每天第一条
export function getRecordDataList(email, serviceName, env, startTime, endTime, pageIndex, pageSize) {
  return request({
    url: '/boss/code/precision/getRecordGroupV2',
    method: 'get',
    params: {
      serviceName,
      env,
      email,
      startTime,
      endTime,
      pageIndex,
      pageSize
    }
  })
}

//查询
export function getRecordList(email, serviceName, startTime, endTime, pageIndex, pageSize) {
  return request({
    url: '/boss/code/precision/getServiceName',
    method: 'get',
    params: {
      serviceName,
      email,
      startTime,
      endTime,
      pageIndex,
      pageSize
    }
  })
}

//查询
export function getRecordListV2(email, gitUrl, nowVersion, pageIndex, pageSize) {
  return request({
    url: '/boss/code/precision/getServiceNameV2',
    method: 'get',
    params: {
      email,
      gitUrl,
      nowVersion,
      pageIndex,
      pageSize
    }
  })
}

export function getTcloudPublishRecord(id) {
  return request({
    url: '/boss/code/precision/getTcloudPublishRecord',
    method: 'get',
    params: {
      id
    }
  })
}

export function addPipeline(data) {
  return request({
    url: '/boss/code/precision/insert',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data
  })
}

export function updatePipeline(data) {
  return request({
    url: '/boss/code/precision/update',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data
  })
}

export function getPipelineList(serviceName, pageIndex, pageSize) {
  return request({
    url: '/boss/code/precision/getPipelineByServiceName?serviceName=' + serviceName + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize,
    method: 'get'
  })
}

export function deletePipeline(recordId) {
  return request({
    url: '/boss/code/precision/del/' + recordId,
    method: 'get'
  })
}
// export function runPipeline (data) {
//   return request({
//     url: '/boss/external/api/publishNotice',
//     method: 'post',
//     headers: { 'content-type': 'application/json' },
//     data: data
//   })
// }

export function runPipeline(recordId, isPack) {
  return request({
    url: '/boss/code/scan/start/task?recordId=' + recordId + '&isPack=' + isPack,
    method: 'get'
  })
}

//删除
export function deleteRecord(id) {
  return request({
    url: '/boss/code/precision/deleteRecord',
    method: 'get',
    params: {
      id
    }
  })
}

//获取diff代码
export function getCodeDiffList(baseVersion, gitUrl, nowVersion) {
  return request({
    url: '/boss/code/diff/git/list',
    method: 'get',
    params: {
      baseVersion,
      gitUrl,
      nowVersion
    },
    timeout: 1000 * 1000
  })
}

//获取git分支
export function getBranchList(gitUrl) {
  return request({
    url: '/boss/code/diff/git/branch',
    method: 'get',
    params: {
      gitUrl
    },
    timeout: 1000 * 1000
  })
}

//获取覆盖率结果报告
export function getCoverResultList(uuid, pageIndex, pageSize) {
  return request({
    url: '/boss/code/cov/getCoverResultList',
    method: 'get',
    params: {
      uuid,
      pageIndex,
      pageSize
    }
  })
}

//生成增量代码覆盖率
export function triggerEnvCov(data) {
  return request({
    url: '/boss/code/cov/triggerEnvCov',
    method: 'post',
    headers: { 'content-type': 'application/json' },
    data: data,
    timeout: 1000 * 1000
  })
}

//代码submit
export function getCodeCallSubmit(data) {
  return request({
    url: '/boss/code/call/submit',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//代码getresult
export function getCodeCallResult(serverName) {
  return request({
    url: '/boss/code/call/getResult',
    method: 'get',
    params: {
      serverName
    }
  })
}

//单个代码submit
export function getCodeCallRecordId(recordId) {
  return request({
    url: '/boss/code/call/submit/recordId',
    method: 'get',
    params: {
      recordId
    }
  })
}

//代码getresult
export function getCodeCallRecordIdResult(recordId) {
  return request({
    url: '/boss/code/call/getResult/recordId',
    method: 'get',
    params: {
      recordId
    }
  })
}
