import request from '@/utils/request'
import qs from 'qs'

//添加
export function addPage(data) {
  return request({
    url: '/boss/agreement/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deletePage(id) {
  return request({
    url: '/boss/agreement/delete',
    method: 'get',
    params: {
      id
    }
  })
}

//查询页面
export function getPage(id, keyword, status, type, service, pageIndex, pageSize) {
  return request({
    url: '/boss/agreement/list',
    method: 'get',
    params: {
      id,
      keyword,
      status,
      type,
      service,
      pageIndex,
      pageSize
    }
  })
}

//修改
export function updatePage(data) {
  return request({
    url: '/boss/agreement/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
