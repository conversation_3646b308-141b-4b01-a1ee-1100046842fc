import request from '@/utils/requestPrecision'
import qs from 'qs'

//查询diff代码信息
export function getCodeDiffInfo(recordId) {
  return request({
    url: '/boss/code/report/getInfo',
    method: 'get',
    params: {
      recordId
    },
    timeout: 60 * 1000
  })
}

//查询diff代码的相关接口
export function getdiffRelateInterface(recordId) {
  return request({
    url: '/boss/code/reportV2/getdiffRelateInterface',
    method: 'get',
    params: {
      recordId
    },
    timeout: 60 * 1000
  })
}

//统计
export function getReportStatistics(recordId) {
  return request({
    url: '/boss/code/report/getReportStatistics',
    method: 'get',
    params: {
      recordId
    },
    timeout: 60 * 1000
  })
}

//获取方法链路
export function getMethodLink(recordId) {
  return request({
    url: '/boss/code/report/getMethodLink',
    method: 'get',
    params: {
      recordId
    },
    timeout: 60 * 1000
  })
}
