import request from '@/utils/request'
import qs from 'qs'

//获取
export function getStatInfo() {
  return request({
    url: '/boss/stat/info',
    method: 'get',
    params: {}
  })
}

//获取每日执行用例数
export function getDailyExecuteCase() {
  return request({
    url: '/boss/stat/daily/executeCase',
    method: 'get',
    params: {}
  })
}

//获取接口用例分布
export function getInterfaceAndCaseDistribution() {
  return request({
    url: '/boss/stat/interfaceAndCase/distribution',
    method: 'get',
    params: {}
  })
}
