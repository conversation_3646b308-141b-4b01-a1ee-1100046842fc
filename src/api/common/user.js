import request from '@/utils/request'
import qs from 'qs'

export function login(data) {
  return request({
    url: '/boss/user/login',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

export function getInfo(token) {
  return request({
    url: '/boss/user/info',
    method: 'get',
    params: { token }
  })
}

export function logout() {
  return request({
    url: '/boss/user/logout',
    method: 'post'
  })
}

export function getInfoIuc(token, ticket, secret_key, appId) {
  return request({
    url: '/boss/iuc/user/info',
    method: 'get',
    params: {
      token,
      ticket,
      secret_key,
      appId
    },
    timeout: 3 * 1000
  })
}

export function loginIuc(t_uc) {
  return request({
    url: '/boss/iuc/user/login',
    method: 'get',
    params: {
      t_uc
    }
  })
}

export function logoutIuc(t_uc, callback) {
  return request({
    url: '/boss/iuc/user/logout',
    method: 'get',
    params: {
      t_uc,
      callback
    }
  })
}
