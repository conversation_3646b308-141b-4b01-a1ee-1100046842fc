import request from '@/utils/request'
import qs from 'qs'

// 通用新后台 接口调用方法
export function sendRequest(data) {
  return request({
    url: '/boss/new/common',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

// 通用get post 接口调用方法
export function sendRequestCommon(data) {
  return request({
    url: '/boss/request/common',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

// 通用get post 接口调用方法 带有签名方法）适合自己公司
export function sendRequestWithKey(data) {
  return request({
    url: '/request/key',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//rc4 请求方法
export function sendRequestRc4(data, key) {
  return request({
    url: '/request/rc4?data=' + data + '&key=' + key,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//请求 Yapi方法
export function sendRequestYApi(search) {
  return request({
    url: '/skip/Yapi?q=' + search,
    method: 'get',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}

//搜索公司成员信息
export function searchUserInfoList(email, name, seq) {
  return request({
    url: '/oa/onLine/get/userInfoList',
    method: 'get',
    params: {
      email,
      name,
      seq
    }
  })
}

// 通用get post 接口调用方法
export function sendRequestCommonToQuality(data) {
  return request({
    url: '/boss/tools/common/commonRequestToQuality',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
