import request from '@/utils/request'
import qs from 'qs'

//添加
export function addActionCase(data) {
  return request({
    url: '/boss/action/case/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteActionCase(id, name) {
  return request({
    url: '/boss/action/case/delete',
    method: 'get',
    params: {
      id: id,
      name: name
    }
  })
}

//获取
export function getActionCaseById(actionId) {
  return request({
    url: '/boss/action/case/listByActionId',
    method: 'get',
    params: {
      actionId
    }
  })
}

//更新
export function updateActionCase(data) {
  return request({
    url: '/boss/action/case/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
