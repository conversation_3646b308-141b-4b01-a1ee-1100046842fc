import request from '@/utils/request'
import qs from 'qs'

//获取
export function getActionLog(startTime, endTime, current, ratioType, ratio, pageIndex, pageSize) {
  return request({
    url: '/boss/action/log/list',
    method: 'get',
    params: {
      startTime,
      endTime,
      current,
      ratioType,
      ratio,
      pageIndex,
      pageSize
    }
  })
}

//获取
export function getActionLogDetail(serialNo, current, ratioType, ratio, pageIndex, pageSize) {
  return request({
    url: '/boss/action/log/serialNo/detailList',
    method: 'get',
    params: {
      serialNo,
      current,
      ratioType,
      ratio,
      pageIndex,
      pageSize
    }
  })
}
