import request from '@/utils/request'
import qs from 'qs'

//添加
export function addBusinessTask(data) {
  return request({
    url: '/boss/flowtrack/addTask',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteBusinessTask(id) {
  return request({
    url: `/boss/flowtrack/deleteTask/${id}`,
    method: 'get',
    params: {}
  })
}

//获取
export function getBusinessTask(taskName, pageIndex, pageSize) {
  return request({
    url: '/boss/flowtrack/getAllTasks',
    method: 'get',
    params: {
      taskName,
      pageIndex,
      pageSize
    }
  })
}

//更新
export function updateBusinessTask(data) {
  return request({
    url: '/boss/flowtrack/updateTask',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}
