import request from '@/utils/request'
import qs from 'qs'

//添加
export function addActionGroup(data) {
  return request({
    url: '/boss/actionGroup/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteActionGroup(id) {
  return request({
    url: '/boss/actionGroup/delete',
    method: 'get',
    params: {
      id: id
    }
  })
}

//获取
export function getActionGroup(groupId, isContainAction) {
  return request({
    url: '/boss/actionGroup/getAllV2',
    method: 'get',
    params: {
      groupId,
      isContainAction
    }
  })
}

//更新
export function updateActionGroup(data) {
  return request({
    url: '/boss/actionGroup/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//获取所有父级模块
export function getAllParentList(groupId) {
  return request({
    url: '/boss/actionGroup/allParent/listV2',
    method: 'get',
    params: {
      groupId
    }
  })
}

//移动分组
export function moveActionGroup(frontId, afterId, type) {
  return request({
    url: '/boss/actionGroup/move',
    method: 'get',
    params: {
      frontId,
      afterId,
      type
    }
  })
}
