import request from '@/utils/request'
import qs from 'qs'

//获取
export function getActionMonitor(searchType, keywords, groupId, topicType, status, level, startTime, endTime, compareTime, pageIndex, pageSize) {
  return request({
    url: '/boss/action/monitor/list',
    method: 'get',
    params: {
      searchType,
      keywords,
      groupId,
      topicType,
      startTime,
      endTime,
      status,
      level,
      compareTime,
      pageIndex,
      pageSize
    },
    timeout: 60 * 1000
  })
}

export function getActionMonitorById(actionId, startTime, endTime, compareTime) {
  return request({
    url: '/boss/action/monitor/byActionId',
    method: 'get',
    params: {
      actionId,
      startTime,
      endTime,
      compareTime
    }
  })
}
