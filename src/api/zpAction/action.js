import request from '@/utils/request'
import qs from 'qs'

//添加
export function addAction(data) {
  return request({
    url: '/boss/action/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//删除
export function deleteAction(id) {
  return request({
    url: `/boss/action/delete`,
    method: 'get',
    params: {
      id: id
    }
  })
}

//获取
export function searchAction(groupId, keywords, topicType, status, level, actionId, pageIndex, pageSize) {
  return request({
    url: '/boss/action/list',
    method: 'get',
    params: {
      keywords,
      groupId,
      topicType,
      status,
      level,
      actionId,
      pageIndex,
      pageSize
    },
    timeout: 60 * 1000
  })
}

//获取
export function getActionById(actionId) {
  return request({
    url: '/boss/action/listByActionId',
    method: 'get',
    params: {
      actionId
    }
  })
}

//更新
export function updateAction(data) {
  return request({
    url: '/boss/action/update',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//更新
export function executeDebugCase(data) {
  return request({
    url: '/boss/action/execute',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//同步
export function synchronizeData(actionId) {
  return request({
    url: '/boss/action/synchronizeData',
    method: 'get',
    params: {
      actionId
    }
  })
}
