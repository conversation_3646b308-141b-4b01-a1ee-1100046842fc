import request from '@/utils/request'
import qs from 'qs'

//获取需求进度
export function getRequirementMetrics(libraryId, version) {
  return request({
    url: '/boss/quality/statistics/requirement',
    method: 'get',
    params: {
      libraryId,
      version
    }
  })
}

//获取计划进度
export function getTestPlan(libraryId, version) {
  return request({
    url: '/boss/quality/statistics/testPlan',
    method: 'get',
    params: {
      libraryId,
      version
    }
  })
}

//bug状态查询
export function getBugStatistics(libraryId, version, recordId, workItemId, spaceId) {
  return request({
    url: '/boss/quality/statistics/bug',
    method: 'get',
    params: {
      libraryId,
      version,
      recordId,
      workItemId,
      spaceId
    }
  })
}

//目标成绩
export function geKpiList(startTime, endTime, group) {
  return request({
    url: '/boss/statistics/kpi/list',
    method: 'get',
    params: {
      startTime,
      endTime,
      group
    }
  })
}

//目标成绩
export function getDevelopKpiList(startTime, endTime, group, version, type, projectId) {
  return request({
    url: '/boss/statistics/kpi/developer/listV2',
    method: 'get',
    params: {
      startTime,
      endTime,
      group,
      version,
      type,
      projectId
    }
  })
}

//分组bug
export function queryBugCountByGroup(startTime, endTime, group) {
  return request({
    url: '/boss/statistics/kpi/developer/queryBugCountByGroup',
    method: 'get',
    params: {
      startTime,
      endTime,
      group
    }
  })
}
