import request from '@/utils/request'
import qs from 'qs'

//获取ios包列表
export function getiOSProjectList() {
  return request({
    url: '/zpPackage/projectList',
    method: 'get'
  })
}

//获取安装包列表
export function getiOSPackageList(projectType) {
  return request({
    url: '/zpPackage/packageList',
    method: 'get',
    params: {
      projectType
    }
  })
}

//获取安装包列表
export function getiOSPackageInfoLog(projectType, buildId) {
  return request({
    url: '/zpPackage/packageInfoLog',
    method: 'get',
    params: {
      projectType,
      buildId
    }
  })
}

export function clearPackage(projectType, buildId) {
  return request({
    url: 'zpPackage/clearPackage',
    method: 'post',
    params: {
      projectType,
      buildId
    }
  })
}

//打包
export function buildPackage(projectType, branch, version, force) {
  return request({
    url: '/zpPackage/buildPackage',
    method: 'post',
    params: {
      projectType,
      branch,
      version,
      force
    }
  })
}

//打包状态
export function buildStatus(projectType) {
  return request({
    url: '/zpPackage/buildStatus',
    method: 'get',
    params: {
      projectType
    }
  })
}

//打包
export function buildingList(projectType) {
  return request({
    url: '/zpPackage/buildingList',
    method: 'get',
    params: {
      projectType
    }
  })
}

//获取问题列表
export function getBeforeIssue(platform, version, time) {
  return request({
    url: '/boss/testhub/grayIssue/getBeforeIssue',
    method: 'get',
    params: {
      platform,
      version,
      time
    }
  })
}

//发版标记校验
export function releaseCheck(platform, version, time, type, buildId) {
  return request({
    url: '/boss/testhub/grayIssue/getBeforeIssue',
    method: 'get',
    params: {
      platform,
      version,
      time,
      type,
      buildId
    }
  })
}

//获取包列表
export function getAndroidProjectList(name) {
  return request({
    url: '/boss/zppackage/projectList',
    method: 'get',
    params: {
      name
    }
  })
}

//获取安装包列表
export function getAndroidPackageList(projectType, env, branch, version, buildId, name) {
  return request({
    url: '/boss/zppackage/packageList',
    method: 'get',
    params: {
      projectType,
      env,
      branch,
      version,
      buildId,
      name
    }
  })
}

//获取安装分支和版本列表
export function getAndroidPackageVersionAndBranchList(projectType, env) {
  return request({
    url: '/boss/zppackage/packageVersionAndBranchList',
    method: 'get',
    params: {
      projectType,
      env
    }
  })
}

//获取安装包列表
export function getAndroidChannelPackageList(projectType) {
  return request({
    url: '/boss/zppackage/channelPackageList',
    method: 'get',
    params: {
      projectType
    }
  })
}

//打包状态
export function androidBuildStatus(jobName) {
  return request({
    url: '/boss/zppackage/buildStatus',
    method: 'get',
    params: {
      jobName
    }
  })
}

//打包
export function androidBuildPackage(jobName, branch) {
  return request({
    url: '/boss/zppackage/buildPackage',
    method: 'get',
    params: {
      jobName,
      branch
    }
  })
}

//打包
export function androidBuildingList(jobName) {
  return request({
    url: '/boss/zppackage/buildingList',
    method: 'get',
    params: {
      jobName
    }
  })
}

//打包时添加通知人
export function zppackageNotifyAdd(data) {
  return request({
    url: '/boss/zppackageNotify/add',
    method: 'post',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data)
  })
}

//取消排队
export function cancelBuid(jobName, buildId, type) {
  return request({
    url: '/boss/zppackage/stopBuildPackage',
    method: 'get',
    params: {
      jobName,
      buildId,
      type
    }
  })
}
