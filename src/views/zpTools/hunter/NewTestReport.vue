<template>
  <div class="plan-report">
    <div>
      <el-form :inline="true">
        <el-form-item label="项目:">
          <el-select v-model="projectType" placeholder="请选择项目类型" @change="versionSelect(projectType)">
            <el-option label="BOSS直聘" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="版本:">
          <el-select v-model="version" placeholder="请选择版本" style="width: 180px" @change="handleChange">
            <el-option v-for="(item, index) in versionList" :key="index" :label="item.recordName" :value="item.recordName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="测试阶段:">
          <el-select v-model="test_type" placeholder="请选择测试阶段" style="width: 180px">
            <el-option v-for="(item, index) in testStageList" :key="index" :label="item.recordName" :value="item.recordName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click.prevent="getStatInfoList()">
            <i class="el-icon-search"></i>
            查询
          </el-button>
        </el-form-item>
      </el-form>
      <div>
        <h3 class="report-title">报告概览</h3>
        <el-form ref="form" label-width="100px">
          <el-row>
            <el-row :gutter="12" style="margin-bottom: 20px">
              <el-col :span="5" style="margin-left: 20px">
                <el-card shadow="hover" class="el-card-totalCases">
                  <div style="color: #72767b; font-size: 15px">
                    缺陷总数
                    <div style="color: rgb(93, 207, 255); font-size: 30px">
                      {{ dubboBugStatisDetailList.bugTotalCount }}
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="5">
                <el-card shadow="hover" class="el-card-passedCases">
                  <div style="color: #72767b; font-size: 15px">
                    未解决缺陷数
                    <div style="color: rgb(115, 216, 151); font-size: 30px">
                      {{ dubboBugStatisDetailList.bugUnresolvedCount }}
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="5">
                <el-card shadow="hover" class="el-card-passingRate">
                  <div style="color: #72767b; font-size: 15px">
                    已修复缺陷数
                    <div style="color: rgb(255, 205, 93); font-size: 30px">
                      {{ dubboBugStatisDetailList.bugFixedCount }}
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="5">
                <el-card shadow="hover" class="el-card-doneRate">
                  <div style="color: #72767b; font-size: 15px">
                    已关闭缺陷数
                    <div style="color: rgb(218, 112, 214); font-size: 30px">
                      {{ dubboBugStatisDetailList.bugClosedCount }}
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-row>
        </el-form>
      </div>
      <div style="height: 5px; background-color: #eee; margin-bottom: 15px"></div>

      <div>
        <h3 class="report-title">结果分布</h3>
        <div v-loading="loading">
          <p>测试结果情况</p>
          <p v-if="test_type === '冒烟测试'">
            <template v-if="test_result.includes('不通过')">
              {{ test_result.split('不通过')[0] }}
              <span style="color: red">不通过</span>
              <span style="color: #666; margin-left: 10px">
                (
                <template v-if="!p1p2BugCondition">P1P2 bug数量>5;</template>
                <template v-if="!testCaseRateCondition">用例通过率&lt;80%;</template>
                <template v-if="!bugIndexCondition">bug指数>1</template>
                )
              </span>
            </template>
            <template v-else>
              {{ test_result.split('通过')[0] }}
              <span style="color: #75cc49">通过</span>
            </template>
          </p>
          <p v-if="test_type === '一轮测试'">
            <template v-if="test_result.includes('不通过')">
              {{ test_result.split('不通过')[0] }}
              <span style="color: red">不通过</span>
              <span style="color: #666; margin-left: 10px">
                (
                <template v-if="!p1p2BugCondition">有未解决的P1 P2 bug;</template>
                <template v-if="!testCaseRateCondition">用例执行率不超过70%;</template>
                <template v-if="!bugIndexCondition">创建时间开始到今天大于3天仍未解决的bug数>3;</template>
                <template v-if="!bugIndexCondition">(当前待修复Bug数 / 累计提交Bug总数) > 30%</template>
                )
              </span>
            </template>
            <template v-else>
              {{ test_result.split('通过')[0] }}
              <span style="color: #75cc49">通过</span>
            </template>
          </p>

          <p v-if="test_type === '服务端验收' || test_type === 'Web验收' || test_type === '客户端验收-IOS' || test_type === '客户端验收-Android'">
            <template v-if="test_result.includes('不通过')">
              {{ test_result.split('不通过')[0] }}
              <span style="color: red">不通过</span>
              <span style="color: #666; margin-left: 10px">
                (
                <template v-if="hasUnresolvedServerBug || hasUnresolvedWebBug || hasUnresolvedIosBug || hasUnresolvedAndroidBug">有待修复的bug;</template>
                )
              </span>
            </template>
            <template v-else>
              {{ test_result.split('通过')[0] }}
              <span style="color: #75cc49">通过</span>
            </template>
          </p>

          <p v-if="newTestProgressList.nagetive_and_not_fill_remark_wiki_list.length > 0">
            在{{ version.replace('.', '').replace(' ', '') }}阶段中，版本需求有
            <span style="color: red; text-decoration: underline">{{ newWikiTypeResult[0].value }}</span>
            个，非版本需求有
            <span style="color: red; text-decoration: underline">{{ newWikiTypeResult[1].value }}</span>
            个，技术需求有
            <span style="color: red; text-decoration: underline">{{ newWikiTypeResult[2].value }}</span>
            个，其中{{ test_type }}结果异常的需求有：
          </p>
          <p v-else>
            在{{ version.replace('.', '').replace(' ', '') }}阶段中，版本需求有
            <span style="color: red; text-decoration: underline">{{ newWikiTypeResult[0].value }}</span>
            个，非版本需求有
            <span style="color: red; text-decoration: underline">{{ newWikiTypeResult[1].value }}</span>
            个，技术需求有
            <span style="color: red; text-decoration: underline">{{ newWikiTypeResult[2].value }}</span>
            个，无结果异常的需求
          </p>
          <el-row>
            <el-col :span="14">
              <el-table :data="newTestProgressList.nagetive_and_not_fill_remark_wiki_list" border :stripe="true" style="width: 100%; margin-right: 20px" :header-cell-style="{ 'background-color': '#f2f2f2', 'font-weight': 'bold', color: '#272626', heigth: '39px', 'text-align': 'center' }">
                <el-table-column prop="wiki_name" label="需求标题" mix-width="100" align="left">
                  <template slot-scope="scope">
                    <a :href="scope.row.wiki_url" target="_blank">{{ scope.row.wiki_name }}</a>
                  </template>
                </el-table-column>
                <el-table-column prop="test_result" label="测试结果" align="center" mix-width="100"></el-table-column>
                <el-table-column prop="remark" label="测试备注" align="center" mix-width="100"></el-table-column>
              </el-table>
            </el-col>
            <!-- 需求类型分布 -->
            <el-col :span="10">
              <div ref="chartFive" style="height: 300px; width: 500px; margin-left: 20px" :auto-resize="true"></div>
            </el-col>
          </el-row>
          <!-- 未关闭需求列表，只在服务端验收加 -->
          <p v-if="test_type == '服务端验收'">未关闭需求列表</p>
          <el-row v-if="test_type == '服务端验收'">
            <el-table :data="acceptence_wiki_list" border :stripe="true" style="width: 80cap; margin-bottom: 20px" :header-cell-style="{ 'background-color': '#f2f2f2', 'font-weight': 'bold', color: '#272626', heigth: '39px', 'text-align': 'center' }">
              <el-table-column prop="name" label="需求名称" min-width="300" align="left">
                <template slot-scope="scope">
                  <a :href="scope.row.visitUrl" target="_blank">{{ scope.row.name }}</a>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="120" align="center"></el-table-column>
            </el-table>
          </el-row>
          <!-- 覆盖度统计 -->
          <p>覆盖度统计</p>
          <p v-if="test_type == '冒烟测试'">
            截至{{ test_type }}结束，版本需求覆盖度 低于
            <span style="color: red; text-decoration: underline">15%</span>
            的列表如下：请关注相关需求测试进度：
          </p>
          <p v-else-if="test_type == '一轮测试'">
            截至{{ test_type }}结束，版本需求覆盖度 低于
            <span style="color: red; text-decoration: underline">50%</span>
            的列表如下：请关注相关需求测试进度：
          </p>
          <p v-else-if="test_type == '服务端验收'">截至{{ test_type }}阶段结束，版本需求&已上线的非版本需求服务端覆盖度分布如下：</p>
          <p v-else-if="test_type == 'Web验收'">截至{{ test_type }}阶段结束，版本需求&已上线的非版本需求前端覆盖度分布如下：</p>
          <p v-else-if="test_type == '客户端验收-IOS'">截至IOS端验收测试结束，涉及IOS端的所有需求覆盖率分布如下：</p>
          <p v-else-if="test_type == '客户端验收-Android'">截至Android端验收测试结束，涉及Android端的所有需求覆盖率分布如下：</p>

          <el-row>
            <el-col :span="14">
              <el-table v-if="test_type == '冒烟测试' || test_type == '一轮测试'" :data="coverageStaticResult" border :stripe="true" style="width: 100%" :header-cell-style="{ 'background-color': '#f2f2f2', 'font-weight': 'bold', color: '#272626', heigth: '39px', 'text-align': 'center' }">
                <el-table-column prop="wiki_name" label="需求名称" mix-width="100">
                  <template slot-scope="scope">
                    <a :href="scope.row.wiki_url" target="_blank">{{ scope.row.wiki_name }}</a>
                  </template>
                </el-table-column>
                <el-table-column prop="coverage_rate_all" label="整体覆盖率" align="center" mix-width="100">
                  <template slot-scope="scope">{{ scope.row.coverage_rate_all }}%</template>
                </el-table-column>
                <el-table-column label="服务端" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.coverage_detail.SERVER === 0 ? '0%' : scope.row.coverage_detail.SERVER && scope.row.coverage_detail.SERVER != '无数据' ? scope.row.coverage_detail.SERVER + '%' : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="安卓" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.coverage_detail.ANDROID === 0 ? '0%' : scope.row.coverage_detail.ANDROID && scope.row.coverage_detail.ANDROID != '无数据' ? scope.row.coverage_detail.ANDROID + '%' : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="IOS" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.coverage_detail.IOS === 0 ? '0%' : scope.row.coverage_detail.IOS && scope.row.coverage_detail.IOS != '无数据' ? scope.row.coverage_detail.IOS + '%' : '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="前端" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.coverage_detail.FE === 0 ? '0%' : scope.row.coverage_detail.FE && scope.row.coverage_detail.FE != '无数据' ? scope.row.coverage_detail.FE + '%' : '-' }}
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="10">
              <!-- 覆盖度分布饼图 -->
              <div ref="coverageChart" style="height: 300px; width: 500px; margin-left: 20px" :auto-resize="true"></div>
            </el-col>
          </el-row>
          <div v-if="isAcceptance">
            <h3 class="report-title">覆盖度统计</h3>
            <div class="coverage-stats">
              <div class="coverage-overview">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="coverage-circle">
                      <div class="circle-wrapper">
                        <div class="circle-value">{{ coverageStats.totalCoverage }}%</div>
                        <div class="circle-label">总体覆盖度</div>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="15">
                    <el-row :gutter="20">
                      <el-col :span="6">
                        <div class="stat-item">
                          <div class="stat-value">{{ coverageStats.demandCount }}</div>
                          <div class="stat-label">需求数量</div>
                        </div>
                      </el-col>
                      <el-col :span="6">
                        <div class="stat-item">
                          <div class="stat-value">{{ coverageStats.projectCount }}</div>
                          <div class="stat-label">涉及项目</div>
                        </div>
                      </el-col>
                      <el-col :span="6">
                        <div class="stat-item">
                          <div class="stat-value">{{ coverageStats.developerCount }}</div>
                          <div class="stat-label">开发人员</div>
                        </div>
                      </el-col>
                      <el-col :span="6">
                        <div class="stat-item">
                          <div class="stat-value">{{ coverageStats.testerCount }}</div>
                          <div class="stat-label">测试人员</div>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20" style="margin-top: 20px">
                      <el-col :span="8">
                        <div class="stat-item">
                          <div class="stat-value">{{ coverageStats.totalLines }}</div>
                          <div class="stat-label">总新增行</div>
                        </div>
                      </el-col>
                      <el-col :span="8">
                        <div class="stat-item">
                          <div class="stat-value">{{ coverageStats.coveredLines }}</div>
                          <div class="stat-label">已覆盖行</div>
                        </div>
                      </el-col>
                      <el-col :span="8">
                        <div class="stat-item">
                          <div class="stat-value">{{ coverageStats.ignoredLines }}</div>
                          <div class="stat-label">已忽略行</div>
                        </div>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </div>

              <!-- 覆盖度趋势图 -->
              <div ref="coverageTrendChart" style="width: 100%; height: 400px"></div>
            </div>
          </div>

          <!-- 测试用例情况 -->
          <div v-if="!isAcceptance">
            <p>{{ test_type }}用例情况</p>
            <template v-if="test_type == '冒烟测试'">
              <p v-if="testCaseList.not_test_or_failed_or_blocked_case_list.length > 0">
                {{ version }}版本共有{{ test_type }}用例
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_num }}</span>
                条，已执行通过
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_passed_num }}</span>
                条，用例通过率
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_rate }}%</span>
                ，其中执行失败或未测用例列表如下，请关注相关需求测试进度：
              </p>
              <p v-else>
                {{ version }}版本共有{{ test_type }}用例
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_num }}</span>
                条，已执行通过
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_passed_num }}</span>
                条，用例通过率
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_rate }}%</span>
                ，当前版本{{ test_type }}用例全部通过
              </p>
            </template>
            <template v-else-if="test_type == '一轮测试'">
              <p v-if="testCaseList.not_test_or_failed_or_blocked_case_list.length > 0">
                {{ version }}版本共有测试用例
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_num }}</span>
                条，已执行
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_passed_num }}</span>
                条，用例执行率
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_rate }}%</span>
                ，其中执行失败用例列表如下，请关注相关需求测试进度：
              </p>
              <p v-else>
                {{ version }}版本共有测试用例
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_num }}</span>
                条，已执行
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_passed_num }}</span>
                条，用例执行率
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_rate }}%</span>
                ，当前版本一轮测试无执行失败的用例
              </p>
            </template>
            <template v-else>
              <p v-if="testCaseList.not_test_or_failed_or_blocked_case_list.length > 0">
                {{ version }}版本共有{{ test_type }}用例
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_num }}</span>
                条，已执行通过
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_passed_num }}</span>
                条，用例通过率
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_rate }}%</span>
                ，其中执行失败用例列表如下，请关注相关需求测试进度：
              </p>
              <p v-else>
                {{ version }}版本共有{{ test_type }}用例
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_num }}</span>
                条，已执行通过
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_passed_num }}</span>
                条，用例通过率
                <span style="color: red; text-decoration: underline">{{ testCaseList.test_case_rate }}%</span>
                ，当前版本{{ test_type }}用例全部通过
              </p>
            </template>
            <el-row v-if="testCaseList.not_test_or_failed_or_blocked_case_list.length > 0">
              <el-col :span="22">
                <el-table :data="testCaseList.not_test_or_failed_or_blocked_case_list" border :stripe="true" style="width: 100%" :header-cell-style="{ 'background-color': '#f2f2f2', 'font-weight': 'bold', color: '#272626', heigth: '39px', 'text-align': 'center' }">
                  <el-table-column prop="wiki_name" label="用例关联需求" mix-width="100" align="left"></el-table-column>
                  <el-table-column label="未测/受阻用例名称" mix-width="100" align="left">
                    <template slot-scope="scope">
                      <div v-for="(item, index) in scope.row.cases" :key="index" style="margin-bottom: 8px">
                        <a :href="item.case_url" target="_blank">{{ item.caseName }}</a>
                        <span style="margin-left: 8px; color: red">({{ item.status }})</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="用例执行失败率" align="center" mix-width="100">
                    <template slot-scope="scope">{{ ((scope.row.cases.length / scope.row.cases[0].case_num) * 100).toFixed(2) }}%</template>
                  </el-table-column>
                  <!-- <el-table-column prop="level" label="用例等级" align="center" mix-width="100"></el-table-column> -->
                  <!-- <el-table-column prop="creator" label="执行人" align="center" mix-width="100"></el-table-column> -->
                </el-table>
              </el-col>
            </el-row>
          </div>

          <!--<缺陷情况 -->
          <p>测试bug情况</p>
          <p v-if="newBugStatisDetailList.p1_p2_bug_list.length > 0">
            截至{{ test_type }}阶段共发现
            <span style="color: red; text-decoration: underline">{{ newBugStatisDetailList.bug_num }}</span>
            个bug，已解决
            <span style="color: red; text-decoration: underline">{{ newBugStatisDetailList.solved_bug_num }}</span>
            个bug，当前修复待验
            <span style="color: red; text-decoration: underline">{{ newBugStatisDetailList.repaired_bug_num }}</span>
            个bug,当前遗留
            <span style="color: red; text-decoration: underline">{{ newBugStatisDetailList.unsolved_bug_num }}</span>
            个bug，bug修复率：
            <span style="color: red; text-decoration: underline">{{ newBugStatisDetailList.bug_repair_rate * 100 }}%</span>
            ，其中优先级较高bug列表如下：
          </p>
          <p v-else>
            截至{{ test_type }}阶段共发现
            <span style="color: red; text-decoration: underline">{{ newBugStatisDetailList.bug_num }}</span>
            个bug，已解决
            <span style="color: red; text-decoration: underline">{{ newBugStatisDetailList.solved_bug_num }}</span>
            个bug，当前遗留
            <span style="color: red; text-decoration: underline">{{ newBugStatisDetailList.unsolved_bug_num }}</span>
            个bug，bug修复率：
            <span style="color: red; text-decoration: underline">{{ newBugStatisDetailList.bug_repair_rate * 100 }}</span>
            %，其中没有优先级较高的bug
          </p>
          <el-row v-if="newBugStatisDetailList.p1_p2_bug_list.length > 0">
            <el-col :span="14">
              <el-table :data="newBugStatisDetailList.p1_p2_bug_list" border :stripe="true" style="width: 100%" :header-cell-style="{ 'background-color': '#f2f2f2', 'font-weight': 'bold', color: '#272626', heigth: '39px', 'text-align': 'center' }">
                <el-table-column prop="bug_title" label="bug标题" mix-width="100" align="left">
                  <template slot-scope="scope">
                    <a :href="scope.row.bug_url" target="_blank">{{ scope.row.bug_title }}</a>
                  </template>
                </el-table-column>
                <el-table-column prop="related_feature" label="关联需求" mix-width="100" align="left">
                  <template slot-scope="scope">
                    <template v-if="scope.row.related_feature && scope.row.wiki_url">
                      <a :href="scope.row.wiki_url" target="_blank">{{ scope.row.related_feature }}</a>
                    </template>
                    <template v-else>{{ scope.row.related_feature || '-' }}</template>
                  </template>
                </el-table-column>
                <el-table-column label="bug优先级" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.priority || scope.row.bug_priority }}
                  </template>
                </el-table-column>
                <el-table-column label="问题所属" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.issue_ownership || scope.row.issueOwnership }}
                  </template>
                </el-table-column>
                <el-table-column label="bug所属" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.creator || scope.row.bug_creator }}
                  </template>
                </el-table-column>
                <el-table-column label="当前执行人" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.processor || scope.row.bug_processor }}
                  </template>
                </el-table-column>
                <el-table-column label="解决状态" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.resolution_result || scope.row.bug_status }}
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="10">
              <!-- 按照bug优先级分布 -->
              <div ref="chartTwo" style="height: 300px; width: 400px; margin-left: 5px" :auto-resize="true"></div>
            </el-col>
          </el-row>
          <p v-if="test_type === '冒烟测试' ? newBugStatisDetailList.bug_top5_wiki_list.filter(item => item.bug_num > 5).length > 0 : newBugStatisDetailList.bug_top5_wiki_list.length > 0">
            截止{{ test_type }}阶段结束
            <span style="color: red">bug较多的需求</span>
            ，请关注需求提测质量和风险：
          </p>
          <p v-else>
            截止{{ test_type }}阶段结束无
            <span style="color: red">bug较多的需求</span>
          </p>
          <el-row v-if="test_type === '冒烟测试' ? newBugStatisDetailList.bug_top5_wiki_list.filter(item => item.bug_num > 5).length > 0 : newBugStatisDetailList.bug_top5_wiki_list.length > 0">
            <el-col :span="22">
              <el-table :data="test_type === '冒烟测试' ? newBugStatisDetailList.bug_top5_wiki_list.filter(item => item.bug_num > 5) : newBugStatisDetailList.bug_top5_wiki_list" border :stripe="true" style="width: 100%" :header-cell-style="{ 'background-color': '#f2f2f2', 'font-weight': 'bold', color: '#272626', heigth: '39px', 'text-align': 'center' }">
                <el-table-column prop="wiki_name" label="需求" align="left" mix-width="100">
                  <template slot-scope="scope">
                    <a :href="scope.row.wiki_url" target="_blank">{{ scope.row.wiki_name }}</a>
                  </template>
                </el-table-column>
                <el-table-column prop="bug_num" label="bug总数" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.bug_num }}
                  </template>
                </el-table-column>
                <el-table-column prop="ownership_str" label="bug统计" align="left" mix-width="100">
                  <template slot-scope="scope">
                    <span style="white-space: pre-wrap">{{ scope.row.ownership_str }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>

          <p v-if="test_type == '冒烟测试' || test_type == '一轮测试'">
            按bug
            <span style="color: red">状态分布</span>
            进行分析：
          </p>
          <el-row style="margin-top: 20px" v-if="test_type == '冒烟测试' || test_type == '一轮测试'">
            <el-col :span="14">
              <el-table :data="dubboBugStatisDetailList.statusFormList" border :stripe="true" style="width: 100%" :header-cell-style="{ 'background-color': '#f2f2f2', 'font-weight': 'bold', color: '#272626', heigth: '39px' }" :cell-style="{ 'text-align': 'center' }">
                <el-table-column prop="status" label="状态分布" mix-width="100">
                  <template slot-scope="scope">
                    <span v-if="scope.row.status == '待修复'">
                      <i style="color: rgb(115, 216, 151)" class="el-icon-edit"></i>
                    </span>
                    <span v-else-if="scope.row.status == '进行中'">
                      <i style="color: rgb(255, 117, 117)" class="el-icon-view"></i>
                    </span>
                    <span v-else-if="scope.row.status == '已修复'">
                      <i style="color: rgb(255, 205, 93)" class="el-icon-bell"></i>
                    </span>
                    <span v-else-if="scope.row.status == '已完成'">
                      <i style="color: rgb(93, 207, 255)" class="el-icon-success"></i>
                    </span>
                    <span v-else-if="scope.row.status == '重新打开'">
                      <i style="color: rgb(93, 207, 255)" class="el-icon-printer"></i>
                    </span>
                    <span v-else>
                      <i style="color: rgb(221, 221, 221)" class="el-icon-info"></i>
                    </span>
                    {{ scope.row.status }}
                  </template>
                </el-table-column>
                <el-table-column prop="p1Count" label="P1" min-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.p1Count }}
                  </template>
                </el-table-column>
                <el-table-column prop="p2Count" label="P2" min-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.p2Count }}
                  </template>
                </el-table-column>
                <el-table-column prop="p3Count" label="P3" min-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.p3Count }}
                  </template>
                </el-table-column>
                <el-table-column prop="p4Count" label="P4" min-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.p4Count }}
                  </template>
                </el-table-column>
                <el-table-column prop="p5Count" label="P5" min-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.p5Count }}
                  </template>
                </el-table-column>
                <el-table-column prop="sum" label="合计" min-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.sum }}
                    <template v-if="newBugStatisDetailList.bug_owner_distribution && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership] && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership].total > 0">(未解决:{{ newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership].total }})</template>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="10">
              <!-- 按照bug状态分布 -->
              <div v-if="(test_type && test_type == '冒烟测试') || test_type == '一轮测试'" ref="chartOne" style="height: 300px; width: 400px; margin-left: 5px" :auto-resize="true"></div>
            </el-col>
          </el-row>
          <p v-if="test_type == '冒烟测试' || test_type == '一轮测试'">
            按bug
            <span style="color: red">归属分布</span>
            进行分析：
          </p>
          <el-row style="margin-top: 20px" v-if="test_type == '冒烟测试' || test_type == '一轮测试'">
            <el-col :span="14">
              <!-- <div>&nbsp;&nbsp;</div> -->
              <el-table v-if="projectType == 1 || projectType == 5" :data="dubboBugStatisDetailList.issueOwnershipFormList" border :stripe="true" style="width: 100%" :header-cell-style="{ 'background-color': '#f2f2f2', 'font-weight': 'bold', color: '#272626', heigth: '39px', 'text-align': 'center' }" :cell-style="{ 'text-align': 'center' }">
                <el-table-column prop="issueOwnership" label="问题所属" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.issueOwnership }}
                  </template>
                </el-table-column>
                <el-table-column prop="p1Count" label="P1" min-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.p1Count }}
                    <template v-if="newBugStatisDetailList.bug_owner_distribution && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership] && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership]['P1 紧急-立即处理'] > 0">(未解决:{{ newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership]['P1 紧急-立即处理'] }})</template>
                  </template>
                </el-table-column>
                <el-table-column prop="p2Count" label="P2" min-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.p2Count }}
                    <template v-if="newBugStatisDetailList.bug_owner_distribution && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership] && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership]['P2 关键-插队处理'] > 0">(未解决:{{ newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership]['P2 关键-插队处理'] }})</template>
                  </template>
                </el-table-column>
                <el-table-column prop="p3Count" label="P3" min-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.p3Count }}
                    <template v-if="newBugStatisDetailList.bug_owner_distribution && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership] && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership]['P3 主要-顺序处理'] > 0">(未解决:{{ newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership]['P3 主要-顺序处理'] }})</template>
                  </template>
                </el-table-column>
                <el-table-column prop="p4Count" label="P4" min-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.p4Count }}
                    <template v-if="newBugStatisDetailList.bug_owner_distribution && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership] && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership]['P4 次要-顺序处理'] > 0">(未解决:{{ newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership]['P4 次要-顺序处理'] }})</template>
                  </template>
                </el-table-column>
                <el-table-column prop="p5Count" label="P5" min-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.p5Count }}
                    <template v-if="newBugStatisDetailList.bug_owner_distribution && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership] && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership]['P5 细节-追求极致'] > 0">(未解决:{{ newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership]['P5 细节-追求极致'] }})</template>
                  </template>
                </el-table-column>
                <el-table-column prop="sum" label="合计" min-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.sum }}
                    <template v-if="newBugStatisDetailList.bug_owner_distribution && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership] && newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership].total > 0">(未解决:{{ newBugStatisDetailList.bug_owner_distribution[scope.row.issueOwnership].total }})</template>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="10">
              <!-- 按照问题所属分布 -->
              <div v-if="(test_type && test_type == '冒烟测试') || test_type == '一轮测试'" ref="chartFour" style="height: 300px; width: 400px; margin-left: 5px" :auto-resize="true"></div>
            </el-col>
          </el-row>
          <el-row style="margin-top: 30px">
            <el-col :span="14">
              <el-table :data="newBugStatisDetailList.bug_by_module_distribution" border :stripe="true" style="width: 100%" :header-cell-style="{ 'background-color': '#f2f2f2', 'font-weight': 'bold', color: '#272626', heigth: '39px', 'text-align': 'center' }" :cell-style="{ 'text-align': 'center' }">
                <el-table-column prop="businessGroup" label="模块分布" min-width="100">
                  <template slot-scope="scope">
                    <span v-if="scope.row.businessGroup == 'B'">
                      <i style="color: rgb(115, 216, 151)" class="el-icon-service"></i>
                    </span>
                    <span v-else-if="scope.row.businessGroup == '商业'">
                      <i style="color: rgb(255, 117, 117)" class="el-icon-rank"></i>
                    </span>
                    <span v-else-if="scope.row.businessGroup == 'C'">
                      <i style="color: rgb(255, 205, 93)" class="el-icon-tickets"></i>
                    </span>
                    <span v-else-if="scope.row.businessGroup == '其他项目'">
                      <i style="color: rgb(93, 207, 255)" class="el-icon-star-on"></i>
                    </span>
                    <span v-else-if="scope.row.businessGroup == '安全'">
                      <i style="color: rgb(93, 207, 255)" class="el-icon-goods"></i>
                    </span>
                    <span v-else-if="scope.row.businessGroup == '其它'">
                      <i style="color: rgb(93, 207, 255)" class="el-icon-question"></i>
                    </span>
                    <span v-else>
                      <i style="color: rgb(221, 221, 221)" class="el-icon-question"></i>
                    </span>
                    {{ scope.row.businessGroup }}
                  </template>
                </el-table-column>
                <el-table-column prop="p1Count" label="P1" min-width="100"></el-table-column>
                <el-table-column prop="p2Count" label="P2" min-width="100"></el-table-column>
                <el-table-column prop="p3Count" label="P3" min-width="100"></el-table-column>
                <el-table-column prop="p4Count" label="P4" min-width="100"></el-table-column>
                <el-table-column prop="p5Count" label="P5" min-width="100"></el-table-column>
                <el-table-column prop="sum" label="合计" min-width="100"></el-table-column>
              </el-table>
            </el-col>
            <el-col :span="10">
              <!-- 按照模块分布 -->
              <div v-if="test_type" ref="chartThree" style="height: 300px; width: 500px; margin-left: 5px" :auto-resize="true"></div>
            </el-col>
          </el-row>
          <!-- Bug类型分布图 -->
          <el-row style="margin-top: 20px">
            <el-col :span="12">
              <p>
                Bug
                <span style="color: red">类型分布</span>
                如下：
              </p>
              <div ref="bugTypeChart" style="height: 300px; width: 400px; margin-left: 5px" :auto-resize="true"></div>
            </el-col>
            <el-col :span="12">
              <p v-if="test_type && test_type === '一轮测试'">
                Bug
                <span style="color: red">解决时间分布</span>
                如下：
              </p>
              <div ref="bugResolveTimeDistributionChart" style="height: 300px; width: 100%; margin-left: 5px" :auto-resize="true" v-if="test_type && test_type === '一轮测试'"></div>
            </el-col>
          </el-row>
          <!-- Bug创建时间分布图，只在一轮测试 -->
          <p v-if="test_type === '一轮测试'">
            Bug
            <span style="color: red">创建时间分布</span>
            趋势如下：
          </p>
          <el-row style="margin-top: 20px" v-if="test_type === '一轮测试'">
            <el-col :span="16">
              <div ref="bugCreateTimeChart" style="width: 100%; height: 400px"></div>
            </el-col>
          </el-row>

          <p v-if="test_type && (test_type == '冒烟测试' || test_type == '一轮测试')">
            细分各端bug，按
            <span style="color: red">原因分类</span>
            如下 ：
          </p>
          <!-- 区分IOS/server/android里面，分端讨论bug分类 -->
          <el-row style="margin-top: 20px; margin-right: 20px" v-if="test_type && (test_type == '冒烟测试' || test_type == '一轮测试')">
            <el-col :span="12">
              <!-- Android bug类型分布 -->
              <div ref="androidChart" style="height: 300px; width: 100%" :auto-resize="true"></div>
            </el-col>
            <el-col :span="12">
              <!-- iOS bug类型分布 -->
              <div ref="iosChart" style="height: 300px; width: 100%" :auto-resize="true"></div>
            </el-col>
          </el-row>
          <el-row style="margin-right: 20px" v-if="test_type && (test_type == '冒烟测试' || test_type == '一轮测试')">
            <el-col :span="12">
              <!-- PC&H5 bug类型分布 -->
              <div ref="pcChart" style="height: 300px; width: 100%" :auto-resize="true"></div>
            </el-col>
            <el-col :span="12">
              <!-- Server bug类型分布 -->
              <div ref="serverChart" style="height: 300px; width: 100%" :auto-resize="true"></div>
            </el-col>
          </el-row>

          <p v-if="test_type && test_type == '服务端验收'">
            细分Server端bug，按
            <span style="color: red">原因分类</span>
            如下 ：
          </p>
          <el-row style="margin-right: 20px" v-if="test_type && test_type == '服务端验收'">
            <el-col :span="16">
              <!-- Server bug类型分布 -->
              <div ref="serverChart" style="height: 300px; width: 100%" :auto-resize="true"></div>
            </el-col>
          </el-row>

          <p v-if="test_type && test_type == 'Web验收'">
            细分Web端bug，按
            <span style="color: red">原因分类</span>
            如下 ：
          </p>
          <el-row style="margin-right: 20px" v-if="test_type && test_type == 'Web验收'">
            <el-col :span="16">
              <!-- Web bug类型分布 -->
              <div ref="pcChart" style="height: 300px; width: 100%" :auto-resize="true"></div>
            </el-col>
          </el-row>

          <p v-if="test_type && test_type == '客户端验收-IOS'">
            细分IOS端bug，按
            <span style="color: red">原因分类</span>
            如下 ：
          </p>
          <el-row style="margin-right: 20px" v-if="test_type && test_type == '客户端验收-IOS'">
            <el-col :span="16">
              <!-- IOS bug类型分布 -->
              <div ref="iosChart" style="height: 300px; width: 100%" :auto-resize="true"></div>
            </el-col>
          </el-row>

          <p v-if="test_type && test_type == '客户端验收-Android'">
            细分Android端bug，按
            <span style="color: red">原因分类</span>
            如下 ：
          </p>
          <el-row style="margin-right: 20px" v-if="test_type && test_type == '客户端验收-Android'">
            <el-col :span="16">
              <!-- Android bug类型分布 -->
              <div ref="androidChart" style="height: 300px; width: 100%" :auto-resize="true"></div>
            </el-col>
          </el-row>

          <!-- 历史版本冒烟阶段bug指数趋势变化图，只在冒烟测试展示 -->
          <p v-if="test_type == '冒烟测试'">
            历史版本冒烟阶段
            <span style="color: red">bug指数</span>
            （bug指数=版本总bug数/版本总需求数）趋势变化如下，供参考版本冒烟提测质量：
          </p>
          <el-row style="margin-top: 20px" v-if="test_type == '冒烟测试'">
            <el-col :span="22">
              <div ref="chartSix" style="width: 100%; height: 400px"></div>
            </el-col>
          </el-row>

          <p v-if="test_type && test_type !== '冒烟测试' && test_type !== '一轮测试'">bug修复时长分布图</p>
          <el-row style="margin-top: 20px" v-if="test_type && test_type !== '冒烟测试' && test_type !== '一轮测试'">
            <el-col :span="22">
              <div ref="bugRepairDurationChart" style="width: 100%; height: 400px"></div>
            </el-col>
          </el-row>

          <p v-if="newBugStatisDetailList.warn_bug_list_2days.length > 0 && test_type === '一轮测试'">停留超过2天列表：</p>
          <p v-else-if="newBugStatisDetailList.warn_bug_list_2days.length === 0 && test_type === '一轮测试'">无停留超过2天的bug</p>
          <el-row style="margin-top: 20px" v-if="newBugStatisDetailList.warn_bug_list_2days.length > 0 && test_type == '一轮测试'">
            <el-col :span="22">
              <el-table :data="newBugStatisDetailList.warn_bug_list_2days" style="width: 100%" :max-height="tableMaxHeight">
                <el-table-column prop="bug_title" label="bug标题" mix-width="100" align="left">
                  <template slot-scope="scope">
                    <a :href="scope.row.bug_url" target="_blank">{{ scope.row.bug_title }}</a>
                  </template>
                </el-table-column>
                <el-table-column prop="related_feature" label="关联需求" mix-width="100" align="left">
                  <template slot-scope="scope">
                    <template v-if="scope.row.related_feature && scope.row.wiki_url">
                      <a :href="scope.row.wiki_url" target="_blank">{{ scope.row.related_feature }}</a>
                    </template>
                    <template v-else>{{ scope.row.related_feature || '-' }}</template>
                  </template>
                </el-table-column>
                <el-table-column label="bug优先级" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.priority || scope.row.bug_priority }}
                  </template>
                </el-table-column>
                <el-table-column label="问题所属" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.issue_ownership || scope.row.issueOwnership }}
                  </template>
                </el-table-column>
                <el-table-column label="bug所属" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.creator || scope.row.bug_creator }}
                  </template>
                </el-table-column>
                <el-table-column label="当前执行人" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.processor || scope.row.bug_processor }}
                  </template>
                </el-table-column>
                <el-table-column label="解决状态" align="center" mix-width="100">
                  <template slot-scope="scope">
                    {{ scope.row.resolution_result || scope.row.bug_status }}
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>

          <!-- 添加 Muses 分析结果展示区域 -->
          <div style="margin-top: 30px">
            <h3 class="report-title">
              AI 分析报告
              <el-button type="primary" size="small" style="margin-left: 10px" :disabled="!reportData" @click="handleAnalysisClick">开始分析</el-button>
            </h3>
            <el-card class="muses-analysis-card">
              <div class="muses-content" v-html="musesAnalysisResult || '点击「开始分析」按钮获取AI分析报告'"></div>
            </el-card>
          </div>
        </div>
      </div>
    </div>

    <!-- </el-drawer> -->
    <button @click="testButton('直聘 V13.11', '服务端验收')">获取测试报告bug数据</button>
  </div>
</template>

<script>
import { getEmailInfo } from '@/api/zpTools/hunterTool'
import { getBugStatic, getTestReportApi, coverageStatic, getBugStaticPost, getWikiTypeStatic, getWikiTestStatic, getTestCaseStatic, getCoverageStaticPost } from '@/api/zpTools/hunterTool'
import { updatePlan } from '@/api/zpTesthub/plan'
import { dubboNewTestReportBugReport } from '@/api/zpTools/authenticationTools'
import { addExecuteLog, executeLogStatistics } from '@/api/zpTesthub/executeLog'
import { dubboBugStatisVersionListFromMagpieData, dubboBugStatisDetailListFromMagpieData } from '@/api/zpTools/authenticationTools'
import { version } from 'jszip'
export default {
  data() {
    return {
      acceptence_wiki_list: [], // 添加验收需求列表数据
      coverageTrendData: null,
      coverageTrendChart: null,
      coverageTrendChartOption: {
        title: {
          text: '覆盖度变化日趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['总新增行', '已覆盖行', '覆盖度'],
          bottom: '5%',
          left: 'center',
          orient: 'horizontal',
          itemGap: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: [],
          axisLabel: {
            rotate: 0,
            formatter: function (value) {
              return value.substring(5) // 只显示月-日
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '行数',
            position: 'left',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#5dcfff'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#E9E9E9'
              }
            },
            axisLabel: {
              formatter: function (value) {
                return value >= 1000 ? (value / 1000).toFixed(1) + 'k' : value
              }
            }
          },
          {
            type: 'value',
            name: '覆盖度(%)',
            position: 'right',
            min: 0,
            max: 100,
            interval: 20,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#ff525f'
              }
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '总新增行',
            type: 'bar',
            data: [],
            itemStyle: {
              color: '#5dcfff',
              opacity: 0.8
            },
            barWidth: '30%',
            z: 9
          },
          {
            name: '已覆盖行',
            type: 'bar',
            data: [],
            itemStyle: {
              color: '#73d897',
              opacity: 0.8
            },
            barWidth: '30%',
            z: 10
          },
          {
            name: '覆盖度',
            type: 'line',
            yAxisIndex: 1,
            data: [],
            itemStyle: {
              color: '#ff525f'
            },
            lineStyle: {
              width: 2,
              type: 'solid'
            },
            symbol: 'circle',
            symbolSize: 6,
            z: 11,
            label: {
              show: true,
              formatter: '{c}%',
              position: 'top'
            }
          }
        ]
      },
      casePlanForm: {},
      projectType: '1',
      version: '',
      loading: false,
      bossVersion: [],
      versionList: [],
      test_type: '',
      newBugStatisDetailList: {
        bug_classification_distribution: [],
        bug_index: 0,
        bug_list: [],
        bug_num: 0,
        bug_repair_rate: 0,
        bug_repair_num: 0,
        p1_p2_bug_num: 0,
        repaired_bug_num: 0,
        unresolve_p1_p2_bug_num: 0,
        p1_p2_bug_list: [],
        solved_bug_num: 0,
        unsolved_bug_num: 0,
        warn_bug_list_2days: [],
        warn_bug_list_3days: [],
        history_smoke_bug_static: [],
        bug_owner_distribution: {},
        bug_top5_wiki_list: []
      },
      testCaseList: {
        test_case_num: 0,
        test_case_passed_num: 0,
        test_case_rate: 0,
        not_test_case_list: [],
        not_test_or_failed_or_blocked_case_list: []
      },
      newTestProgressList: {
        has_remark_wiki: 0,
        non_version_demand_num: 0,
        technical_demand_num: 0,
        has_remark_wiki_list: [],
        nagetive_and_not_fill_remark_wiki_list: []
      },
      newWikiTypeResult: [
        {
          name: '版本需求',
          value: 0
        },
        {
          name: '非版本需求',
          value: 0
        },
        {
          name: '技术需求',
          value: 0
        }
      ],
      test_result: '',
      coverageStaticResult: [],
      selectedItem: {},
      dubboBugStatisDetailList: {},
      testStageList: [
        {
          recordName: '冒烟测试',
          recordId: '1'
        },
        {
          recordName: '一轮测试',
          recordId: '2'
        },
        {
          recordName: '服务端验收',
          recordId: '3'
        },
        {
          recordName: 'Web验收',
          recordId: '4'
        },
        {
          recordName: '客户端验收-Android',
          recordId: '5'
        },
        {
          recordName: '客户端验收-IOS',
          recordId: '6'
        }
      ],
      testStage: '',
      chartOne: null,
      chartTwo: null,
      chartThree: null,
      chartFour: null,
      chartFive: null,
      chartSix: null,
      androidChart: null,
      iosChart: null,
      pcChart: null,
      serverChart: null,
      editorContent: '',

      //bug状态分布
      bugStatusDistributionOption: {
        color: ['rgb(255, 117, 117)', 'rgb(93, 207, 255)', 'rgb(255, 205, 93)', 'rgb(115, 216, 151)', '#FF525F', '#3391FF', '#FF834D', '#10C4C4', '#75CC49', '#8A5CFF', '#75E3E2', '#FFA98A', '#B394FF'],
        title: {
          text: '按状态分布',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'right',
          data: ['待修复', '进行中', '已修复', '已完成']
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: '50%',
            center: ['50%', '45%'],
            data: [
              { value: 10, name: '待修复' },
              { value: 20, name: '进行中' },
              { value: 30, name: '已修复' },
              { value: 40, name: '已完成' }
            ],
            label: {
              show: true,
              position: 'outside',
              formatter: params => {
                if (params.value === 0) {
                  return ''
                }
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      },

      //bug优先级分布
      bugPriorityDistributionOption: {
        color: ['#FF525F', '#3391FF', '#FF834D', '#10C4C4', '#75CC49'],
        title: {
          text: 'Bug优先级分布',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}个 ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'right',
          data: ['P1', 'P2', 'P3', 'P4', 'P5']
        },
        series: [
          {
            name: 'Bug优先级',
            type: 'pie',
            radius: '50%',
            center: ['50%', '45%'],
            data: [],
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}: {c}个'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      },

      //bug模块分布
      bugModuleDistributionOption: {
        color: ['rgb(255, 117, 117)', 'rgb(93, 207, 255)', 'rgb(255, 205, 93)', 'rgb(115, 216, 151)', '#FF525F', '#3391FF', '#FF834D', '#10C4C4', '#75CC49', '#8A5CFF', '#75E3E2', '#FFA98A', '#B394FF'],
        title: {
          text: '按模块分布',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'right',
          data: []
        },
        series: [
          {
            name: 'Bug模块分布',
            type: 'pie',
            radius: '50%',
            center: ['50%', '45%'],
            data: [],
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}: {c}个'
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 25
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      },

      //bug模块分布
      bugProblemBelongOption: {
        color: ['rgb(255, 117, 117)', 'rgb(93, 207, 255)', 'rgb(255, 205, 93)', 'rgb(115, 216, 151)', '#FF525F', '#3391FF', '#FF834D', '#10C4C4', '#75CC49', '#8A5CFF', '#75E3E2', '#FFA98A', '#B394FF'],
        title: {
          text: '按问题所属分布',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'right',
          data: ['Server', 'Android', 'iOS', 'PC&H5', '小程序', '产品', '设计', '其他', '后台', '用户反馈', '未关联模块']
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: '50%',
            center: ['50%', '45%'],
            data: [
              { value: 2, name: 'Server' },
              { value: 2, name: 'Android' },
              { value: 2, name: 'iOS' },
              { value: 2, name: 'PC&H5' },
              { value: 2, name: '小程序' },
              { value: 2, name: '产品' },
              { value: 2, name: '设计' },
              { value: 2, name: '其他' },
              { value: 2, name: '后台' },
              { value: 2, name: '用户反馈' },
              { value: 2, name: '未关联模块' }
            ],
            label: {
              show: true,
              position: 'outside',
              formatter: params => {
                if (params.value === 0) {
                  return ''
                }
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      },

      // Android bug类型分布配置
      androidBugTypeOption: {
        color: ['rgb(255, 117, 117)', 'rgb(93, 207, 255)', 'rgb(255, 205, 93)', 'rgb(115, 216, 151)'],
        title: {
          text: 'Android Bug类型分布',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          top: 'middle',
          data: []
        },
        series: [
          {
            name: 'Bug类型',
            type: 'pie',
            radius: ['0%', '50%'],
            center: ['40%', '50%'],
            data: [],
            label: {
              show: true,
              formatter: '{b}: {c}个',
              position: 'outside'
            },
            labelLine: {
              show: true,
              length: 20,
              length2: 30
            }
          }
        ]
      },

      // iOS bug类型分布配置
      iosBugTypeOption: {
        color: ['rgb(255, 117, 117)', 'rgb(93, 207, 255)', 'rgb(255, 205, 93)', 'rgb(115, 216, 151)'],
        title: {
          text: 'iOS Bug类型分布',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          top: 'middle',
          data: []
        },
        series: [
          {
            name: 'Bug类型',
            type: 'pie',
            radius: ['0%', '50%'],
            center: ['40%', '50%'],
            data: [],
            label: {
              show: true,
              formatter: '{b}: {c}个',
              position: 'outside'
            },
            labelLine: {
              show: true,
              length: 20,
              length2: 30
            }
          }
        ]
      },

      // PC&H5 bug类型分布配置
      pcBugTypeOption: {
        color: ['rgb(255, 117, 117)', 'rgb(93, 207, 255)', 'rgb(255, 205, 93)', 'rgb(115, 216, 151)'],
        title: {
          text: 'PC&H5 Bug类型分布',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          top: 'middle',
          data: []
        },
        series: [
          {
            name: 'Bug类型',
            type: 'pie',
            radius: ['0%', '50%'],
            center: ['40%', '50%'],
            data: [],
            label: {
              show: true,
              formatter: '{b}: {c}个',
              position: 'outside'
            },
            labelLine: {
              show: true,
              length: 20,
              length2: 30
            }
          }
        ]
      },

      // Server bug类型分布配置
      serverBugTypeOption: {
        color: ['rgb(255, 117, 117)', 'rgb(93, 207, 255)', 'rgb(255, 205, 93)', 'rgb(115, 216, 151)'],
        title: {
          text: 'Server Bug类型分布',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          top: 'middle',
          data: []
        },
        series: [
          {
            name: 'Bug类型',
            type: 'pie',
            radius: ['0%', '50%'],
            center: ['40%', '50%'],
            data: [],
            label: {
              show: true,
              formatter: '{b}: {c}个',
              position: 'outside'
            },
            labelLine: {
              show: true,
              length: 20,
              length2: 30
            }
          }
        ]
      },

      // 需求类型分布配置
      wikiTypeDistributionOption: {
        color: ['#FF525F', '#3391FF', '#FF834D'],
        title: {
          text: '需求类型分布',
          subtext: '',
          left: 'center',
          top: 10
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}个 ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: '2%',
          top: 'middle',
          data: ['版本需求', '非版本需求', '技术需求']
        },
        series: [
          {
            name: '需求类型',
            type: 'pie',
            radius: '50%',
            center: ['50%', '55%'],
            data: [],
            label: {
              show: true,
              formatter: function (params) {
                return `${params.name}: ${params.value}个`
              },
              position: 'outside',
              distanceToLabelLine: 5
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 25,
              maxSurfaceAngle: 80,
              smooth: true
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      },

      // 历史冒烟阶段bug/需求比折线图配置
      historySmokeBugOption: {
        color: ['#3391FF'],
        title: {
          text: '历史版本冒烟阶段bug/需求比',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            const data = params[0].data
            return `${params[0].name}<br/>
                    Bug数量: ${data.bug_num}<br/>
                    需求数量: ${data.wiki_num}<br/>
                    Bug指数: ${data.bug_index}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [],
          axisLabel: {
            interval: 0,
            width: 100,
            overflow: 'break'
          }
        },
        yAxis: [
          {
            type: 'value',
            name: 'Bug指数',
            position: 'left',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#3391FF'
              }
            },
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: 'Bug指数',
            type: 'line',
            smooth: true,
            data: [],
            label: {
              show: true,
              position: 'top',
              formatter: function (params) {
                return `Bug数:${params.data.bug_num}\n需求数:${params.data.wiki_num}`
              },
              backgroundColor: 'rgba(255,255,255,0.8)',
              padding: [4, 8],
              borderRadius: 4
            }
          }
        ]
      },

      // 覆盖度分布图表配置
      coverageDistributionOption: {
        title: {
          text: '覆盖度分布',
          left: 'center',
          top: 10
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}个 ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: 0,
          left: 'center',
          itemGap: 30
        },
        series: [
          {
            name: '覆盖度分布',
            type: 'pie',
            radius: ['0%', '60%'],
            center: ['50%', '55%'],
            avoidLabelOverlap: true,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}: {c}个',
              alignTo: 'labelLine',
              minMargin: 5,
              edgeDistance: 10,
              lineHeight: 15,
              distanceToLabelLine: 5
            },
            labelLine: {
              length: 20,
              length2: 30,
              maxSurfaceAngle: 60,
              minTurnAngle: 60
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            data: []
          }
        ]
      },
      coverageChart: null,
      // 覆盖度需求占比配置
      coverageRatioOption: {
        color: ['#3391FF', '#FF834D'],
        title: {
          text: '覆盖度需求占比',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'right',
          data: ['已统计覆盖度', '未统计']
        },
        series: [
          {
            name: '覆盖度统计',
            type: 'pie',
            radius: '50%',
            center: ['50%', '45%'],
            data: [],
            label: {
              show: true,
              formatter: '{b}: {c}个',
              position: 'outside'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      },

      // Bug创建时间分布图配置
      bugCreateTimeOption: {
        color: ['#3391FF'],
        title: {
          text: 'Bug创建时间分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}个bug'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            interval: 0,
            rotate: 0, // 设置为0度，即水平显示
            formatter: function (value) {
              return value
            }
          }
        },
        yAxis: {
          type: 'value',
          name: 'Bug数量',
          minInterval: 1
        },
        series: [
          {
            name: 'Bug数量',
            type: 'bar', // 改为柱状图
            barWidth: '60%', // 设置柱子宽度
            data: [],
            label: {
              show: true,
              position: 'top',
              formatter: '{c}'
            },
            itemStyle: {
              borderRadius: [4, 4, 0, 0] // 设置柱子顶部圆角
            }
          }
        ]
      },
      bugCreateTimeChart: null,

      musesAnalysisResult: '', // 存储 muses 分析结果
      reportData: null, // 存储报告数据用于分析
      p1p2BugCondition: true,
      testCaseRateCondition: true,
      bugIndexCondition: true,
      hasUnresolvedServerBug: true,
      hasUnresolvedWebBug: true,
      hasUnresolvedIosBug: true,
      hasUnresolvedAndroidBug: true,

      // 添加覆盖度统计数据
      coverageStats: {
        totalCoverage: 0,
        demandCount: 0,
        projectCount: 0,
        developerCount: 0,
        testerCount: 0,
        totalLines: 0,
        coveredLines: 0,
        ignoredLines: 0,
        updateTime: ''
      },

      // 添加覆盖度趋势数据
      coverageTrend: {
        dates: [],
        covered: [],
        total: [],
        coverage: []
      },

      // 添加覆盖度趋势图配置
      coverageTrendOption: {
        title: {
          text: '覆盖度变化日趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['已覆盖行', '总新增行', '覆盖度'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: []
        },
        yAxis: [
          {
            type: 'value',
            name: '行数',
            position: 'left'
          },
          {
            type: 'value',
            name: '覆盖率(%)',
            position: 'right',
            min: 0,
            max: 100
          }
        ],
        series: [
          {
            name: '已覆盖行',
            type: 'bar',
            data: [],
            itemStyle: {
              color: '#73d897'
            }
          },
          {
            name: '总行数',
            type: 'bar',
            data: [],
            itemStyle: {
              color: '#5dcfff'
            }
          },
          {
            name: '覆盖率',
            type: 'line',
            yAxisIndex: 1,
            data: [],
            itemStyle: {
              color: '#ff525f'
            }
          }
        ]
      },

      // 覆盖度趋势图表
      coverageTrendChart: null,

      // 覆盖度趋势数据
      coverageTrendData: null,

      tableMaxHeight: 400,

      // Bug类型分布图表配置
      bugTypeDistributionOption: {
        title: {
          text: 'Bug类型分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          top: 'middle'
        },
        series: [
          {
            name: 'Bug类型',
            type: 'pie',
            radius: ['0%', '50%'], // 调整饼图大小
            center: ['40%', '50%'], // 调整饼图位置
            label: {
              show: true,
              formatter: '{b}: {c}个',
              position: 'outside' // 标签位置调整为外部
            },
            labelLine: {
              show: true,
              length: 20,
              length2: 30
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            data: []
          }
        ]
      },
      bugTypeChart: null,

      // Bug解决时间分布折线图配置
      bugResolveTimeDistributionOption: {
        color: ['#3391FF'],
        title: {
          text: 'Bug解决时间分布',
          left: 'center',
          top: 10
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}个'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['0-1天', '1-2天', '2-3天', '3-4天', '4-7天', '7天以上'],
          axisLabel: {
            interval: 0,
            rotate: 0 // 将rotate改为0，使标签水平显示
          }
        },
        yAxis: {
          type: 'value',
          name: 'Bug数量',
          minInterval: 1,
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: 'Bug数量',
            type: 'line',
            smooth: true,
            data: [],
            label: {
              show: true,
              position: 'top',
              formatter: '{c}个'
            },
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              borderWidth: 2
            },
            areaStyle: {
              opacity: 0.1
            }
          }
        ]
      },
      bugResolveTimeDistributionChart: null,
      bugRepairDurationChart: null,
      bugRepairDurationOption: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function (params) {
            let result = params[0].axisValue + '<br/>'
            params.forEach(param => {
              let value = param.value
              result += param.marker + param.seriesName + ': ' + value + ' 个<br/>'
            })
            return result
          }
        },
        legend: {
          data: ['Bug修复时长', 'Bug验收时长', '总修复时长'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '60px',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: 'Bug数量',
            min: 0,
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: 'Bug修复时长',
            type: 'bar',
            barWidth: '20%',
            data: [],
            itemStyle: {
              color: '#5470c6'
            },
            label: {
              show: true,
              position: 'top',
              formatter: ''
            }
          },
          {
            name: 'Bug验收时长',
            type: 'bar',
            barWidth: '20%',
            data: [],
            itemStyle: {
              color: '#91cc75'
            },
            label: {
              show: true,
              position: 'top',
              formatter: ''
            }
          },
          {
            name: '总修复时长',
            type: 'line',
            data: [],
            itemStyle: {
              color: '#ee6666'
            },
            symbolSize: 8,
            symbol: 'circle',
            smooth: true,
            lineStyle: {
              width: 3
            },
            label: {
              show: true,
              position: 'top',
              formatter: ''
            }
          }
        ]
      }
    }
  },

  computed: {
    isAcceptance() {
      return this.selectedItem && (this.test_type === '服务端验收' || this.test_type === 'Web验收' || this.test_type === '客户端验收-Android' || this.test_type === '客户端验收-IOS')
    }
  },

  methods: {
    // 添加清理图表的方法
    clearCharts() {
      // 销毁所有图表实例
      if (this.bugResolveTimeDistributionChart) {
        this.bugResolveTimeDistributionChart.dispose()
        this.bugResolveTimeDistributionChart = null
      }
      if (this.chartOne) {
        this.chartOne.dispose()
        this.chartOne = null
      }
      if (this.chartTwo) {
        this.chartTwo.dispose()
        this.chartTwo = null
      }
      if (this.chartThree) {
        this.chartThree.dispose()
        this.chartThree = null
      }
      if (this.chartFour) {
        this.chartFour.dispose()
        this.chartFour = null
      }
      if (this.chartFive) {
        this.chartFive.dispose()
        this.chartFive = null
      }
      if (this.chartSix) {
        this.chartSix.dispose()
        this.chartSix = null
      }
      if (this.bugTypeChart) {
        this.bugTypeChart.dispose()
        this.bugTypeChart = null
      }
      if (this.bugCreateTimeChart) {
        this.bugCreateTimeChart.dispose()
        this.bugCreateTimeChart = null
      }
      if (this.androidChart) {
        this.androidChart.dispose()
        this.androidChart = null
      }
      if (this.iosChart) {
        this.iosChart.dispose()
        this.iosChart = null
      }
      if (this.pcChart) {
        this.pcChart.dispose()
        this.pcChart = null
      }
      if (this.serverChart) {
        this.serverChart.dispose()
        this.serverChart = null
      }
    },

    // 更新Bug解决时间分布折线图
    async updateBugResolveTimeDistributionChart() {
      if (!this.newBugStatisDetailList || !this.newBugStatisDetailList.bug_resolve_time_distribution) {
        console.warn('Bug解决时间分布数据未准备好')
        return
      }

      try {
        // 确保在更新前清理旧的图表实例
        if (this.bugResolveTimeDistributionChart) {
          this.bugResolveTimeDistributionChart.dispose()
          this.bugResolveTimeDistributionChart = null
        }

        await this.$nextTick()

        // 重新初始化图表
        if (this.$refs.bugResolveTimeDistributionChart) {
          this.bugResolveTimeDistributionChart = this.$echarts.init(this.$refs.bugResolveTimeDistributionChart)
        }

        const timeData = this.newBugStatisDetailList.bug_resolve_time_distribution
        const timeMapping = {
          '0-1d': '0-1天',
          '1-2d': '1-2天',
          '2-3d': '2-3天',
          '3-4d': '3-4天',
          '4-7d': '4-7天',
          '7d+': '7天以上'
        }

        // 按照固定顺序获取数据
        const xAxisData = Object.keys(timeMapping).map(key => timeMapping[key])
        const seriesData = Object.keys(timeMapping).map(key => timeData[key] || 0)

        const option = {
          title: {
            text: 'Bug解决时间分布',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'normal'
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'line',
              lineStyle: {
                color: '#5470c6',
                type: 'dashed'
              }
            },
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderColor: '#5470c6',
            borderWidth: 1,
            textStyle: {
              color: '#333'
            },
            formatter: '{b}<br/>{a}: {c}个'
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '8%',
            top: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              interval: 0,
              rotate: 30,
              color: '#666'
            },
            axisLine: {
              lineStyle: {
                color: '#ddd'
              }
            },
            axisTick: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            name: 'Bug数量',
            nameTextStyle: {
              color: '#666',
              padding: [0, 0, 0, 30]
            },
            axisLabel: {
              color: '#666'
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: '#eee',
                type: 'dashed'
              }
            }
          },
          series: [
            {
              name: 'Bug数量',
              type: 'line',
              data: seriesData,
              smooth: true,
              symbol: 'emptyCircle',
              symbolSize: 8,
              label: {
                show: true,
                position: 'top',
                formatter: '{c}',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                borderColor: '#5470c6',
                borderWidth: 1,
                borderRadius: 4,
                padding: [2, 4],
                distance: 8
              },
              lineStyle: {
                width: 3,
                shadowColor: 'rgba(84, 112, 198, 0.3)',
                shadowBlur: 10
              },
              itemStyle: {
                color: '#5470c6',
                borderWidth: 2
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(84, 112, 198, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(84, 112, 198, 0.02)'
                    }
                  ]
                }
              },
              emphasis: {
                scale: true,
                focus: 'series'
              }
            }
          ]
        }

        if (this.bugResolveTimeDistributionChart) {
          this.bugResolveTimeDistributionChart.setOption(option)
        }
      } catch (error) {
        console.error('更新Bug解决时间分布图表失败:', error)
      }
    },

    calculateTableHeight() {
      // 获取视窗高度
      const windowHeight = window.innerHeight
      // 预留其他元素的空间（头部、间距等）
      const reservedSpace = 300 // 可以根据实际情况调整
      // 设置表格最大高度
      this.tableMaxHeight = windowHeight - reservedSpace
    },
    // 更新覆盖度趋势图表
    updateCoverageTrendChart() {
      if (!this.$refs.coverageTrendChart) {
        console.warn('覆盖度趋势图表容器不存在')
        return
      }

      try {
        if (!this.coverageTrendChart) {
          this.coverageTrendChart = this.$echarts.init(this.$refs.coverageTrendChart)
        }

        // 更新图表数据
        if (this.coverageTrendData) {
          this.coverageTrendChartOption.xAxis.data = this.coverageTrendData.dates || []
          this.coverageTrendChartOption.series[0].data = this.coverageTrendData.covered || []
          this.coverageTrendChartOption.series[1].data = this.coverageTrendData.total || []
          this.coverageTrendChartOption.series[2].data = this.coverageTrendData.coverage || []
        }

        this.coverageTrendChart.setOption(this.coverageTrendChartOption)
      } catch (error) {
        console.error('更新覆盖度趋势图表失败:', error)
      }
    },

    getTestCaseStaticInfo(version, test_type) {
      return getTestCaseStatic(version, test_type)
        .then(resp => {
          console.log('打印getTestCaseStatic中的resp的值')
          console.log(resp)
          this.testCaseList = resp.data
        })
        .catch(error => {
          console.error('获取测试用例数据失败:', error)
        })
    },
    getBugStaticInfo(version, test_type) {
      // this.getTestCaseStaticInfo(version, test_type)
      // this.getWikiStaticInfo(version, test_type)
      // this.getWikiTypeStaticInfo(version, test_type)
      getBugStaticPost(version, test_type)
        .then(resp => {
          this.newBugStatisDetailList = resp.data
          console.log('Bug列表数据结构:', resp.data.p1_p2_bug_list)
          this.$nextTick(() => {
            this.updateAllCharts()
          })
        })
        .catch(error => {
          console.error('获取bug静态数据失败:', error)
        })
    },
    getWikiStaticInfo(version, test_type) {
      getWikiTestStatic(version, test_type)
        .then(resp => {
          if (resp.code === 0) {
            const data = resp.data
            // 处理验收需求列表数据
            this.acceptence_wiki_list = data.acceptence_wiki_list || []
            // 处理验收需求列表数据
            this.acceptence_wiki_list = data.acceptence_wiki_list || []

            // 处理覆盖度分布数据
            this.coverageStaticResult = resp.data.pass_line_static || []
            this.$nextTick(() => {
              // 使用完整的数据更新饼图
              this.updateCoverageDistributionChart(resp.data)
            })
          }
        })
        .catch(error => {
          console.error('获取测试进度数据失败:', error)
        })
    },

    getWikiTypeStaticInfo(version, test_type) {
      return getWikiTypeStatic(version, test_type)
        .then(resp => {
          console.log('打印getWikiTestStatic中的resp的值')
          console.log(resp)
          this.newWikiTypeResult = resp.data
        })
        .catch(error => {
          console.error('获取需求类型数据失败:', error)
        })
    },
    getCoverageStaticInfo(version, test_type) {
      coverageStatic(version, test_type)
        .then(resp => {
          console.log('打印coverageStatic中的resp的值')
          console.log(resp)
          // 确保表格数据是数组
          this.coverageStaticResult = resp.data.pass_line_static || []
          this.$nextTick(() => {
            // 使用完整的数据更新饼图
            this.updateCoverageDistributionChart(resp.data)
          })
        })
        .catch(error => {
          console.error('获取覆盖率数据失败:', error)
        })
    },

    async getReportAnalysis(reportData) {
      try {
        this.musesAnalysisResult = '' // 清空之前的结果
        let infodata = await getEmailInfo()
        let email = infodata.userinfo.zpData.email
        if (this.test_type === '冒烟测试') {
          this.apiKey = await getTestReportApi()
          this.apiKey = this.apiKey.data.muses_api.api_smoke_test
        } else if (this.test_type === '一轮测试') {
          this.apiKey = await getTestReportApi()
          this.apiKey = this.apiKey.data.muses_api.api_first_test
        } else if (this.test_type === '服务端验收') {
          this.apiKey = await getTestReportApi()
          console.log('apiKey', this.apiKey)
          this.apiKey = this.apiKey.data.muses_api.api_server_test
        } else if (this.test_type === 'Web验收') {
          this.apiKey = await getTestReportApi()
          this.apiKey = this.apiKey.data.muses_api.api_server_test
        } else {
          this.apiKey = ''
        }
        console.log('apiKey', this.apiKey)
        // test smoke
        // this.apiKey = 'Bearer app-R0lRNeAQHeCOzItzSCiLDdYV'
        // test first
        // this.apiKey = 'Bearer app-C4xkJEUKw1FqFiE534wWQOv2'
        // this.apiKey = 'Bearer app-fPFxd0MnHDhAFyFdr4Wo8MIX'

        const header = {
          Authorization: this.apiKey,
          'Content-Type': 'application/json'
        }

        const muses_url = 'https://muses.weizhipin.com/v1/chat-messages'
        const payload = {
          inputs: {},
          query: reportData,
          response_mode: 'streaming',
          conversation_id: '',
          user: email,
          files: []
        }

        const response = await fetch(muses_url, {
          method: 'POST',
          headers: header,
          body: JSON.stringify(payload)
        })

        const reader = response.body.getReader()
        const decoder = new TextDecoder()

        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const jsonStrings = chunk.split('\n').filter(str => str.trim())

          for (const jsonStr of jsonStrings) {
            console.log('jsonStr', jsonStr)
            try {
              if (jsonStr.includes('node_finished')) continue

              const parsedData = JSON.parse(jsonStr.split('data: ')[1])
              if ('answer' in parsedData) {
                // 将返回的文本转换为HTML格式，保留换行符
                const formattedText = parsedData.answer.replace(/\n/g, '<br>')
                this.musesAnalysisResult += formattedText
              }
            } catch (e) {
              console.warn('解析JSON失败:', e, '数据:', jsonStr)
            }
          }
        }
      } catch (error) {
        this.$message.error('分析报告生成失败')
        console.error(error)
      }
    },

    async testButton() {
      this.getCoverageStaticInfo(this.selectedItem.recordName, 'Web验收')
      // this.getBugStaticInfo(this.selectedItem.recordName, '客户端验收-IOS')
      // this.getWikiStaticInfo(this.selectedItem.recordName, '服务端验收')
      // try {
      //   const resp = await getCoverageStaticPost(this.selectedItem.recordName);
      //   if (resp && resp.data) {
      //     this.handleCoverageTrendData(resp.data);
      //   }
      // } catch (error) {
      //   console.error('获取覆盖度趋势数据失败:', error);
      //   this.$message.error('获取覆盖度趋势数据失败');
      // }
    },

    versionSelect(projectType) {
      var self = this
      self.version = ''
      console.log('打印versionSelect中projectType的值')
      console.log(projectType)
      dubboBugStatisVersionListFromMagpieData(projectType)
        .then(resp => {
          console.log('打印versionSelect中的resp的值')
          console.log(resp)
          if ((resp.status = 'success')) {
            self.versionList = resp.data.list
            if (4 == projectType) {
              self.versionList = self.versionList.filter(item => item.recordName.charAt(0) == 'C')
            } else {
              self.versionList = self.versionList.filter(item => item.recordName != '非版本')
            }
          }
          //加上这部分逻辑解决问题：切换项目projectType的值，echart v-if 判断有问题，需要确保DOM更新后再初始化echarts实例。
          if (projectType == 1 || projectType == 5) {
            this.$nextTick(() => {
              this.initChart() //DOM更新后初始化表
            })
          } else {
            if (this.chartFour) {
              this.chartFour.dispose() //销毁图标实例
              this.chartFour = null
            }
          }
        })
        .catch(() => {
          // this.$emit("getCasePlan")
        })
    },
    handleChange(value) {
      // 判断是版本还是测试阶段
      let versionItem = this.versionList.find(option => option.recordName === value)
      if (versionItem) {
        console.log('selectedItem')
        console.log(this.selectedItem)
        this.selectedItem = versionItem
        if (this.test_type) {
          this.getStatInfoList()
        }
      } else {
        // 说明是切换了测试阶段
        if (this.selectedItem) {
          this.getStatInfoList()
        }
      }
    },
    getStatInfoList() {
      // 没有选择版本和测试阶段时给提示拦截
      if (!this.version || !this.test_type) {
        this.$message.warning('请先选择版本和测试阶段')
        return
      }
      // 清空AI分析报告内容
      this.musesAnalysisResult = ''
      this.loading = true
      const recordId = this.selectedItem.recordId
      const recordTitle = this.selectedItem.recordName
      const projectType = this.projectType
      const test_type = this.test_type

      // 确保DOM已经更新
      this.$nextTick(async () => {
        try {
          // 确保图表实例已初始化
          if (!this.chartOne) {
            this.initChart()
          }

          const [testCaseResp, bugStaticResp, wikiTypeResp, wikiTestResp, coverageResp, dubboBugResp, coverageTrendResp] = await Promise.all([
            this.getTestCaseStaticInfo(recordTitle, test_type),
            getBugStaticPost(recordTitle, test_type),
            this.getWikiTypeStaticInfo(recordTitle, test_type),
            this.getWikiStaticInfo(recordTitle, test_type),
            this.getCoverageStaticInfo(recordTitle, test_type),
            dubboBugStatisDetailListFromMagpieData(recordId, recordTitle, projectType),
            this.isAcceptance ? getCoverageStaticPost(recordTitle, test_type) : Promise.resolve(null)
          ])

          // 打印API返回数据
          console.log('bugStaticResp 数据:', bugStaticResp?.data)
          console.log('bug_by_module_distribution:', bugStaticResp?.data?.bug_by_module_distribution)

          // 处理覆盖度趋势数据
          if (this.isAcceptance && coverageTrendResp && coverageTrendResp.data) {
            this.handleCoverageTrendData(coverageTrendResp.data)
          }

          // 更新数据
          if (bugStaticResp && bugStaticResp.data) {
            this.newBugStatisDetailList = bugStaticResp.data

            // 处理模块分布数据
            if (bugStaticResp.data.bug_by_module_distribution && this.chartThree) {
              console.log('模块分布数据:', bugStaticResp.data.bug_by_module_distribution)
              const bugModelDis = []
              for (const [module, count] of Object.entries(bugStaticResp.data.bug_by_module_distribution)) {
                console.log('处理模块:', module, '数量:', count)
                if (count > 0) {
                  bugModelDis.push({
                    name: module,
                    value: count
                  })
                }
              }
              console.log('处理后的模块分布数据:', bugModelDis)
              // 更新模块分布图表
              this.bugModuleDistributionOption.series[0].data = bugModelDis
              this.bugModuleDistributionOption.legend.data = bugModelDis.map(item => item.name)
              console.log('图表配置:', this.bugModuleDistributionOption)
              this.chartThree.setOption(this.bugModuleDistributionOption)
            } else {
              console.warn('模块分布数据或图表实例不存在:', 'bugStaticResp.data:', !!bugStaticResp.data, 'bug_by_module_distribution:', !!bugStaticResp.data?.bug_by_module_distribution, 'chartThree:', !!this.chartThree)
            }
          }

          // 整合所有数据用于 muses 分析
          this.reportData = {
            version: recordTitle,
            test_type: test_type,
            test_case_info: testCaseResp?.data || {},
            bug_static_info: bugStaticResp?.data || {},
            wiki_type_info: wikiTypeResp?.data || [],
            wiki_test_info: wikiTestResp?.data || {},
            coverage_info: coverageResp?.data || {},
            dubbo_bug_info: dubboBugResp?.data || {}
          }

          // 检查必要的数据是否都已加载完成
          const versionNumber = recordTitle.match(/\d+\.\d+/)?.[0]?.replace('.', '') || ''
          const baseResult = 'V' + versionNumber + test_type + '结果：'

          if (test_type == '冒烟测试') {
            if (bugStaticResp?.data && testCaseResp?.data) {
              // 更新各个条件的状态
              this.p1p2BugCondition = bugStaticResp.data.p1_p2_bug_num <= 5
              this.testCaseRateCondition = testCaseResp.data.test_case_rate >= 80
              this.bugIndexCondition = bugStaticResp.data.bug_index <= 1

              if (this.p1p2BugCondition && this.testCaseRateCondition && this.bugIndexCondition) {
                this.test_result = baseResult + '通过'
              } else {
                this.test_result = baseResult + '不通过'
              }
            } else {
              console.warn('部分数据未加载完成，无法判断测试结果')
              this.test_result = baseResult
              // 重置条件状态
              this.p1p2BugCondition = true
              this.testCaseRateCondition = true
              this.bugIndexCondition = true
            }
          } else if (test_type == '一轮测试') {
            // 一轮测试通过规则：没有未解决的P1、P2bug，所有测试用例执行率>=70%，从创建时间开始到今天小于3天仍未解决的bug数<=3
            if (bugStaticResp?.data && testCaseResp?.data) {
              // 更新各个条件的状态
              this.p1p2BugCondition = bugStaticResp.data.unresolve_p1_p2_bug_num == 0
              this.testCaseRateCondition = testCaseResp.data.test_case_rate >= 70
              this.bugIndexCondition = bugStaticResp.data.warn_bug_list_3days.length <= 3
              // 待修复bug数/累计提交bug数<=30% 表示通过
              this.backlogRateCondition = bugStaticResp.data.unsolved_bug_num / bugStaticResp.data.bug_num <= 0.3

              if (this.p1p2BugCondition && this.testCaseRateCondition && this.bugIndexCondition && this.backlogRateCondition) {
                this.test_result = baseResult + '通过'
              } else {
                this.test_result = baseResult + '不通过'
              }
            } else {
              console.warn('部分数据未加载完成，无法判断测试结果')
              this.test_result = baseResult
              // 重置条件状态
              this.p1p2BugCondition = true
              this.testCaseRateCondition = true
              this.bugIndexCondition = true
            }
          } else if (test_type == '服务端验收') {
            if (bugStaticResp?.data) {
              // 更新各个条件的状态
              this.hasUnresolvedServerBug = bugStaticResp.data.unsolved_bug_num > 0
              if (!this.hasUnresolvedServerBug) {
                this.test_result = baseResult + '通过'
              } else {
                this.test_result = baseResult + '不通过'
              }
            } else {
              console.warn('部分数据未加载完成，无法判断测试结果')
              this.test_result = baseResult
              this.hasUnresolvedServerBug = true
            }
          } else if (test_type == 'Web验收') {
            if (bugStaticResp?.data) {
              // 更新各个条件的状态
              this.hasUnresolvedWebBug = bugStaticResp.data.unsolved_bug_num > 0
              if (!this.hasUnresolvedWebBug) {
                this.test_result = baseResult + '通过'
              } else {
                this.test_result = baseResult + '不通过'
              }
            } else {
              console.warn('部分数据未加载完成，无法判断测试结果')
              this.test_result = baseResult
              this.hasUnresolvedWebBug = true
            }
          } else if (test_type == '客户端验收-IOS') {
            if (bugStaticResp?.data) {
              // 更新各个条件的状态
              this.hasUnresolvedIosBug = bugStaticResp.data.unsolved_bug_num > 0
              if (!this.hasUnresolvedIosBug) {
                this.test_result = baseResult + '通过'
              } else {
                this.test_result = baseResult + '不通过'
              }
            } else {
              console.warn('部分数据未加载完成，无法判断测试结果')
              this.test_result = baseResult
              this.hasUnresolvedIosBug = true
            }
          } else if (test_type == '客户端验收-Android') {
            if (bugStaticResp?.data) {
              // 更新各个条件的状态
              this.hasUnresolvedAndroidBug = bugStaticResp.data.unsolved_bug_num > 0
              if (!this.hasUnresolvedAndroidBug) {
                this.test_result = baseResult + '通过'
              } else {
                this.test_result = baseResult + '不通过'
              }
            } else {
              console.warn('部分数据未加载完成，无法判断测试结果')
              this.test_result = baseResult
              this.hasUnresolvedAndroidBug = true
            }
          }

          // 更新数据
          if (bugStaticResp && bugStaticResp.data) {
            this.newBugStatisDetailList = bugStaticResp.data
          }

          // 更新 dubboBugStatisDetailList 数据
          if (dubboBugResp && dubboBugResp.data) {
            this.dubboBugStatisDetailList = dubboBugResp.data
          }

          // 等待DOM更新完成后再更新图表
          await this.$nextTick()

          // 更新所有图表
          this.updateAllCharts()
          this.getBugStatusPieChart()
          this.updateWikiTypeChart()
          this.updateBugCreateTimeChart()
          // this.updateBugSolveTimeChart();
          this.updateBugResolveTimeDistributionChart()
        } catch (error) {
          console.error('获取数据失败:', error)
          this.$message.error('获取数据失败，请稍后重试')
        } finally {
          this.loading = false
        }
      })
    },

    // 更新initChart方法，添加错误处理
    initChart() {
      try {
        // 如果没有选择测试阶段，不初始化图表
        if (!this.test_type) {
          return
        }

        this.$nextTick(() => {
          // 需求类型分布图表
          if (this.$refs.chartFive) {
            this.chartFive = this.$echarts.init(this.$refs.chartFive)
            this.chartFive.setOption(this.wikiTypeDistributionOption)
          }
          // 初始化覆盖度分布图表
          if (this.$refs.coverageChart) {
            this.coverageChart = this.$echarts.init(this.$refs.coverageChart)
            this.coverageChart.setOption(this.coverageDistributionOption)
          }

          // Bug优先级分布图表
          if (this.$refs.chartTwo) {
            this.chartTwo = this.$echarts.init(this.$refs.chartTwo)
            this.chartTwo.setOption(this.bugPriorityDistributionOption)
            console.log('优先级图表初始化成功')
          } else {
            console.warn('优先级图表容器不存在')
          }

          // Bug状态分布图表
          if (this.$refs.chartOne && (this.test_type == '冒烟测试' || this.test_type == '一轮测试')) {
            this.chartOne = this.$echarts.init(this.$refs.chartOne)
            this.chartOne.setOption(this.bugStatusDistributionOption)
          }

          // Bug归属分布图表
          if (this.$refs.chartFour && (this.test_type == '冒烟测试' || this.test_type == '一轮测试')) {
            this.chartFour = this.$echarts.init(this.$refs.chartFour)
            this.chartFour.setOption(this.bugProblemBelongOption)
          }

          // 模块分布图表
          if (this.$refs.chartThree) {
            this.chartThree = this.$echarts.init(this.$refs.chartThree)
            this.chartThree.setOption(this.bugModuleDistributionOption)
          }

          // 初始化Bug类型分布图表
          if (this.$refs.bugTypeChart) {
            console.log('初始化Bug类型分布图表')
            this.bugTypeChart = this.$echarts.init(this.$refs.bugTypeChart)
            this.bugTypeChart.setOption(this.bugTypeDistributionOption)
          }

          // 初始化Bug解决时间分布折线图
          if (this.$refs.bugResolveTimeDistributionChart) {
            console.log('初始化Bug解决时间分布图表')
            this.bugResolveTimeDistributionChart = this.$echarts.init(this.$refs.bugResolveTimeDistributionChart)
            this.bugResolveTimeDistributionChart.setOption(this.bugResolveTimeDistributionOption)
          }

          // 初始化bug创建时间分布图表
          if (this.$refs.bugCreateTimeChart && this.test_type === '一轮测试') {
            this.bugCreateTimeChart = this.$echarts.init(this.$refs.bugCreateTimeChart)
            this.bugCreateTimeChart.setOption(this.bugCreateTimeOption)
          }

          // 初始化各端bug类型分布图表
          if (this.$refs.androidChart) {
            this.androidChart = this.$echarts.init(this.$refs.androidChart)
            this.androidChart.setOption(this.androidBugTypeOption)
          }

          if (this.$refs.iosChart) {
            this.iosChart = this.$echarts.init(this.$refs.iosChart)
            this.iosChart.setOption(this.iosBugTypeOption)
          }

          if (this.$refs.pcChart) {
            this.pcChart = this.$echarts.init(this.$refs.pcChart)
            this.pcChart.setOption(this.pcBugTypeOption)
          }

          if (this.$refs.serverChart) {
            this.serverChart = this.$echarts.init(this.$refs.serverChart)
            this.serverChart.setOption(this.serverBugTypeOption)
          }

          // 初始化解决时间分布图表
          // if (this.$refs.bugSolveTimeChart && this.test_type === '一轮测试') {
          //   this.bugSolveTimeChart = this.$echarts.init(this.$refs.bugSolveTimeChart);
          //   this.bugSolveTimeChart.setOption(this.bugSolveTimeOption);
          // }

          // 冒烟阶段bug指数趋势变化图
          if (this.$refs.chartSix && this.test_type == '冒烟测试') {
            this.chartSix = this.$echarts.init(this.$refs.chartSix)
            this.chartSix.setOption(this.historySmokeBugOption)
          }

          // 更新bug修复时长图表
          if (this.$refs.bugRepairDurationChart && this.test_type && this.test_type !== '冒烟测试' && this.test_type !== '一轮测试') {
            this.bugRepairDurationChart = this.$echarts.init(this.$refs.bugRepairDurationChart)
            this.bugRepairDurationChart.setOption(this.bugRepairDurationOption)
          }

          // 添加窗口resize事件监听
          window.addEventListener('resize', () => {
            // 按照图表初始化的相同顺序排列
            this.chartFive && this.chartFive.resize() // 需求类型分布图表
            this.coverageChart && this.coverageChart.resize() // 覆盖度分布图表
            this.chartTwo && this.chartTwo.resize() // Bug优先级分布图表
            this.chartOne && this.chartOne.resize() // Bug状态分布图表
            this.chartFour && this.chartFour.resize() // Bug归属分布图表
            this.chartThree && this.chartThree.resize() // 模块分布图表
            this.bugTypeChart && this.bugTypeChart.resize() // Bug类型分布图表
            this.bugResolveTimeDistributionChart && this.bugResolveTimeDistributionChart.resize() // Bug解决时间分布图表
            this.bugCreateTimeChart && this.bugCreateTimeChart.resize() // Bug创建时间分布图表

            // 各端bug类型分布图表
            this.androidChart && this.androidChart.resize()
            this.iosChart && this.iosChart.resize()
            this.pcChart && this.pcChart.resize()
            this.serverChart && this.serverChart.resize()

            // this.bugSolveTimeChart && this.bugSolveTimeChart.resize();  // Bug解决时间图表
            if (this.test_type == '冒烟测试') {
              this.chartSix && this.chartSix.resize() // 冒烟阶段bug指数趋势图
            }
            this.bugRepairDurationChart && this.bugRepairDurationChart.resize() // Bug修复时长图表
          })
        })
      } catch (error) {
        console.error('图表初始化失败:', error)
      }
    },

    // 更新所有图表
    updateAllCharts() {
      this.$nextTick(async () => {
        try {
          // 如果没有选择测试阶段，不更新图表
          if (!this.test_type) {
            return
          }

          // 确保所有图表实例都已初始化
          if (!this.chartOne || !this.chartTwo || !this.chartThree || !this.bugTypeChart || !this.bugRepairDurationChart) {
            console.log('重新初始化图表实例')
            await this.initChart()
          }

          // 更新bug修复时长图表
          if (this.newBugStatisDetailList && this.newBugStatisDetailList.bug_repair_verify_duration) {
            await this.initBugRepairDurationChart(this.newBugStatisDetailList.bug_repair_verify_duration)
          }

          // 先更新优先级图表
          await this.updateBugPriorityChart()

          // 更新bug状态和归属分布图表
          await this.getBugStatusPieChart()

          // 更新模块分布图表
          if (this.newBugStatisDetailList && this.newBugStatisDetailList.bug_by_module_distribution && this.chartThree) {
            console.log('开始更新模块分布图表')
            console.log('模块分布数据:', this.newBugStatisDetailList.bug_by_module_distribution)

            const bugModelDis = []
            // 处理新的数据结构：数组形式
            this.newBugStatisDetailList.bug_by_module_distribution.forEach(item => {
              console.log('处理模块:', item.businessGroup, '总数:', item.sum)
              if (item.sum > 0) {
                bugModelDis.push({
                  name: item.businessGroup,
                  value: item.sum
                })
              }
            })

            console.log('处理后的模块分布数据:', bugModelDis)

            // 更新图表配置
            this.bugModuleDistributionOption.series[0].data = bugModelDis
            this.bugModuleDistributionOption.legend.data = bugModelDis.map(item => item.name)

            // 确保图表实例存在并更新
            if (this.chartThree) {
              console.log('更新图表配置')
              this.chartThree.setOption(this.bugModuleDistributionOption, true)
            } else {
              console.warn('图表实例不存在')
            }
          } else {
            console.warn('模块分布数据或图表实例不存在:', {
              hasNewBugStatisDetailList: !!this.newBugStatisDetailList,
              hasBugByModuleDistribution: !!(this.newBugStatisDetailList && this.newBugStatisDetailList.bug_by_module_distribution),
              hasChartThree: !!this.chartThree
            })
          }

          // 更新各端bug类型分布图表
          await this.updateBugTypeCharts()

          // 更新Bug类型分布图表
          await this.updateBugTypeDistributionChart()

          // 更新Bug解决时间分布折线图
          await this.updateBugResolveTimeDistributionChart()

          // 最后更新bug创建时间分布图表和解决时间分布图表
          if (this.test_type === '一轮测试' && this.bugCreateTimeChart) {
            await this.updateBugCreateTimeChart()
          }

          // 等待一个小的延时，确保之前的图表都已渲染完成
          await new Promise(resolve => setTimeout(resolve, 100))

          // 更新其他图表
          if (this.test_type === '冒烟测试') {
            await this.updateHistorySmokeBugChart()
          }

          // await this.updateBugSolveTimeChart();
        } catch (error) {
          console.error('更新图表失败:', error)
        }
      })
    },

    // 处理各端bug类型数据
    updateBugTypeCharts() {
      if (!this.newBugStatisDetailList || !this.newBugStatisDetailList.bug_classification_distribution) {
        console.warn('Bug类型分布数据未准备好')
        return
      }

      const bugTypeData = this.newBugStatisDetailList.bug_classification_distribution

      try {
        // 处理Android数据
        if (this.androidChart && (this.test_type === '一轮测试' || this.test_type === '冒烟测试' || this.test_type === '客户端验收-Android')) {
          const androidData = []
          for (const type in bugTypeData.android_classification_count) {
            androidData.push({
              name: type,
              value: bugTypeData.android_classification_count[type]
            })
          }
          this.androidBugTypeOption.series[0].data = androidData
          this.androidBugTypeOption.legend.data = androidData.map(item => item.name)
          this.androidChart.setOption(this.androidBugTypeOption)
        }

        // 处理iOS数据
        if (this.iosChart && (this.test_type === '一轮测试' || this.test_type === '冒烟测试' || this.test_type === '客户端验收-IOS')) {
          const iosData = []
          for (const type in bugTypeData.ios_classification_count) {
            iosData.push({
              name: type,
              value: bugTypeData.ios_classification_count[type]
            })
          }
          this.iosBugTypeOption.series[0].data = iosData
          this.iosBugTypeOption.legend.data = iosData.map(item => item.name)
          this.iosChart.setOption(this.iosBugTypeOption)
        }

        // 处理PC&H5数据
        if (this.pcChart && (this.test_type === '一轮测试' || this.test_type === '冒烟测试' || this.test_type === 'Web验收')) {
          const pcData = []
          for (const type in bugTypeData.pc_h5_classification_count) {
            pcData.push({
              name: type,
              value: bugTypeData.pc_h5_classification_count[type]
            })
          }
          this.pcBugTypeOption.series[0].data = pcData
          this.pcBugTypeOption.legend.data = pcData.map(item => item.name)
          this.pcChart.setOption(this.pcBugTypeOption)
        }

        // 处理Server数据
        if (this.serverChart && (this.test_type === '一轮测试' || this.test_type === '冒烟测试' || this.test_type === '服务端验收')) {
          const serverData = []
          for (const type in bugTypeData.server_classification_count) {
            serverData.push({
              name: type,
              value: bugTypeData.server_classification_count[type]
            })
          }
          this.serverBugTypeOption.series[0].data = serverData
          this.serverBugTypeOption.legend.data = serverData.map(item => item.name)
          this.serverChart.setOption(this.serverBugTypeOption)
        }
      } catch (error) {
        console.error('更新Bug类型分布图表失败:', error)
      }
    },

    // 新增需求类型图表更新方法
    updateWikiTypeChart() {
      if (!this.newWikiTypeResult || !this.newWikiTypeResult.length) {
        return
      }

      const wikiTypeDis = this.newWikiTypeResult.map(item => ({
        name: item.name,
        value: item.value
      }))

      this.wikiTypeDistributionOption.series[0].data = wikiTypeDis
      this.wikiTypeDistributionOption.legend.data = wikiTypeDis.map(item => item.name)
      if (this.chartFive) {
        this.chartFive.setOption(this.wikiTypeDistributionOption)
      }
    },

    // 更新bug优先级分布图表
    updateBugPriorityChart() {
      if (!this.newBugStatisDetailList) {
        console.warn('Bug统计数据未准备好')
        return
      }

      try {
        // 如果图表实例不存在，尝试重新初始化
        if (!this.chartTwo && this.$refs.chartTwo) {
          console.log('尝试重新初始化优先级图表')
          this.chartTwo = this.$echarts.init(this.$refs.chartTwo)
        }

        if (!this.chartTwo) {
          console.warn('优先级图表实例未初始化，等待下一次更新')
          // 延迟重试一次
          setTimeout(() => {
            if (this.$refs.chartTwo) {
              this.chartTwo = this.$echarts.init(this.$refs.chartTwo)
              this.updateBugPriorityChart()
            }
          }, 100)
          return
        }

        // 从bug_by_module_distribution中获取各优先级的总和
        let p1Sum = 0
        let p2Sum = 0
        let p3Sum = 0
        let p4Sum = 0
        let p5Sum = 0

        this.newBugStatisDetailList.bug_by_module_distribution.forEach(item => {
          p1Sum += item.p1Count || 0
          p2Sum += item.p2Count || 0
          p3Sum += item.p3Count || 0
          p4Sum += item.p4Count || 0
          p5Sum += item.p5Count || 0
        })

        // 转换为图表数据格式
        const chartData = [
          { name: 'P1', value: p1Sum },
          { name: 'P2', value: p2Sum },
          { name: 'P3', value: p3Sum },
          { name: 'P4', value: p4Sum },
          { name: 'P5', value: p5Sum }
        ].filter(item => item.value > 0) // 只显示有数据的优先级

        console.log('优先级图表数据:', chartData)

        // 更新图表配置
        this.bugPriorityDistributionOption.series[0].data = chartData
        this.bugPriorityDistributionOption.legend.data = chartData.map(item => item.name)

        // 更新图表
        this.chartTwo.setOption(this.bugPriorityDistributionOption, true)
      } catch (error) {
        console.error('更新优先级图表失败:', error)
      }
    },

    // 添加回getBugStatusPieChart方法
    getBugStatusPieChart() {
      try {
        if (!this.dubboBugStatisDetailList || !this.dubboBugStatisDetailList.statusFormList) {
          console.warn('数据未准备好')
          return
        }

        //bug状态
        const bugStatusDis = []
        const bugStatusLegend = []
        const list = this.dubboBugStatisDetailList.statusFormList

        for (const item of list) {
          const bugStatus = {
            name: item.status,
            value: item.sum
          }
          bugStatusDis.push(bugStatus)
          bugStatusLegend.push(bugStatus.name)
        }

        // 更新bug状态分布
        if (this.chartOne) {
          this.bugStatusDistributionOption.series[0].data = bugStatusDis.filter(item => item.value != 0)
          this.bugStatusDistributionOption.legend.data = bugStatusLegend.filter(item => item.value != 0)
          this.chartOne.setOption(this.bugStatusDistributionOption)
        }

        // 更新问题所属分布
        if (this.dubboBugStatisDetailList.issueOwnershipFormList && this.chartFour) {
          const problemBelongDis = this.dubboBugStatisDetailList.issueOwnershipFormList
            .map(item => ({
              name: item.issueOwnership,
              value: item.sum
            }))
            .filter(item => item.value != 0)

          this.bugProblemBelongOption.series[0].data = problemBelongDis
          this.bugProblemBelongOption.legend.data = problemBelongDis.map(item => item.name)
          this.chartFour.setOption(this.bugProblemBelongOption)
        }
      } catch (error) {
        console.error('更新图表失败:', error)
      }
    },

    // 更新历史冒烟阶段bug/需求比折线图
    updateHistorySmokeBugChart() {
      if (!this.newBugStatisDetailList || !this.newBugStatisDetailList.history_smoke_bug_static) {
        console.warn('历史冒烟数据(history_smoke_bug_static)为空, 无法更新折线图')
        return
      }

      const historyData = this.newBugStatisDetailList.history_smoke_bug_static

      if (!Array.isArray(historyData) || historyData.length === 0) {
        console.warn('历史冒烟数据格式不正确或为空数组')
        this.historySmokeBugOption.xAxis.data = []
        this.historySmokeBugOption.series[0].data = []
        if (this.chartSix) {
          this.chartSix.setOption(this.historySmokeBugOption, true)
        }
        return
      }

      // 更新X轴数据
      this.historySmokeBugOption.xAxis.data = historyData.map(item => item.version)

      // 更新折线数据，并确保tooltip可以访问到所有需要的数据
      this.historySmokeBugOption.series[0].data = historyData.map(item => ({
        value: item.bug_index,
        bug_num: item.bug_num,
        wiki_num: item.wiki_num,
        bug_index: item.bug_index
      }))

      // 修复tooltip formatter
      this.historySmokeBugOption.tooltip.formatter = function (params) {
        const data = params[0].data
        return `${params[0].name}<br/>
                Bug数量: ${data.bug_num}<br/>
                需求数量: ${data.wiki_num}<br/>
                Bug指数: ${data.bug_index}`
      }

      // 更新图表
      if (this.chartSix) {
        this.chartSix.setOption(this.historySmokeBugOption, true) // 使用 notMerge: true 来确保旧数据被清除
      }
    },

    // 更新覆盖度分布图表
    updateCoverageDistributionChart(data) {
      if (!data || !data.distribution_static) {
        console.warn('覆盖度分布数据未准备好')
        return
      }

      const distributionData = []
      const distribution = data.distribution_static

      // 计算已统计的总数
      let totalCounted = 0
      for (const [range, count] of Object.entries(distribution)) {
        if (count > 0) {
          totalCounted += count
          distributionData.push({
            name: range,
            value: count
          })
        }
      }

      // 添加未统计覆盖度的数据
      if (data.total_wiki_num) {
        const noCoverageNum = data.total_wiki_num - totalCounted
        if (noCoverageNum > 0) {
          distributionData.push({
            name: '未统计',
            value: noCoverageNum
          })
        }
      }

      this.coverageDistributionOption.series[0].data = distributionData

      if (this.coverageChart) {
        this.coverageChart.setOption(this.coverageDistributionOption)
      }
    },

    // 添加更新bug创建时间分布图表的方法
    updateBugCreateTimeChart() {
      if (!this.newBugStatisDetailList || !this.newBugStatisDetailList.bug_create_time_distribution) {
        console.warn('Bug创建时间分布数据未准备好')
        return
      }

      try {
        const timeData = this.newBugStatisDetailList.bug_create_time_distribution
        // 将对象转换为数组，过滤掉数量为0的日期，并按日期排序
        const sortedData = Object.entries(timeData)
          .map(([date, count]) => ({
            date: date,
            count: count
          }))
          .filter(item => item.count > 0) // 过滤掉数量为0的日期
          .sort((a, b) => a.date.localeCompare(b.date))

        this.bugCreateTimeOption.xAxis.data = sortedData.map(item => item.date)
        this.bugCreateTimeOption.series[0].data = sortedData.map(item => item.count)

        if (this.bugCreateTimeChart) {
          this.bugCreateTimeChart.setOption(this.bugCreateTimeOption)
        }
      } catch (error) {
        console.error('更新Bug创建时间分布图表失败:', error)
      }
    },

    // 添加新的方法处理分析按钮点击
    async handleAnalysisClick() {
      if (!this.reportData) {
        this.$message.warning('请先获取测试报告数据')
        return
      }
      this.musesAnalysisResult = '' // 清空之前的结果
      await this.getReportAnalysis(this.reportData)
    },

    // 处理覆盖度趋势数据
    handleCoverageTrendData(data) {
      if (!data || !data.coverage_day_trend) return

      const { coverage_day_trend } = data
      // 处理覆盖度数据
      this.coverageStats.totalCoverage = data.coverage_total.covRate
      this.coverageStats.demandCount = data.coverage_total.prCount
      this.coverageStats.projectCount = data.coverage_total.projectCount
      this.coverageStats.developerCount = data.coverage_total.developerCount
      this.coverageStats.testerCount = data.coverage_total.qaCount
      this.coverageStats.totalLines = data.coverage_total.lineTotalCount
      this.coverageStats.coveredLines = data.coverage_total.lineCoveredCount
      this.coverageStats.ignoredLines = data.coverage_total.lineIgnoredCount
      this.coverageStats.updateTime = data.coverage_total.updateTime

      // 处理趋势数据
      const dates = new Set()
      const coveredMap = new Map()
      const totalMap = new Map()
      const coverageMap = new Map()

      // 处理柱状图数据
      coverage_day_trend.dataBar.forEach(item => {
        dates.add(item.category)
        if (item.type === '已覆盖行') {
          coveredMap.set(item.category, item.value)
        } else if (item.type === '总新增行') {
          totalMap.set(item.category, item.value)
        }
      })

      // 处理折线图数据
      coverage_day_trend.dataLine.forEach(item => {
        dates.add(item.category)
        if (item.type === '覆盖度') {
          coverageMap.set(item.category, item.value)
        }
      })

      // 按日期排序
      const sortedDates = Array.from(dates).sort()

      // 更新图表数据
      this.coverageTrendChartOption.xAxis.data = sortedDates
      this.coverageTrendChartOption.series[0].data = sortedDates.map(date => totalMap.get(date) || 0)
      this.coverageTrendChartOption.series[1].data = sortedDates.map(date => coveredMap.get(date) || 0)
      this.coverageTrendChartOption.series[2].data = sortedDates.map(date => coverageMap.get(date) || 0)

      // 更新图表
      this.$nextTick(() => {
        if (!this.coverageTrendChart && this.$refs.coverageTrendChart) {
          this.coverageTrendChart = this.$echarts.init(this.$refs.coverageTrendChart)
        }
        if (this.coverageTrendChart) {
          this.coverageTrendChart.setOption(this.coverageTrendChartOption)
        }
      })
    },

    // 更新Bug类型分布图表
    updateBugTypeDistributionChart() {
      if (!this.newBugStatisDetailList || !this.newBugStatisDetailList.bug_type_distribution) {
        console.warn('Bug类型分布数据未准备好')
        return
      }

      try {
        if (!this.bugTypeChart && this.$refs.bugTypeChart) {
          this.bugTypeChart = this.$echarts.init(this.$refs.bugTypeChart)
        }

        const bugTypeData = []
        const typeColors = {
          改进: '#91cc75',
          缺陷: '#ee6666',
          新功能: '#5470c6'
        }

        Object.entries(this.newBugStatisDetailList.bug_type_distribution).forEach(([type, count]) => {
          bugTypeData.push({
            name: type,
            value: count,
            itemStyle: {
              color: typeColors[type]
            }
          })
        })

        this.bugTypeDistributionOption.series[0].data = bugTypeData
        this.bugTypeDistributionOption.legend.data = bugTypeData.map(item => item.name)

        if (this.bugTypeChart) {
          this.bugTypeChart.setOption(this.bugTypeDistributionOption)
        }
      } catch (error) {
        console.error('更新Bug类型分布图表失败:', error)
      }
    },
    // 更新Bug解决时间分布折线图
    updateBugResolveTimeDistributionChart() {
      if (!this.newBugStatisDetailList || !this.newBugStatisDetailList.bug_resolve_time_distribution) {
        console.warn('Bug解决时间分布数据未准备好')
        return
      }

      try {
        // 先销毁已存在的图表实例
        if (this.bugResolveTimeDistributionChart) {
          this.bugResolveTimeDistributionChart.dispose()
          this.bugResolveTimeDistributionChart = null
        }

        // 重新初始化图表
        if (this.$refs.bugResolveTimeDistributionChart) {
          this.bugResolveTimeDistributionChart = this.$echarts.init(this.$refs.bugResolveTimeDistributionChart)
        }

        const timeData = this.newBugStatisDetailList.bug_resolve_time_distribution
        const timeMapping = {
          '0-1d': '0-1天',
          '1-2d': '1-2天',
          '2-3d': '2-3天',
          '3-4d': '3-4天',
          '4-7d': '4-7天',
          '7d+': '7天以上'
        }

        // 按照固定顺序获取数据
        const xAxisData = Object.keys(timeMapping).map(key => timeMapping[key])
        const seriesData = Object.keys(timeMapping).map(key => timeData[key] || 0)

        const option = {
          title: {
            text: 'Bug解决时间分布',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'normal'
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'line',
              lineStyle: {
                color: '#5470c6',
                type: 'dashed'
              }
            },
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderColor: '#5470c6',
            borderWidth: 1,
            textStyle: {
              color: '#333'
            },
            formatter: '{b}<br/>{a}: {c}个'
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '8%',
            top: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              interval: 0,
              rotate: 30,
              color: '#666'
            },
            axisLine: {
              lineStyle: {
                color: '#ddd'
              }
            },
            axisTick: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            name: 'Bug数量',
            nameTextStyle: {
              color: '#666',
              padding: [0, 0, 0, 30]
            },
            axisLabel: {
              color: '#666'
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: '#eee',
                type: 'dashed'
              }
            }
          },
          series: [
            {
              name: 'Bug数量',
              type: 'line',
              data: seriesData,
              smooth: true,
              symbol: 'emptyCircle',
              symbolSize: 8,
              label: {
                show: true,
                position: 'top',
                formatter: '{c}',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                borderColor: '#5470c6',
                borderWidth: 1,
                borderRadius: 4,
                padding: [2, 4],
                distance: 8
              },
              lineStyle: {
                width: 3,
                shadowColor: 'rgba(84, 112, 198, 0.3)',
                shadowBlur: 10
              },
              itemStyle: {
                color: '#5470c6',
                borderWidth: 2
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(84, 112, 198, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(84, 112, 198, 0.02)'
                    }
                  ]
                }
              },
              emphasis: {
                scale: true,
                focus: 'series'
              }
            }
          ]
        }

        if (this.bugResolveTimeDistributionChart) {
          this.bugResolveTimeDistributionChart.setOption(option)
        }
      } catch (error) {
        console.error('更新Bug解决时间分布图表失败:', error)
      }
    },
    initBugRepairDurationChart(data) {
      if (!this.$refs.bugRepairDurationChart) return

      console.log('初始化bug修复时长图表，数据:', data)
      const xAxisData = ['0-1天', '1-2天', '2-3天', '3-4天', '4-7天', '7天+']
      const repairData = [data['0-1d'].bug_repair_duration_0_1d, data['1-2d'].bug_repair_duration_1_2d, data['2-3d'].bug_repair_duration_2_3d, data['3-4d'].bug_repair_duration_3_4d, data['4-7d'].bug_repair_duration_4_7d, data['7d+']['bug_repair_duration_7d+']]
      const verifyData = [data['0-1d'].bug_verify_duration_0_1d, data['1-2d'].bug_verify_duration_1_2d, data['2-3d'].bug_verify_duration_2_3d, data['3-4d'].bug_verify_duration_3_4d, data['4-7d'].bug_verify_duration_4_7d, data['7d+']['bug_verify_duration_7d+']]
      const totalData = [data['0-1d'].bug_total_duration_0_1d, data['1-2d'].bug_total_duration_1_2d, data['2-3d'].bug_total_duration_2_3d, data['3-4d'].bug_total_duration_3_4d, data['4-7d'].bug_total_duration_4_7d, data['7d+']['bug_total_duration_7d+']]

      console.log('处理后的数据:', {
        repairData,
        verifyData,
        totalData
      })

      // 更新配置数据
      this.bugRepairDurationOption.xAxis[0].data = xAxisData
      this.bugRepairDurationOption.series[0].data = repairData
      this.bugRepairDurationOption.series[1].data = verifyData
      this.bugRepairDurationOption.series[2].data = totalData

      if (!this.bugRepairDurationChart) {
        this.bugRepairDurationChart = this.$echarts.init(this.$refs.bugRepairDurationChart)
      }
      this.bugRepairDurationChart.setOption(this.bugRepairDurationOption)
    },

    // 处理测试阶段切换
    async handleTestTypeChange() {
      // 先清理所有图表
      this.clearCharts()
      // 等待DOM更新
      await this.$nextTick()
    }
  },
  mounted() {
    console.log('进入mounted')
    this.versionSelect(1)
    // 等待DOM更新后再初始化图表
    this.$nextTick(() => {
      this.initChart()
      // 初始化覆盖度趋势图表
      if (this.$refs.coverageTrendChart) {
        this.coverageTrendChart = this.$echarts.init(this.$refs.coverageTrendChart)
      }
    })
    this.calculateTableHeight()
    window.addEventListener('resize', this.calculateTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateTableHeight)
    // 清理图表实例
    if (this.bugResolveTimeDistributionChart) {
      this.bugResolveTimeDistributionChart.dispose()
      this.bugResolveTimeDistributionChart = null
    }
  },
  watch: {
    test_type: {
      handler: 'handleTestTypeChange',
      immediate: false
    }
  }
}
</script>

<style scoped>
.plan-report {
  display: inline;
}

.plan-report ::v-deep .el-button--primary {
  /* height: 32px; */
}

.plan-report ::v-deep .el-drawer__header {
  font-size: 25px;
}
.plan-report .report-title {
  clear: both;
  font-weight: 400;
  margin-bottom: 15px;
  border-left: 3px solid #2395f1;
  padding-left: 8px;
  display: inline-block;
  /* margin: 0px; */
  padding-top: 4px;
  padding-bottom: 4px;
  margin-left: 5px;
}

.plan-report ::v-deep .el-form-item {
  margin-bottom: 5px;
}
.el-card-totalCases {
  border-color: #5dcfff;
  color: #5dcfff;
  background-color: #f7fcff;
  height: 100px;
}

.el-card-passedCases {
  border-color: #73d897;
  color: #73d897;
  background-color: #f8fdfa;
  height: 100px;
}

.el-card-passingRate {
  border-color: #ffcd5d;
  color: #ffcd5d;
  background-color: #fffcf8;
  height: 100px;
}
.el-card-doneRate {
  border-color: #fa5dff;
  color: #f75dff;
  background-color: #fef8ff;
  height: 100px;
}

.plan-report ::v-deep .el-table td {
  padding: 10px 0 !important;
  font-size: 13px !important;
}

.plan-report ::v-deep .el-table th {
  padding: 6px 0 !important;
}

.plan-report ::v-deep .el-drawer__body {
  overflow: auto;
}

.plan-report ::v-deep .el-table tr {
  font-size: 13px !important;
}

.plan-report ::v-deep .el-table__row td {
  padding: 5px 0 !important;
}

.muses-analysis-card {
  margin: 20px;
  padding: 10px;
  min-height: 100px;
}

.muses-content {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-break: break-word;
  min-height: 60px;
}

.muses-content:empty::before {
  content: '点击「开始分析」按钮获取AI分析报告';
  color: #999;
  font-style: italic;
}

.muses-content ::v-deep p {
  margin: 8px 0;
}

.coverage-stats {
  margin: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.coverage-overview {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.coverage-circle {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.circle-wrapper {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: linear-gradient(90deg, #73d897 0%, #5dcfff 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
}

.circle-value {
  font-size: 36px;
  font-weight: bold;
}

.circle-label {
  font-size: 14px;
  margin-top: 5px;
}

.stat-item {
  text-align: center;
  padding: 10px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.chart-container {
  margin: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
