<template>
  <div style="display: inline" class="import-data">
    <el-button type="primary" size="medium" @click.prevent="clickUserid()">
      <i class="el-icon-plus"></i>
      同步线上回测数据
    </el-button>
    <el-dialog title="同步线上回测数据" :visible.sync="dialogFormVisible" width="600px" :modal-append-to-body="false" :append-to-body="true">
      <el-form ref="form" label-width="100px">
        <el-form-item label="环境：" required>
          <el-radio-group v-model="env">
            <!-- <el-radio label="1">QA1</el-radio> -->
            <el-radio label="4">线上</el-radio>
            <!-- <el-radio label="5">开发</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item label="用户id：" required>
          <el-input placeholder="请输入用户id" v-model="userId"></el-input>
        </el-form-item>

        <el-form-item label="测试人：" required>
          <el-select v-model="name" filterable remote reserve-keyword placeholder="请输入姓名" :remote-method="remoteMethod" :loading="loading" size="medium">
            <el-option v-for="item in userOptionsList" :key="item.value" :label="item.name + item.email" :value="item.name"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="时间：">
          <div class="block">
            <el-date-picker v-model="startTime" type="datetime" placeholder="选择日期时间"></el-date-picker>
            <label>-</label>
            <el-date-picker v-model="endTime" type="datetime" placeholder="选择日期时间"></el-date-picker>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button slot="trigger" size="small" @click.prevent="quit()">
          <i class="el-icon-close"></i>
          取消
        </el-button>
        <el-button slot="trigger" size="small" type="primary" :loading="btnLoading" @click.prevent="syncTesterData()">
          <i class="el-icon-plus"></i>
          同步
        </el-button>
      </div>
    </el-dialog>
    <getCode style="display: none" ref="getCode"></getCode>
  </div>
</template>

<script>
import getCode from '@/views/zpTools/base/getCode'
import { isEmpty } from '@/utils/baseUtil'
import { addUserId, testhubSyncTesterOperaLogs } from '@/api/zpTools/coverage'
import { searchUserInfoList } from '@/api/common/sendRequest'
export default {
  name: 'importData',
  data() {
    return {
      isShow: false,
      dialogFormVisible: false,
      btnLoading: false,
      env: '4',
      userId: '',
      startTime: '',
      endTime: '',
      name: '',
      userOptionsList: [],
      requestForm: {
        message: '',
        url: '',
        status: '',
        response: {
          message: '',
          result: ''
        },
        result: ''
      },

      login: {
        phone: '13811183058',
        password: '',
        http: 'http'
      }
    }
  },

  methods: {
    clickUserid() {
      this.dialogFormVisible = true
    },

    quit() {
      this.dialogFormVisible = false
    },

    //userid查询
    async getUserIdNew(phone) {
      var self = this
      //   self.btnLoading = true
      self.searchUserIdLoad = true
      if (isEmpty(phone)) {
        this.$message({
          message: '手机号 不能为空！',
          type: 'success'
        })
        return
      }
      let name = this.$store.state.user.name
      let arr = { account: phone.toString().trim(), regionCode: this.regionCode }
      let parameters = JSON.stringify(arr).replace('}{', ',')
      let request = {
        parameter: parameters,
        name: name
      }
      const resp = await addUserId(request)
      if (resp.status == 'success') {
        // self.requestForm.result = resp.data.data.userId
        // self.$emit('updateUserId', self.requestForm.result)
        self.$message({
          message: resp.message,
          type: 'success'
        })
      } else {
        self.$message({
          message: resp.message,
          type: 'error'
        })
      }
    },

    async syncTesterData() {
      var self = this
      self.btnLoading = true
      let name = self.name
      let userId = self.userId
      let startTime = self.startTime
      let endTime = self.endTime

      try {
        const resp = await testhubSyncTesterOperaLogs(name, userId, startTime, endTime)
        console.log(resp)
        if (resp.status == 'success') {
          self.$message({
            message: resp.message,
            type: 'success'
          })
        } else {
          self.$message({
            message: resp.message,
            type: 'error'
          })
        }
        // self.btnLoading = false;
        // self.dialogFormVisible = false;
        // self.$emit('queryData')
      } catch (error) {
        self.$message({
          message: error.message,
          type: 'error'
        })
      } finally {
        self.btnLoading = false
        self.dialogFormVisible = false
        self.$emit('queryData')
      }
    },

    async remoteMethod(query) {
      this.userOptionsList = []
      if (!isEmpty(query)) {
        const result = await searchUserInfoList('', query, '')
        this.userOptionsList = result.data
        if (this.userOptionsList) {
          for (var i = 0; i < this.userOptionsList.length; i++) {
            this.userOptionsList[i].value = this.userOptionsList[i].email
          }
        }
      }
    }
  },

  components: {
    getCode
  },

  mounted() {}
}
</script>

<style>
.import-data .el-dialog__body {
  padding-left: 5px;
}

.import-data .el-upload__input {
  display: none;
}

.import-data .upload-demo {
  padding-left: 38px;
}

.el-dialog__body {
  padding-top: 20px;
  padding-right: 20px;
  padding-bottom: 25px;
  padding-left: 20px;
}
</style>
