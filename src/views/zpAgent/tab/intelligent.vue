<template>
  <div class="intelligent-agent-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 欢迎区域 -->
      <div class="welcome-section">
        <div class="welcome-header">
          <h1 class="welcome-title">你好，我是AI智能体~</h1>
          <p class="welcome-subtitle">发现并使用各种强大的AI智能体，一键开始任务</p>
        </div>

        <!-- 输入框区域 -->
        <div class="input-section">
          <div class="input-container">
            <div class="input-with-button">
              <textarea v-model="inputMessage" class="chat-input" :placeholder="placeholderText" rows="3" @keydown.enter.prevent="handleEnterPress" @compositionstart="isComposing = true" @compositionend="isComposing = false"></textarea>
              <button class="send-btn-inside" @click="handleSendMessage" :disabled="!inputMessage">
                <i class="ri-send-plane-fill"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- 智能体推荐区域 -->
        <div class="agents-section">
          <div class="section-header">
            <h3>请选择Agent</h3>
          </div>

          <div class="agents-grid">
            <div class="agent-card" :class="{ selected: selectedAgent && selectedAgent.id === agent.id }" v-for="agent in recommendedAgents" :key="agent.id" @click="selectAgent(agent)">
              <div class="agent-icon">
                <img :src="agent.image" alt="" class="agent-avatar" />
              </div>
              <div class="agent-info">
                <div class="agent-header">
                  <span class="agent-name">{{ agent.name }}</span>
                  <div class="usage-count-tag">
                    <i class="ri-fire-line"></i>
                    <span>{{ agent.usageCount || 0 }}</span>
                  </div>
                </div>
              </div>
              <div class="selected-indicator" v-if="selectedAgent && selectedAgent.id === agent.id">
                <i class="ri-check-line"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAgentList, updateAgent } from '@/api/agent/agent'
import { id } from 'brace/worker/javascript'
export default {
  name: 'IntelligentAgent',
  data() {
    return {
      // API 相关配置
      agentServerUrl: 'https://qa.kanzhun-inc.com/prod-api',
      // agentServerUrl: 'http://127.0.0.1:30001',
      apiPaths: {
        agentList: '/ai_server/api/agent/list'
      },
      agentList: [],
      inputMessage: '',
      recommendedAgents: [],
      selectedAgent: null,
      isComposing: false,
      placeholderText: '请选择智能体，进行开聊~'
    }
  },

  methods: {
    // 获取智能体列表，数据库获取
    async getAgentByDbList() {
      try {
        const res = await getAgentList('', 'agent', 1, 100)
        if (res.code == 0) {
          this.agentList = res.data
          for (let i = 0; i < this.agentList.length; i++) {
            this.agentList[i].image = require('@/views/zpAgent/assets/images/agent' + (i + 1) + '.svg')
            // 为每个智能体添加示例标签（实际开发中应从后端获取）
            this.agentList[i].tags = res.data[i].label.split('、')
          }
        }
      } catch (error) {
        console.log(error)
      }
    },

    //智能体列表获取，实际获取
    async getAgentList() {
      // 创建EventSource连接
      const response = await fetch(this.agentServerUrl + this.apiPaths.agentList, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        }
      })

      const data = await response.json()
      this.recommendedAgents = data.agents
      for (let i = 0; i < this.recommendedAgents.length; i++) {
        this.recommendedAgents[i].image = require('@/views/zpAgent/assets/images/agent' + (i + 1) + '.svg')

        // 模拟使用次数数据，实际项目中应该从API获取
        this.recommendedAgents[i].usageCount = this.agentList.find(item => item.name == this.recommendedAgents[i].name)?.count || 0
        this.recommendedAgents[i].agentdbId = this.agentList.find(item => item.name == this.recommendedAgents[i].name)?.id
      }
    },

    // 更新智能体次数
    async updateAgent(id) {
      try {
        await updateAgent(id)
      } catch (error) {
        console.log(error)
      }
    },

    //处理回车按键
    handleEnterPress(e) {
      if (this.isComposing) {
        return
      }
      if (e.shiftKey) {
        return
      }
      if (this.inputMessage.trim()) {
        this.handleSendMessage()
      }
    },

    //发送消息
    handleSendMessage() {
      if (!this.inputMessage.trim()) {
        return
      }

      if (!this.selectedAgent) {
        this.$message.info('请先选择一个智能体')
        return
      }
      // this.updateAgent(this.selectedAgent.agentdbId)

      this.$router.push({
        path: '/zpagent/aiAgent',
        query: {
          agent: this.selectedAgent.id,
          message: this.inputMessage,
          id: this.selectedAgent.agentdbId
        }
      })
    },

    //设置文案展示
    setPlaceholderText(agent) {
      if (agent.name == '接口分析&用例编写') {
        this.placeholderText = '请输入接口路径，例如：/api/v1/user/info，开启任务~'
      } else {
        this.placeholderText = '请提供需求文档（URL/文字描述），开启任务~'
      }
    },

    //选择智能体
    selectAgent(agent) {
      this.setPlaceholderText(agent)
      // 如果点击的是已选中的智能体，则取消选中
      if (this.selectedAgent && this.selectedAgent.id === agent.id) {
        this.selectedAgent = null
        // 清除输入框中的@智能体名称
        if (this.inputMessage.startsWith('@' + agent.name)) {
          this.inputMessage = this.inputMessage.replace('@' + agent.name + ' ', '').trim()
        }
        return
      }

      // 设置选中的智能体
      // 如果当前输入内容以@开头，替换掉@+名字的内容
      // if (this.inputMessage.startsWith('@') && this.selectedAgent) {
      //   this.inputMessage = this.inputMessage.replace('@' + this.selectedAgent.name, '@' + agent.name + ' ')
      // } else {
      //   this.inputMessage = '@' + agent.name + ' ' + this.inputMessage
      // }
      this.selectedAgent = agent
    },

    // 初始化数据
    async initializeData() {
      try {
        // 先获取数据库中的智能体列表
        await this.getAgentByDbList()
        // 再获取实际的智能体列表
        await this.getAgentList()
      } catch (error) {
        console.error('初始化数据失败:', error)
      }
    }
  },

  mounted() {
    this.initializeData()
  }
}
</script>

<style lang="scss" scoped>
@import '../assets/css/style.css';

.intelligent-agent-container {
  background-color: #ffffff;
  border-radius: 10px;
  // height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  box-sizing: border-box;
  height: calc(100vh - var(--header-height) - 220px);
}

.intelligent-agent-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.main-content {
  width: 100%;
  max-width: 900px;
  position: relative;
  z-index: 1;
}

.welcome-section {
  text-align: center;
}

.welcome-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  position: relative;
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #1a2b4b;
  margin: 0;
  margin-bottom: 1rem;
}

.welcome-subtitle {
  font-size: 1.1rem;
  color: #5d7290;
  margin: 0;
  line-height: 1.5;
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.input-section {
  margin-bottom: 2rem;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.input-with-button {
  position: relative;
  width: 100%;
  display: flex;
  align-items: flex-start;
}

.input-tools {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.tool-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem;
  border: none;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.tool-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
}

.expert-btn {
  background: rgba(118, 75, 162, 0.1);
  color: #764ba2;
}

.expert-btn:hover {
  background: rgba(118, 75, 162, 0.2);
}

.chat-input {
  flex: 1;
  width: 100%;
  min-height: 80px;
  padding: 1rem;
  padding-right: 60px;
  border: 2px solid #f0f2f5;
  border-radius: 10px;
  background: #ffffff;
  font-size: 1rem;
  color: #1a2b4b;
  outline: none;
  transition: all 0.3s ease;
  resize: none;
  font-family: inherit;
  line-height: 1.5;
}

.chat-input:focus {
  border-color: #3a6df0;
  box-shadow: 0 0 0 3px rgba(58, 109, 240, 0.1);
}

.chat-input::placeholder {
  color: #5d7290;
}

.send-btn-inside {
  position: absolute;
  right: 8px;
  bottom: 8px;
  width: 44px;
  height: 44px;
  border: none;
  background: var(--primary-gradient);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
}

.send-btn-inside:hover:not(:disabled) {
  background: #6e92f5;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(58, 109, 240, 0.3);
}

.send-btn-inside:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #5d7290;
}

.agents-section {
  animation: slideUp 0.3s ease-out;
  margin-top: 2rem;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.section-header h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #1a2b4b;
}

.detail-link {
  font-size: 0.9rem;
  color: #666;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  max-width: 900px;
  margin: 0 auto;
}

.agent-card {
  background: #f7f9fc;
  border: 1px solid #f7f9fc;
  border-radius: 16px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: row;
  align-items: center;
  text-align: left;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.agent-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.agent-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
  margin-bottom: 0;
  background: #f5f7fa;
}

.agent-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.agent-info {
  flex: 1;
  text-align: left;
  margin-top: 0;
}

.agent-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.agent-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.agent-card.selected {
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  // border-color: #667eea;
  color: white;
  transform: translateY(-2px);
  // box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.agent-card.selected .agent-name {
  color: white;
  font-weight: 600;
}

.agent-card.selected .agent-icon {
  background: rgba(255, 255, 255, 0.2);
}

.selected-indicator {
  position: absolute;
  top: 15px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #667eea;
  font-weight: bold;
}

.agent-card {
  position: relative;
}

.usage-count-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.2rem 0.4rem;
  // background: rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  font-size: 0.7rem;
  color: #667eea;
  font-weight: 500;
  width: fit-content;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.usage-count-tag i {
  font-size: 0.8rem;
}

.agent-card.selected .usage-count-tag {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.agent-card:hover .usage-count-tag {
  background: rgba(102, 126, 234, 0.15);
  transform: scale(1.05);
}
</style>
