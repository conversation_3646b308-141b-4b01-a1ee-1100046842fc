<template>
  <div class="markdown-body modern-markdown-content" :class="[messageTypeClass, { 'streaming-content': isStreaming }]" v-html="renderedContent"></div>
</template>

<script>
import 'highlight.js/styles/atom-one-dark.css'
import hljs from 'highlight.js'
import MarkdownIt from 'markdown-it'

// 支持的语言列表
const languageList = [
  '1c',
  'abnf',
  'accesslog',
  'ada',
  'arduino',
  'armasm',
  'avrasm',
  'actionscript',
  'angelscript',
  'apache',
  'applescript',
  'arcade',
  'asciidoc',
  'aspectj',
  'autohotkey',
  'autoit',
  'awk',
  'bash',
  'basic',
  'bnf',
  'brainfuck',
  'csharp',
  'c',
  'cpp',
  'cal',
  'cos',
  'cmake',
  'coq',
  'csp',
  'css',
  'capnproto',
  'clojure',
  'coffeescript',
  'crmsh',
  'crystal',
  'd',
  'dart',
  'delphi',
  'diff',
  'django',
  'dns',
  'dockerfile',
  'dos',
  'dsconfig',
  'dts',
  'dust',
  'ebnf',
  'elixir',
  'elm',
  'erlang',
  'excel',
  'fsharp',
  'fix',
  'fortran',
  'gcode',
  'gams',
  'gauss',
  'gherkin',
  'go',
  'golo',
  'gradle',
  'graphql',
  'groovy',
  'xml',
  'http',
  'haml',
  'handlebars',
  'haskell',
  'haxe',
  'hy',
  'ini',
  'inform7',
  'irpf90',
  'json',
  'java',
  'javascript',
  'julia',
  'kotlin',
  'latex',
  'leaf',
  'lasso',
  'less',
  'ldif',
  'lisp',
  'livecodeserver',
  'livescript',
  'lua',
  'makefile',
  'markdown',
  'mathematica',
  'matlab',
  'maxima',
  'mel',
  'mercury',
  'mipsasm',
  'mizar',
  'mojolicious',
  'monkey',
  'moonscript',
  'n1ql',
  'nsis',
  'nginx',
  'nim',
  'nix',
  'ocaml',
  'objectivec',
  'glsl',
  'openscad',
  'ruleslanguage',
  'oxygene',
  'pf',
  'php',
  'parser3',
  'perl',
  'plaintext',
  'pony',
  'pgsql',
  'powershell',
  'processing',
  'prolog',
  'properties',
  'protobuf',
  'puppet',
  'python',
  'profile',
  'q',
  'qml',
  'r',
  'reasonml',
  'rib',
  'rsl',
  'roboconf',
  'ruby',
  'rust',
  'sas',
  'scss',
  'sql',
  'step21',
  'scala',
  'scheme',
  'scilab',
  'shell',
  'smali',
  'smalltalk',
  'sml',
  'stan',
  'stata',
  'stylus',
  'subunit',
  'swift',
  'tcl',
  'tap',
  'thrift',
  'tp',
  'twig',
  'typescript',
  'vbnet',
  'vbscript',
  'vhdl',
  'vala',
  'verilog',
  'vim',
  'x86asm',
  'xl',
  'xquery',
  'yaml',
  'zephir'
]
// 动态注册所有语言
languageList.forEach(lang => {
  try {
    const langModule = require(`highlight.js/lib/languages/${lang}`)
    hljs.registerLanguage(lang, langModule)
  } catch (e) {
    console.warn(`无法加载语言: ${lang}`, e)
  }
})

export default {
  name: 'MarkdownRenderer',
  props: {
    content: {
      type: String,
      required: true
    },
    messageType: {
      type: String,
      default: 'normal', // 可选值: 'normal', 'system', 'user', 'agent'
      validator: function (value) {
        return ['normal', 'system', 'user', 'agent'].indexOf(value) !== -1
      }
    },
    darkMode: {
      type: Boolean,
      default: true // 只影响代码块的深色主题
    },
    isStreaming: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      md: new MarkdownIt({
        html: true,
        xhtmlOut: true,
        breaks: true,
        langPrefix: 'language-',
        linkify: true,
        typographer: true,
        quotes: '""\'\'',

        highlight: (str, lang) => {
          // 处理行内代码
          if (str.startsWith('`') && str.endsWith('`') && str.length > 1) {
            return `<code class="inline-code">${this.md.utils.escapeHtml(str.slice(1, -1))}</code>`
          }

          // 处理代码块
          if (lang && hljs.getLanguage(lang)) {
            try {
              // 只给代码块添加深色主题类
              const darkModeClass = this.darkMode ? 'dark-code' : ''
              return `<pre class="${darkModeClass} code-block-wrapper">
                  <div class="code-header">
                    <span class="code-language">${lang}</span>
                    <span class="copy-button">复制</span>
                  </div>
                  <code class="hljs language-${lang}">${hljs.highlight(str, { language: lang }).value}</code>
                </pre>`
            } catch (e) {
              console.warn('代码高亮失败:', e)
            }
          }
          // 普通代码块也添加深色主题类
          const darkModeClass = this.darkMode ? 'dark-code' : ''
          return `<pre class="${darkModeClass} code-block-wrapper">
              <div class="code-header">
                <span class="code-language">plaintext</span>
                <span class="copy-button">复制</span>
              </div>
              <code class="hljs">${this.md.utils.escapeHtml(str)}</code>
            </pre>`
        }
      }),

      // 为思考内容创建单独的Markdown实例，避免循环引用
      thinkMd: new MarkdownIt({
        html: true,
        xhtmlOut: true,
        breaks: true,
        linkify: true,
        typographer: true,
        highlight: (str, lang) => {
          // 处理代码块
          if (lang && hljs.getLanguage(lang)) {
            try {
              // 给代码块添加深色主题类
              const darkModeClass = this.darkMode ? 'dark-code' : ''
              return `<pre class="${darkModeClass} code-block-wrapper">
                  <div class="code-header">
                    <span class="code-language">${lang}</span>
                    <span class="copy-button">复制</span>
                  </div>
                  <code class="hljs language-${lang}">${hljs.highlight(str, { language: lang }).value}</code>
                </pre>`
            } catch (e) {
              console.warn('深度思考代码高亮失败:', e)
            }
          }
          // 普通代码块
          const darkModeClass = this.darkMode ? 'dark-code' : ''
          return `<pre class="${darkModeClass} code-block-wrapper">
              <div class="code-header">
                <span class="code-language">plaintext</span>
                <span class="copy-button">复制</span>
              </div>
              <code class="hljs">${this.thinkMd.utils.escapeHtml(str)}</code>
            </pre>`
        }
      }),

      // 用于追踪当前处理状态的变量
      isInThinkingBlock: false,
      thinkingContent: '',
      processedContent: '',
      currentThinkingBlock: '',
      remainingContent: ''
    }
  },
  computed: {
    messageTypeClass() {
      return this.messageType ? `message-type-${this.messageType}` : ''
    },
    renderedContent() {
      try {
        // 直接使用Markdown渲染预处理后的内容
        return this.md.render(this.processStreamingContent(this.content))
      } catch (error) {
        console.error('Markdown 渲染错误:', error)
        return this.md.utils.escapeHtml(this.content)
      }
    }
  },
  methods: {
    // 处理流式内容的方法
    processStreamingContent(content) {
      if (!content) return ''

      // 重置处理状态
      this.isInThinkingBlock = false
      this.thinkingContent = ''
      this.processedContent = ''
      this.currentThinkingBlock = ''

      // 检查是否有未闭合的<think>标签
      let openTagIndex = content.indexOf('<think>')
      let closeTagIndex = content.indexOf('</think>')

      // 如果没有思考标签，直接返回原内容
      if (openTagIndex === -1) {
        return content
      }

      let result = ''
      let currentPosition = 0
      let thinkBlockCount = 0

      // 循环处理所有思考块
      while (openTagIndex !== -1) {
        // 添加思考块前的内容
        result += content.substring(currentPosition, openTagIndex)

        // 找到闭合标签
        closeTagIndex = content.indexOf('</think>', openTagIndex)
        thinkBlockCount++

        // 如果有闭合标签，提取完整的思考块
        if (closeTagIndex !== -1) {
          const thinkContent = content.substring(openTagIndex + 7, closeTagIndex)
          const renderedThinkContent = this.thinkMd.render(thinkContent)

          // 计算思考用时 (模拟，实际项目中可能需要后端提供)
          const thinkingTime = Math.floor(Math.random() * 5) + 1 // 1-5秒，仅用于演示

          // 添加深度思考样式
          result += `
  <div class="deep-thinking-container" id="think-block-${thinkBlockCount}">
    <div class="deep-thinking-header">
      <div class="deep-thinking-left">
        <i class="ri-brain-fill deep-thinking-icon"></i>
        <span class="deep-thinking-title">深度思考</span>
        <span class="thinking-time">${thinkingTime}s</span>
      </div>
      <div class="deep-thinking-right">
        <span class="collapse-btn" data-target="think-content-${thinkBlockCount}">
          <i class="ri-arrow-up-s-line collapse-icon"></i>
        </span>
      </div>
    </div>
    <div class="deep-thinking-content" id="think-content-${thinkBlockCount}">${renderedThinkContent}</div>
  </div>`

          // 更新当前位置到闭合标签之后
          currentPosition = closeTagIndex + 8 // 8 是 </think> 的长度

          // 寻找下一个开始标签
          openTagIndex = content.indexOf('<think>', currentPosition)
        } else {
          // 没有闭合标签，说明是流式输出中的未完成思考块
          // 将剩余内容放在思考块内
          const thinkContent = content.substring(openTagIndex + 7)
          const renderedThinkContent = this.thinkMd.render(thinkContent)

          // 添加深度思考样式（未闭合状态）
          result += `
  <div class="deep-thinking-container thinking-in-progress" id="think-block-${thinkBlockCount}">
    <div class="deep-thinking-header">
      <div class="deep-thinking-left">
        <i class="ri-brain-fill deep-thinking-icon"></i>
        <span class="deep-thinking-title">深度思考</span>
        <span class="thinking-status">思考中</span>
      </div>
      <div class="deep-thinking-right">
        <span class="collapse-btn" data-target="think-content-${thinkBlockCount}">
          <i class="ri-arrow-up-s-line collapse-icon"></i>
        </span>
      </div>
    </div>
    <div class="deep-thinking-content" id="think-content-${thinkBlockCount}">${renderedThinkContent}</div>
  </div>`

          // 设置当前位置到内容末尾
          currentPosition = content.length
          break
        }
      }

      // 添加剩余内容
      if (currentPosition < content.length) {
        result += content.substring(currentPosition)
      }

      return result
    },

    // 处理深度思考块的折叠/展开
    setupThinkingBlocksToggle() {
      const collapseButtons = document.querySelectorAll('.collapse-btn')
      collapseButtons.forEach(btn => {
        btn.addEventListener('click', () => {
          const targetId = btn.getAttribute('data-target')
          const contentElement = document.getElementById(targetId)
          const iconElement = btn.querySelector('.collapse-icon')

          if (contentElement && iconElement) {
            contentElement.classList.toggle('collapsed')
            iconElement.classList.toggle('rotated')
          }
        })
      })
    },

    // 处理代码块复制功能
    setupCodeCopyButtons() {
      const copyButtons = document.querySelectorAll('.copy-button')
      copyButtons.forEach(button => {
        // 移除旧的事件监听器，避免重复绑定
        button.removeEventListener('click', this.handleCopyButtonClick)
        // 添加新的事件监听器
        button.addEventListener('click', this.handleCopyButtonClick)
      })
    },

    // 处理复制按钮点击事件
    handleCopyButtonClick(e) {
      const codeBlock = e.target.closest('.code-block-wrapper').querySelector('code')
      if (codeBlock) {
        const text = codeBlock.textContent
        navigator.clipboard.writeText(text).then(
          () => {
            e.target.textContent = '已复制'
            setTimeout(() => {
              e.target.textContent = '复制'
            }, 2000)
          },
          err => {
            console.error('复制失败:', err)
          }
        )
      }
    }
  },
  mounted() {
    // 添加复制功能的事件监听
    this.$nextTick(() => {
      // 设置代码复制按钮事件
      this.setupCodeCopyButtons()

      // 设置深度思考块的折叠/展开功能
      this.setupThinkingBlocksToggle()
    })
  },
  updated() {
    // 在内容更新后，重新设置事件监听
    this.$nextTick(() => {
      // 重新设置代码复制按钮事件
      this.setupCodeCopyButtons()

      // 重新设置折叠/展开功能
      this.setupThinkingBlocksToggle()
    })
  }
}
</script>

<style>
/* 深度思考样式 */
.deep-thinking-container {
  background-color: #f5f5f5;
  border-radius: 8px;
  margin: 15px 0;
  border-left: 3px solid #909399;
  overflow: hidden;
  font-family: monospace;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 流式思考中的特殊样式 */
.thinking-in-progress {
  border-left: 3px solid #409eff;
  position: relative;
}

.thinking-in-progress:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, transparent, #409eff, transparent);
  animation: pulsate 1.5s infinite;
}

@keyframes pulsate {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}

.thinking-in-progress .deep-thinking-header {
  background-color: #e6f1ff;
  border-bottom: 1px solid #c0d8ff;
}

/* 思考中状态 */
.thinking-status {
  font-size: 12px;
  color: #409eff;
  margin-left: 6px;
  font-weight: normal;
  display: inline-block;
  animation: blink 1s infinite;
}

@keyframes blink {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* 深度思考头部样式 */
.deep-thinking-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #e0e0e0;
  border-bottom: 1px solid #d0d0d0;
}

.deep-thinking-left {
  display: flex;
  align-items: center;
}

.deep-thinking-right {
  display: flex;
  align-items: center;
}

/* 思考图标样式 */
.deep-thinking-icon {
  font-size: 16px;
  margin-right: 8px;
  color: #606266;
}

.deep-thinking-title {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

/* 思考时间样式 */
.thinking-time {
  font-size: 14px;
  color: #909399;
  margin-left: 8px;
  font-weight: normal;
}

/* 折叠/展开图标 */
.collapse-icon {
  cursor: pointer;
  font-size: 18px;
  color: #909399;
  transition: transform 0.3s ease;
}

.collapse-icon.rotated {
  transform: rotate(180deg);
}

/* 深度思考内容 */
.deep-thinking-content {
  padding: 12px 15px 12px 15px;
  color: #333;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  overflow-x: auto;
  max-height: 500px;
  transition:
    max-height 0.3s ease,
    padding 0.3s ease;
}

.deep-thinking-content.collapsed {
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  overflow: hidden;
}

/* 确保深度思考不受其他样式影响 */
.deep-thinking-content p {
  margin: 8px 0 !important;
  color: #333 !important;
  font-family: 'Courier New', monospace !important;
}

.deep-thinking-content code {
  background-color: #e8e8e8 !important;
  padding: 2px 5px !important;
  border-radius: 3px !important;
  font-family: monospace !important;
}

/* 确保深度思考不受其他样式影响 */
.deep-thinking-content p {
  margin: 0 !important;
  padding: 0 !important;
  color: #3a3a3a !important;
}

.deep-thinking {
  margin: 15px 0;
  border-radius: 8px;
  background-color: #f8f8f8;
  border-left: 4px solid #909399;
}

/* 代码块顶部横幅样式 */
.markdown-body .code-block-wrapper {
  position: relative;
  /* margin-top: 16px; */
}

.markdown-body .code-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 34px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  background-color: #2d3748;
  color: #fff;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom: 1px solid #3f4758;
  font-size: 14px;
}

.markdown-body .code-language {
  font-size: 0.9em;
  font-weight: 500;
  text-transform: lowercase;
}

.markdown-body .copy-button {
  cursor: pointer;
  font-size: 0.9em;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #3f4758;
  transition: all 0.2s ease;
}

.markdown-body .copy-button:hover {
  background-color: #4a5568;
}

/* 暗黑样式 */
.markdown-body pre.dark-code {
  background-color: #282c34;
  color: #abb2bf;
  border: 1px solid #181a1f;
}

.markdown-body pre.dark-code .code-header {
  background-color: #404045;
  border-bottom: 1px solid #181a1f;
}

.markdown-body pre.dark-code .copy-button {
  background-color: #404045;
}

.markdown-body pre.dark-code .copy-button:hover {
  background-color: #383e4a;
}

.markdown-body pre.dark-code code.hljs {
  padding: 5px;
  font-size: 13px;
  padding: 0;
  background: transparent;
  color: #ffffff;
}

.markdown-body pre.dark-code .hljs-keyword {
  color: #569cd6;
}

.markdown-body pre.dark-code .hljs-comment,
.markdown-body pre.dark-code .hljs-quote {
  color: #636f88;
}

.markdown-body pre.dark-code .hljs-variable {
  color: #9cdcfe;
}
</style>

<style scoped>
/* 移除之前深度思考相关样式，因为已经移至全局样式 */

.markdown-body {
  padding: 5px;
  color: #12151a;
  border-radius: 8px;
  word-break: break-all;
  font-size: 14.5px;
  text-align: left;
  line-height: 1.8;
}

/* 标题样式 */
.markdown-body ::v-deep h1,
.markdown-body ::v-deep h2,
.markdown-body ::v-deep h3,
.markdown-body ::v-deep h4,
.markdown-body ::v-deep h5,
.markdown-body ::v-deep h6 {
  margin-top: 1.14em;
  font-weight: 600;
  line-height: 1.4;
  color: #1a202c;
  position: relative;
}

.markdown-body ::v-deep h1 {
  font-size: 1.5em;
}

.markdown-body ::v-deep h3 {
  font-size: 1.1em;
}

/* 段落和链接 */
.markdown-body ::v-deep p {
  line-height: 1.8;
  color: #2d3748;
}

.markdown-body ::v-deep a {
  transition: all 0.2s ease;
  background-color: transparent;
  text-decoration: none;
}

.message-content-agent ::v-deep a {
  color: #1783ff !important;
}

.markdown-body ::v-deep a:hover {
  color: #2b6cb0;
  border-bottom-color: #2b6cb0;
}

/* 基于消息类型的样式 */
.message-type-system {
  font-weight: 500;
  color: #64748b;
}

.message-type-user ::v-deep p {
  color: #ffffff;
}

.message-type-user ::v-deep a {
  color: #ffffff;
}

.message-type-agent ::v-deep a {
  color: #1783ff !important;
}

.markdown-body ::v-deep hr {
  box-sizing: content-box;
  overflow: hidden;
  background: transparent;
  padding: 0;
  border: 0;
}

/* 代码块样式 */
.markdown-body ::v-deep pre {
  border-radius: 8px;
  padding: 15px !important;
  overflow: auto;
  position: relative;
}

.markdown-body ::v-deep code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: rgba(0, 0, 0, 0.04);
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-size: 0.9em;
  color: #2d3748;
}

.markdown-body ::v-deep pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  font-size: 0.95em;
  line-height: 1.6;
}

/* 引用块样式 */
.markdown-body ::v-deep blockquote {
  margin: 24px 0;
  padding: 16px 24px;
  color: #4a5568;
  border-left: 4px solid #4299e1;
  background-color: #f7fafc;
  border-radius: 0 8px 8px 0;
  font-style: italic;
}

/* 列表样式 */
.markdown-body ::v-deep ul,
.markdown-body ::v-deep ol {
  padding-left: 2em;
  margin: 5px 0;
}

.markdown-body ::v-deep li {
  line-height: 1.8;
  letter-spacing: 0.25px;
}

/* 表格样式 */
.markdown-body ::v-deep table {
  width: 100%;
  margin: 24px 0;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.markdown-body ::v-deep th {
  background-color: #f7fafc;
  font-weight: 600;
  padding: 12px 16px;
  border-bottom: 2px solid #e2e8f0;
  color: #2d3748;
}

.markdown-body ::v-deep td {
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  color: #4a5568;
}

.markdown-body ::v-deep tr:last-child td {
  border-bottom: none;
}

.markdown-body ::v-deep tr:hover {
  background-color: #f7fafc;
}

/* 图片样式 */
.markdown-body ::v-deep img {
  max-width: 100%;
  border-radius: 8px;
  margin: 24px 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 流式输出动画 */
.markdown-body.streaming-content::after {
  content: '';
  display: inline-block;
  vertical-align: middle;
  width: 2px;
  height: 18px;
  margin-left: 4px;
  background: linear-gradient(to bottom, #4299e1, #63b3ed);
  border-radius: 1px;
  animation: streamingCursor 1.2s infinite;
  box-shadow: 0 0 6px rgba(66, 153, 225, 0.4);
}

@keyframes streamingCursor {
  0%,
  50% {
    opacity: 1;
    transform: scaleY(1);
  }
  51%,
  100% {
    opacity: 0.3;
    transform: scaleY(0.8);
  }
}

/* 流式内容动画效果 */
.markdown-body.streaming-content {
  position: relative;
}

.markdown-body.streaming-content p:last-child,
.markdown-body.streaming-content div:last-child {
  position: relative;
}

/* 流式内容渐入动画 */
.streaming-content p,
.streaming-content div,
.streaming-content h1,
.streaming-content h2,
.streaming-content h3,
.streaming-content h4,
.streaming-content h5,
.streaming-content h6,
.streaming-content ul,
.streaming-content ol,
.streaming-content blockquote {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
