/**
 * 增强的元素包装器
 */
class EnhancedElement {
  constructor(locator) {
    this.locator = locator
  }

  /**
   * 点击元素直到元素不存在
   * @param {Object} options - 配置选项
   * @param {number} [options.timeout=20000] - 超时时间（毫秒），默认 20000 毫秒
   * @param {number} [options.interval=1000] - 点击间隔时间（毫秒），默认 1000 毫秒
   * @param {number} [options.waitTimeout=2000] - 等待元素出现的超时时间（毫秒），默认 2000 毫秒
   * @returns {Promise<boolean>} - 返回是否成功完成操作
   */
  async clickUntilDisappears(options = {}) {
    const { timeout = 20000, interval = 1000, waitTimeout = 2000 } = options

    const startTime = Date.now()

    try {
      // 1. 先等待元素存在并可见
      console.log('等待元素出现...')
      await this.locator.waitFor({
        state: 'visible',
        timeout: waitTimeout
      })
      console.log('元素已出现，开始点击操作')

      // 2. 循环点击直到元素不存在或超时
      let clickCount = 0
      while (Date.now() - startTime < timeout) {
        try {
          // 检查元素是否仍然可见
          const isVisible = await this.locator.isVisible()

          if (!isVisible) {
            console.log(`元素已消失，点击次数: ${clickCount}`)
            return true // 成功：元素已消失
          }

          // 点击元素
          await this.locator.click()
          clickCount++
          console.log(`第 ${clickCount} 次点击元素`)

          // 等待指定间隔时间
          await this.locator.page().waitForTimeout(interval)
        } catch (error) {
          // 如果点击过程中元素消失或不可点击，认为操作成功
          console.log('元素在点击过程中消失，操作成功')
          return true
        }
      }

      // 超时仍未消失
      const elapsedTime = Date.now() - startTime
      console.warn(`超时: 元素在 ${elapsedTime}ms 后仍然存在`)
      return false
    } catch (error) {
      // 元素未出现
      if (error.message.includes('Timeout')) {
        console.log('元素未在指定时间内出现，可能已经不存在')
        return true // 元素不存在也算成功
      }

      console.error('操作过程中发生错误:', error.message)
      throw error
    }
  }

  /**
   * 增强的点击方法 - 等待元素可见后点击
   * @param {Object} options - 配置选项
   * @param {number} [options.timeout=5000] - 等待超时时间（毫秒）
   * @param {boolean} [options.force=false] - 是否强制点击
   * @returns {Promise<EnhancedElement>} - 返回自身支持链式调用
   */
  async click(options = {}) {
    const { timeout = 5000, force = false } = options

    try {
      await this.locator.waitFor({ state: 'visible', timeout })
      await this.locator.click({ force })
      console.log('元素点击成功')
    } catch (error) {
      console.warn('点击失败:', error.message)
      throw error
    }

    return this
  }

  /**
   * 获取原始 locator 对象
   * @returns {import('@playwright/test').Locator} - 原始 Playwright 元素定位器
   */
  raw() {
    return this.locator
  }

  /**
   * 在指定时间内如果元素存在就点击一次
   * @param {Object} options - 配置选项
   * @param {number} [options.timeout=5000] - 等待时间（毫秒），默认 5000 毫秒
   * @returns {Promise<boolean>} - 返回是否成功点击（true: 点击了, false: 元素不存在）
   */
  async clickIfExists(options = {}) {
    const { timeout = 5000 } = options

    try {
      // 等待元素存在并可见
      console.log(`等待元素出现，超时时间: ${timeout}ms`)
      await this.locator.waitFor({
        state: 'visible',
        timeout: timeout
      })

      // 元素存在，执行点击
      await this.locator.click()
      console.log('元素存在，点击成功')
      return true
    } catch (error) {
      // 元素不存在或超时，正常结束
      if (error.message.includes('Timeout')) {
        console.log(`${timeout}ms 内元素未出现，正常结束`)
        return false
      }

      // 其他错误也正常结束，不抛出异常
      console.log('元素点击过程中发生错误，正常结束:', error.message)
      return false
    }
  }
}

/**
 * 元素工具函数
 * @param {import('@playwright/test').Locator} locator - Playwright 元素定位器
 * @returns {EnhancedElement} - 增强的元素对象
 */
function elementUtils(locator) {
  return new EnhancedElement(locator)
}

/**
 * 元素操作工具类（保留原有API）
 */
class ElementUtils {
  /**
   * 点击元素直到元素不存在
   * @param {import('@playwright/test').Locator} locator - Playwright 元素定位器
   * @param {Object} options - 配置选项
   * @returns {Promise<boolean>} - 返回是否成功完成操作
   */
  static async clickUntilElementDisappears(locator, options = {}) {
    return elementUtils(locator).clickUntilDisappears(options)
  }
}

// 为了向后兼容，保留原始函数
async function clickUntilElementDisappears(locator, timeout = 5000) {
  return ElementUtils.clickUntilElementDisappears(locator, { timeout })
}

// ES6 模块导出
export { elementUtils }

// CommonJS 导出
module.exports = { elementUtils }

// 浏览器环境支持
if (typeof window !== 'undefined') {
  window.elementUtils = elementUtils
}
