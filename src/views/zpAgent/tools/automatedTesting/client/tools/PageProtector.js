/**
 * 页面保护工具类
 * 用于阻止页面意外关闭、跳转和刷新，确保测试的稳定性
 */
class PageProtector {
  constructor(page) {
    this.page = page
    this.isProtected = false
    this.eventListeners = []
    this.originalUrl = null
    this.protectionConfig = {
      blockNavigation: true, // 阻止页面导航
      blockRefresh: true, // 阻止页面刷新
      blockPopups: true, // 阻止弹窗
      blockFormSubmit: false, // 是否阻止表单提交（默认不阻止）
      logEvents: true, // 是否记录事件日志
      autoRecover: true, // 是否自动恢复页面
      recoveryData: null // 页面恢复数据
    }
  }

  /**
   * 启用页面保护
   * @param {Object} config - 保护配置选项
   * @returns {Promise<void>}
   */
  async enable(config = {}) {
    if (this.isProtected) {
      console.log('[PageProtector] 页面保护已启用，跳过重复启用')
      return
    }

    // 合并配置
    this.protectionConfig = { ...this.protectionConfig, ...config }
    this.originalUrl = this.page.url()

    console.log('[PageProtector] 启用页面保护，配置:', this.protectionConfig)

    // 添加页面事件监听
    await this._setupEventListeners()

    // 注入页面保护脚本
    await this._injectProtectionScript()

    this.isProtected = true
    console.log('[PageProtector] 页面保护已启用')
  }

  /**
   * 禁用页面保护
   * @returns {Promise<void>}
   */
  async disable() {
    if (!this.isProtected) {
      console.log('[PageProtector] 页面保护未启用，跳过禁用')
      return
    }

    console.log('[PageProtector] 禁用页面保护')

    // 移除事件监听器
    this._removeEventListeners()

    // 移除页面保护脚本
    await this._removeProtectionScript()

    this.isProtected = false
    console.log('[PageProtector] 页面保护已禁用')
  }

  /**
   * 设置页面恢复数据
   * @param {Object} recoveryData - 恢复数据
   */
  setRecoveryData(recoveryData) {
    this.protectionConfig.recoveryData = recoveryData
    console.log('[PageProtector] 设置页面恢复数据:', recoveryData)
  }

  /**
   * 检查页面是否需要恢复
   * @returns {Promise<boolean>}
   */
  async checkAndRecover() {
    if (!this.protectionConfig.autoRecover) {
      return false
    }

    try {
      const currentUrl = this.page.url()

      // 检查页面是否已关闭
      if (this.page.isClosed()) {
        console.log('[PageProtector] 检测到页面已关闭，无法恢复')
        return false
      }

      // 检查URL是否发生了意外变化
      if (this.originalUrl && !currentUrl.includes(this._extractDomain(this.originalUrl))) {
        console.log(`[PageProtector] 检测到页面URL异常变化: ${this.originalUrl} -> ${currentUrl}`)
        return await this._recoverPage()
      }

      return true
    } catch (error) {
      console.error('[PageProtector] 页面检查过程中出错:', error.message)
      return false
    }
  }

  /**
   * 手动恢复页面
   * @returns {Promise<boolean>}
   */
  async recoverPage() {
    return await this._recoverPage()
  }

  /**
   * 设置页面事件监听器
   * @private
   */
  async _setupEventListeners() {
    // 监听页面关闭事件
    const closeHandler = () => {
      if (this.protectionConfig.logEvents) {
        console.log('[PageProtector] 检测到页面关闭事件')
      }
    }
    this.page.on('close', closeHandler)
    this.eventListeners.push({ event: 'close', handler: closeHandler })

    // 监听页面导航事件
    const navigatedHandler = frame => {
      if (frame === this.page.mainFrame()) {
        if (this.protectionConfig.logEvents) {
          console.log(`[PageProtector] 页面导航到: ${frame.url()}`)
        }

        // 检查是否需要阻止导航
        if (this.protectionConfig.blockNavigation && this.originalUrl) {
          const currentDomain = this._extractDomain(frame.url())
          const originalDomain = this._extractDomain(this.originalUrl)

          if (currentDomain !== originalDomain) {
            console.log(`[PageProtector] 检测到跨域导航，将尝试恢复: ${originalDomain} -> ${currentDomain}`)
            // 延迟恢复，避免立即执行导致冲突
            setTimeout(() => this._recoverPage(), 1000)
          }
        }
      }
    }
    this.page.on('framenavigated', navigatedHandler)
    this.eventListeners.push({ event: 'framenavigated', handler: navigatedHandler })

    // 监听页面错误
    const errorHandler = error => {
      if (this.protectionConfig.logEvents) {
        console.log(`[PageProtector] 页面错误: ${error.message}`)
      }
    }
    this.page.on('pageerror', errorHandler)
    this.eventListeners.push({ event: 'pageerror', handler: errorHandler })
  }

  /**
   * 注入页面保护脚本
   * @private
   */
  async _injectProtectionScript() {
    const protectionScript = `
      (function() {
        console.log('[PageProtector] 注入页面保护脚本');
        
        // 保护配置
        const config = ${JSON.stringify(this.protectionConfig)};
        
        // 阻止页面刷新和关闭
        if (config.blockRefresh) {
          window.addEventListener('beforeunload', function(e) {
            console.log('[PageProtector] 阻止页面卸载');
            e.preventDefault();
            e.returnValue = '确定要离开此页面吗？测试正在进行中...';
            return e.returnValue;
          });
        }
        
        // 阻止某些可能导致页面跳转的默认行为
        if (config.blockNavigation) {
          document.addEventListener('click', function(e) {
            const target = e.target;
            if (target && (target.tagName === 'A' || target.closest('a'))) {
              const link = target.tagName === 'A' ? target : target.closest('a');
              const href = link.href;
              
              if (href && !href.includes('javascript:') && href !== '#' && href !== window.location.href) {
                if (config.logEvents) {
                  console.log('[PageProtector] 检测到链接点击，href:', href);
                }
                
                // 检查是否是外部链接
                const currentDomain = window.location.hostname;
                const linkDomain = new URL(href).hostname;
                
                if (linkDomain !== currentDomain) {
                  console.log('[PageProtector] 阻止外部链接跳转:', href);
                  e.preventDefault();
                  e.stopPropagation();
                  return false;
                }
              }
            }
          }, true);
        }
        
        // 阻止表单提交（可选）
        if (config.blockFormSubmit) {
          document.addEventListener('submit', function(e) {
            console.log('[PageProtector] 阻止表单提交');
            e.preventDefault();
            return false;
          }, true);
        }
        
        // 阻止弹窗
        if (config.blockPopups) {
          const originalOpen = window.open;
          window.open = function() {
            console.log('[PageProtector] 阻止弹窗打开');
            return null;
          };
        }
        
        // 标记页面保护已激活
        window.__pageProtectorActive = true;
        console.log('[PageProtector] 页面保护脚本已激活');
      })();
    `

    await this.page.addInitScript(protectionScript)
  }

  /**
   * 移除页面保护脚本
   * @private
   */
  async _removeProtectionScript() {
    try {
      await this.page.evaluate(() => {
        if (window.__pageProtectorActive) {
          console.log('[PageProtector] 移除页面保护脚本')
          window.__pageProtectorActive = false

          // 移除beforeunload事件监听器
          window.removeEventListener('beforeunload')
        }
      })
    } catch (error) {
      console.log('[PageProtector] 移除保护脚本时出错:', error.message)
    }
  }

  /**
   * 移除事件监听器
   * @private
   */
  _removeEventListeners() {
    this.eventListeners.forEach(({ event, handler }) => {
      this.page.off(event, handler)
    })
    this.eventListeners = []
  }

  /**
   * 恢复页面
   * @private
   */
  async _recoverPage() {
    if (!this.originalUrl) {
      console.log('[PageProtector] 没有原始URL，无法恢复页面')
      return false
    }

    try {
      console.log(`[PageProtector] 尝试恢复页面到: ${this.originalUrl}`)

      // 导航回原始页面
      await this.page.goto(this.originalUrl, { waitUntil: 'networkidle' })

      // 如果有恢复数据，执行恢复操作
      if (this.protectionConfig.recoveryData) {
        await this._executeRecoveryActions(this.protectionConfig.recoveryData)
      }

      // 重新注入保护脚本
      await this._injectProtectionScript()

      console.log('[PageProtector] 页面恢复成功')
      return true
    } catch (error) {
      console.error('[PageProtector] 页面恢复失败:', error.message)
      return false
    }
  }

  /**
   * 执行恢复操作
   * @private
   */
  async _executeRecoveryActions(recoveryData) {
    if (!recoveryData) return

    try {
      console.log('[PageProtector] 执行页面恢复操作')

      // 恢复cookies
      if (recoveryData.cookies) {
        await this.page.context().addCookies(recoveryData.cookies)
      }

      // 执行恢复步骤
      if (recoveryData.actions && Array.isArray(recoveryData.actions)) {
        for (const action of recoveryData.actions) {
          await this._executeAction(action)
        }
      }

      console.log('[PageProtector] 页面恢复操作完成')
    } catch (error) {
      console.error('[PageProtector] 执行恢复操作时出错:', error.message)
    }
  }

  /**
   * 执行单个恢复动作
   * @private
   */
  async _executeAction(action) {
    const { type, selector, text, timeout = 5000 } = action

    try {
      switch (type) {
        case 'click':
          if (selector) {
            await this.page.locator(selector).click({ timeout })
          } else if (text) {
            await this.page.getByText(text).click({ timeout })
          }
          break

        case 'fill':
          if (selector && action.value) {
            await this.page.locator(selector).fill(action.value, { timeout })
          }
          break

        case 'wait':
          if (action.duration) {
            await this.page.waitForTimeout(action.duration)
          } else if (selector) {
            await this.page.waitForSelector(selector, { timeout })
          }
          break

        default:
          console.log(`[PageProtector] 未知的恢复动作类型: ${type}`)
      }
    } catch (error) {
      console.log(`[PageProtector] 执行恢复动作失败 (${type}):`, error.message)
    }
  }

  /**
   * 提取域名
   * @private
   */
  _extractDomain(url) {
    try {
      return new URL(url).hostname
    } catch {
      return url
    }
  }

  /**
   * 获取保护状态
   */
  isEnabled() {
    return this.isProtected
  }

  /**
   * 获取当前配置
   */
  getConfig() {
    return { ...this.protectionConfig }
  }
}

/**
 * 创建页面保护器
 * @param {import('@playwright/test').Page} page - Playwright页面对象
 * @returns {PageProtector} - 页面保护器实例
 */
function createPageProtector(page) {
  console.log('[PageProtector] 创建页面保护器实例')
  return new PageProtector(page)
}

module.exports = { PageProtector, createPageProtector }
