const { chromium } = require('playwright')

/**
 * iframe DOM提取器 - 专门处理主页面和iframe内容的获取
 * 使用多策略组合获取方式，返回分层DOM结构
 */
class IframeDomExtractor {
  constructor() {
    this.browser = null
    this.context = null
  }

  /**
   * 初始化浏览器环境
   */
  async init(options = {}) {
    this.browser = await chromium.launch({
      headless: options.headless !== false,
      devtools: options.devtools || false,
      ...options.browserOptions
    })

    this.context = await this.browser.newContext({
      ...options.contextOptions
    })
  }

  /**
   * 清理浏览器环境
   */
  async cleanup() {
    if (this.context) {
      await this.context.close()
    }
    if (this.browser) {
      await this.browser.close()
    }
  }

  /**
   * 获取页面完整DOM结构（包括所有iframe）
   * @param {string} url - 目标页面URL
   * @param {Array} cookies - Cookie数组
   * @param {Object} options - 配置选项
   * @returns {Object} 分层DOM结构
   */
  async extractFullDomStructure(url, cookies = [], options = {}) {
    if (!this.browser) {
      await this.init(options)
    }

    const page = await this.context.newPage()

    try {
      // 设置Cookie
      if (cookies && cookies.length > 0) {
        const cookiesWithDefaults = cookies.map(cookie => ({
          name: cookie.name,
          value: cookie.value,
          domain: cookie.domain || new URL(url).hostname,
          path: cookie.path || '/',
          ...cookie
        }))

        await this.context.addCookies(cookiesWithDefaults)
        console.log('✅ Cookie设置成功')
      }

      // 访问页面
      console.log(`🌐 正在访问: ${url}`)
      await page.goto(url, {
        waitUntil: options.waitUntil || 'networkidle',
        timeout: options.timeout || 30000
      })

      // 等待页面加载完成
      await page.waitForTimeout(options.pageLoadDelay || 2000)

      // 获取主页面信息
      const mainPageData = await this.getMainPageData(page, url)

      // 获取所有iframe信息
      const iframesData = await this.getAllIframesData(page, url, options)

      // 返回分层结构
      const result = {
        mainPage: mainPageData,
        iframes: iframesData,
        summary: {
          totalIframes: iframesData.length,
          successfulIframes: iframesData.filter(iframe => iframe.dom && !iframe.error).length,
          failedIframes: iframesData.filter(iframe => iframe.error).length,
          timestamp: new Date().toISOString()
        }
      }

      console.log(`\n📊 DOM提取完成:`)
      console.log(`- 主页面DOM长度: ${mainPageData.dom.length} 字符`)
      console.log(`- 发现iframe数量: ${result.summary.totalIframes}`)
      console.log(`- 成功获取iframe: ${result.summary.successfulIframes}`)
      console.log(`- 失败iframe数量: ${result.summary.failedIframes}`)

      return result
    } catch (error) {
      console.error('❌ DOM提取过程中出错:', error)
      throw error
    } finally {
      await page.close()
    }
  }

  /**
   * 获取主页面数据
   */
  async getMainPageData(page, url) {
    const dom = await page.content()
    const title = await page.title()
    const allElements = await page.locator('*').count()
    const links = await page.locator('a').count()
    const images = await page.locator('img').count()
    const scripts = await page.locator('script').count()

    return {
      url: url,
      title: title,
      dom: dom,
      stats: {
        totalElements: allElements,
        links: links,
        images: images,
        scripts: scripts,
        domSize: dom.length
      }
    }
  }

  /**
   * 获取所有iframe数据
   */
  async getAllIframesData(page, baseUrl, options = {}) {
    const iframes = await page.locator('iframe').all()
    const iframesData = []

    console.log(`\n🖼️  发现 ${iframes.length} 个iframe`)

    // 并行处理所有iframe
    const iframePromises = iframes.map(async (iframe, index) => {
      return await this.getIframeData(iframe, index, baseUrl, options)
    })

    const results = await Promise.allSettled(iframePromises)

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        iframesData.push(result.value)
      } else {
        iframesData.push({
          index: index,
          error: `获取iframe失败: ${result.reason.message}`,
          type: 'unknown'
        })
      }
    })

    return iframesData
  }

  /**
   * 获取单个iframe数据 - 多策略获取
   */
  async getIframeData(iframe, index, baseUrl, options = {}) {
    console.log(`\n--- 处理第 ${index + 1} 个iframe ---`)

    // 获取iframe基本信息
    const iframeInfo = await this.getIframeBasicInfo(iframe, index)

    // 策略1: 同域iframe直接获取
    let domResult = await this.getIframeDomDirect(iframe, index)

    // 策略2: 如果直接获取失败，尝试通过URL获取
    if (!domResult.success && iframeInfo.src) {
      domResult = await this.getIframeDomByUrl(iframeInfo.src, baseUrl, options)
    }

    // 策略3: 如果还是失败，尝试内联内容
    if (!domResult.success) {
      domResult = await this.getIframeInlineContent(iframe)
    }

    return {
      ...iframeInfo,
      dom: domResult.dom,
      error: domResult.error,
      method: domResult.method,
      stats: domResult.stats
    }
  }

  /**
   * 获取iframe基本信息
   */
  async getIframeBasicInfo(iframe, index) {
    const src = await iframe.getAttribute('src')
    const id = await iframe.getAttribute('id')
    const name = await iframe.getAttribute('name')
    const srcdoc = await iframe.getAttribute('srcdoc')

    // 判断iframe类型
    let type = 'unknown'
    if (srcdoc) {
      type = 'inline'
    } else if (src) {
      if (src.startsWith('http')) {
        type = 'cross-origin'
      } else {
        type = 'same-origin'
      }
    }

    return {
      index: index,
      id: id,
      name: name,
      src: src,
      srcdoc: srcdoc,
      type: type
    }
  }

  /**
   * 策略1: 直接获取iframe内容
   */
  async getIframeDomDirect(iframe, index) {
    try {
      const frame = await iframe.contentFrame()
      if (!frame) {
        return {
          success: false,
          error: '无法获取iframe frame对象',
          method: 'direct'
        }
      }

      // 等待iframe内容加载
      await frame.waitForLoadState('networkidle', { timeout: 10000 })

      // 获取DOM内容
      const dom = await frame.content()
      const title = await frame.title()
      const elementCount = await frame.locator('*').count()

      console.log(`✅ 直接获取第${index + 1}个iframe成功，DOM长度: ${dom.length}`)

      return {
        success: true,
        dom: dom,
        method: 'direct',
        stats: {
          title: title,
          elements: elementCount,
          domSize: dom.length
        }
      }
    } catch (error) {
      console.log(`❌ 直接获取第${index + 1}个iframe失败: ${error.message}`)
      return {
        success: false,
        error: error.message,
        method: 'direct'
      }
    }
  }

  /**
   * 策略2: 通过URL获取iframe内容（域名+路径方式）
   */
  async getIframeDomByUrl(src, baseUrl, options = {}) {
    try {
      // 处理相对路径，拼接完整URL
      const fullUrl = this.resolveUrl(src, baseUrl)

      console.log(`🔄 尝试通过URL获取iframe内容: ${fullUrl}`)

      const newPage = await this.context.newPage()
      await newPage.goto(fullUrl, {
        waitUntil: options.waitUntil || 'networkidle',
        timeout: options.timeout || 30000
      })

      // 等待页面加载
      await newPage.waitForTimeout(options.pageLoadDelay || 2000)

      const dom = await newPage.content()
      const title = await newPage.title()
      const elementCount = await newPage.locator('*').count()

      await newPage.close()

      console.log(`✅ 通过URL获取iframe成功，DOM长度: ${dom.length}`)

      return {
        success: true,
        dom: dom,
        method: 'url',
        stats: {
          title: title,
          elements: elementCount,
          domSize: dom.length,
          resolvedUrl: fullUrl
        }
      }
    } catch (error) {
      console.log(`❌ 通过URL获取iframe失败: ${error.message}`)
      return {
        success: false,
        error: error.message,
        method: 'url'
      }
    }
  }

  /**
   * 策略3: 获取内联iframe内容
   */
  async getIframeInlineContent(iframe) {
    try {
      const srcdoc = await iframe.getAttribute('srcdoc')
      if (srcdoc) {
        console.log(`✅ 获取内联iframe内容成功，长度: ${srcdoc.length}`)
        return {
          success: true,
          dom: srcdoc,
          method: 'inline',
          stats: {
            domSize: srcdoc.length
          }
        }
      } else {
        return {
          success: false,
          error: '无内联内容',
          method: 'inline'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        method: 'inline'
      }
    }
  }

  /**
   * 解析URL - 处理相对路径（域名+路径方式）
   */
  resolveUrl(url, baseUrl) {
    if (url.startsWith('http')) {
      return url
    }

    const base = new URL(baseUrl)

    if (url.startsWith('/')) {
      // 绝对路径：域名 + 路径
      return `${base.protocol}//${base.host}${url}`
    } else {
      // 相对路径：域名 + 当前路径 + 相对路径
      return `${base.protocol}//${base.host}/${url}`
    }
  }
}

/**
 * 便捷函数 - 快速获取页面完整DOM结构
 */
async function extractPageDomStructure(url, cookies = [], options = {}) {
  const extractor = new IframeDomExtractor()

  try {
    await extractor.init(options)
    const result = await extractor.extractFullDomStructure(url, cookies, options)
    return result
  } finally {
    await extractor.cleanup()
  }
}

module.exports = {
  IframeDomExtractor,
  extractPageDomStructure
}
