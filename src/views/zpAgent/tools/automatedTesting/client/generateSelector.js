/**
 * Playwright智能选择器生成器 - 性能优化版本
 * 符合Playwright元素定位最佳实践
 * 优先级: id > name > data-testid > ARIA角色属性 > 简化版text > class > xpath
 */

class PlaywrightSelectorGenerator {
  constructor() {
    // 简化的ARIA角色列表，只保留最常用的
    this.commonAriaRoles = ['button', 'link', 'textbox', 'checkbox', 'radio', 'combobox', 'listbox', 'option', 'tab', 'tabpanel', 'dialog', 'alert', 'menu', 'menuitem', 'navigation', 'main', 'banner', 'contentinfo', 'search', 'form']

    // 缓存DOM查询结果，避免重复查询
    this.queryCache = new Map()
  }

  /**
   * 生成智能选择器 - 性能优化版本
   * @param {Element} element - DOM元素
   * @param {Document} document - 整个页面DOM
   * @returns {Object} 包含定位信息的对象
   */
  generateSelector(element, document) {
    // 清空缓存（每次生成选择器时重置）
    this.queryCache.clear()

    // 快速策略 - 按优先级顺序，找到第一个有效选择器就返回
    const quickStrategies = [
      () => this.generateByID(element, document),
      () => this.generateByName(element, document),
      () => this.generateByTestId(element, document),
      () => this.generateByAriaRole(element, document),
      () => this.generateByPlaceholder(element, document),
      () => this.generateByAltText(element, document),
      () => this.generateBySimpleText(element, document), // 简化版本
      () => this.generateByOptimizedClass(element, document) // 优化版本
    ]

    // 尝试快速策略，找到唯一选择器就立即返回
    for (const strategy of quickStrategies) {
      try {
        const result = strategy()
        if (result && result.count === 1) {
          return result
        }
      } catch (error) {
        console.warn('策略执行失败:', error)
        continue
      }
    }

    // 如果快速策略都失败，使用XPath作为后备
    return this.generateByOptimizedXPath(element, document)
  }

  /**
   * 缓存的DOM查询
   */
  cachedQuerySelectorAll(selector, document) {
    if (this.queryCache.has(selector)) {
      return this.queryCache.get(selector)
    }

    const elements = document.querySelectorAll(selector)
    this.queryCache.set(selector, elements)
    return elements
  }

  /**
   * 通过ID生成选择器
   */
  generateByID(element, document) {
    const id = element.id
    if (!id) return null

    const selector = `#${CSS.escape(id)}`
    const count = this.cachedQuerySelectorAll(selector, document).length

    return {
      strategy: 'id',
      selector: selector,
      playwrightCode: `page.locator('#${id}')`,
      count: count,
      priority: 1,
      description: `通过ID属性定位: ${id}`,
      isUnique: count === 1,
      stability: 'high'
    }
  }

  /**
   * 通过name属性生成选择器
   */
  generateByName(element, document) {
    const name = element.getAttribute('name')
    if (!name) return null

    const selector = `[name="${name}"]`
    const count = this.cachedQuerySelectorAll(selector, document).length

    return {
      strategy: 'name',
      selector: selector,
      playwrightCode: `page.locator('[name="${name}"]')`,
      count: count,
      priority: 2,
      description: `通过name属性定位: ${name}`,
      isUnique: count === 1,
      stability: 'high'
    }
  }

  /**
   * 通过data-testid生成选择器
   */
  generateByTestId(element, document) {
    const testIdAttrs = ['data-testid', 'data-test', 'data-cy', 'data-e2e']

    for (const attr of testIdAttrs) {
      const testId = element.getAttribute(attr)
      if (testId) {
        const selector = `[${attr}="${testId}"]`
        const count = this.cachedQuerySelectorAll(selector, document).length

        return {
          strategy: 'testid',
          selector: selector,
          playwrightCode: attr === 'data-testid' ? `page.getByTestId('${testId}')` : `page.locator('[${attr}="${testId}"]')`,
          count: count,
          priority: 3,
          description: `通过测试ID定位: ${testId}`,
          isUnique: count === 1,
          stability: 'very-high'
        }
      }
    }
    return null
  }

  /**
   * 简化的ARIA角色生成器 - 只检查显式角色
   */
  generateByAriaRole(element, document) {
    const role = element.getAttribute('role')
    if (!role || !this.commonAriaRoles.includes(role)) return null

    // 简化：只检查显式角色，不计算可访问名称
    const selector = `[role="${role}"]`
    const count = this.cachedQuerySelectorAll(selector, document).length

    return {
      strategy: 'role',
      selector: selector,
      playwrightCode: `page.getByRole('${role}')`,
      count: count,
      priority: 4,
      description: `通过ARIA角色定位: ${role}`,
      isUnique: count === 1,
      stability: 'high'
    }
  }

  /**
   * 通过placeholder生成选择器
   */
  generateByPlaceholder(element, document) {
    const placeholder = element.getAttribute('placeholder')
    if (!placeholder) return null

    const selector = `[placeholder="${placeholder}"]`
    const count = this.cachedQuerySelectorAll(selector, document).length

    return {
      strategy: 'placeholder',
      selector: selector,
      playwrightCode: `page.getByPlaceholder('${placeholder}')`,
      count: count,
      priority: 5,
      description: `通过占位符文本定位: ${placeholder}`,
      isUnique: count === 1,
      stability: 'medium'
    }
  }

  /**
   * 通过alt文本生成选择器
   */
  generateByAltText(element, document) {
    if (element.tagName.toLowerCase() !== 'img') return null

    const altText = element.getAttribute('alt')
    if (!altText) return null

    const selector = `img[alt="${altText}"]`
    const count = this.cachedQuerySelectorAll(selector, document).length

    return {
      strategy: 'alttext',
      selector: selector,
      playwrightCode: `page.getByAltText('${altText}')`,
      count: count,
      priority: 6,
      description: `通过alt文本定位: ${altText}`,
      isUnique: count === 1,
      stability: 'high'
    }
  }

  /**
   * 简化的文本内容生成器 - 避免全页面遍历
   */
  generateBySimpleText(element, document) {
    const text = element.textContent?.trim()
    if (!text || text.length > 30) return null // 缩短文本长度限制

    // 只检查常见的交互元素
    const interactiveElements = ['button', 'a', 'span']
    if (!interactiveElements.includes(element.tagName.toLowerCase())) return null

    // 使用更精确的CSS选择器，避免全页面搜索
    const tagName = element.tagName.toLowerCase()
    const exactElements = Array.from(document.querySelectorAll(tagName)).filter(el => el.textContent?.trim() === text)

    return {
      strategy: 'text',
      selector: `:has-text("${text}")`,
      playwrightCode: `page.getByText('${text}')`,
      count: exactElements.length,
      priority: 7,
      description: `通过文本内容定位: ${text}`,
      isUnique: exactElements.length === 1,
      stability: 'medium'
    }
  }

  /**
   * 优化的class选择器生成器
   */
  generateByOptimizedClass(element, document) {
    const className = element.className
    if (!className || typeof className !== 'string') return null

    const classes = className.trim().split(/\s+/)
    if (classes.length === 0) return null

    // 只检查第一个稳定的类名
    for (const cls of classes.slice(0, 3)) {
      // 最多检查前3个类
      if (cls && !this.isGeneratedClass(cls)) {
        const selector = `.${CSS.escape(cls)}`
        const count = this.cachedQuerySelectorAll(selector, document).length

        if (count <= 5) {
          // 如果匹配元素不超过5个，认为是可接受的
          return {
            strategy: 'class',
            selector: selector,
            playwrightCode: `page.locator('.${cls}')`,
            count: count,
            priority: 8,
            description: `通过CSS类定位: ${cls}`,
            isUnique: count === 1,
            stability: 'low'
          }
        }
      }
    }
    return null
  }

  /**
   * 优化的XPath生成器
   */
  generateByOptimizedXPath(element, document) {
    const xpath = this.generateSimpleXPath(element)

    return {
      strategy: 'xpath',
      selector: xpath,
      playwrightCode: `page.locator('xpath=${xpath}')`,
      count: 1, // 假设XPath是唯一的，避免昂贵的验证
      priority: 9,
      description: `通过XPath定位: ${xpath}`,
      isUnique: true,
      stability: 'very-low'
    }
  }

  /**
   * 生成简化的XPath
   */
  generateSimpleXPath(element) {
    if (element.id) {
      return `//*[@id="${element.id}"]`
    }

    const paths = []
    let currentElement = element
    let depth = 0

    while (currentElement && currentElement.nodeType === Node.ELEMENT_NODE && depth < 4) {
      let selector = currentElement.tagName.toLowerCase()

      if (currentElement.id) {
        selector += `[@id="${currentElement.id}"]`
        paths.unshift(selector)
        break
      } else {
        // 简化：不计算兄弟元素索引，直接使用标签名
        paths.unshift(selector)
      }

      currentElement = currentElement.parentElement
      depth++
    }

    return '//' + paths.join('/')
  }

  /**
   * 检查是否是自动生成的类名
   */
  isGeneratedClass(className) {
    const generatedPatterns = [/^css-[a-z0-9]+$/i, /^[a-z]+-[0-9a-f]{6,}$/i, /^_[a-z0-9]+$/i, /^[a-f0-9]{8,}$/i]

    return generatedPatterns.some(pattern => pattern.test(className))
  }
}

// 导出类和便捷方法
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PlaywrightSelectorGenerator
} else if (typeof window !== 'undefined') {
  window.PlaywrightSelectorGenerator = PlaywrightSelectorGenerator
}

/**
 * 便捷函数：生成智能选择器
 */
function generatePlaywrightSelector(element, document = window.document) {
  const generator = new PlaywrightSelectorGenerator()
  return generator.generateSelector(element, document)
}

// 导出便捷函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports.generatePlaywrightSelector = generatePlaywrightSelector
} else if (typeof window !== 'undefined') {
  window.generatePlaywrightSelector = generatePlaywrightSelector
}
