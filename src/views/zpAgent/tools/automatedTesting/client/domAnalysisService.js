// DOM分析服务 - 独立模块
const { chromium } = require('playwright')
const PlaywrightSelectorGenerator = require('./generateSelector')
const { IframeDomExtractor } = require('./iframeDomExtractor')

class DOMAnalysisService {
  constructor() {
    this.browser = null
    this.domExtractor = null
  }

  // 初始化DOM提取器
  async initialize() {
    if (!this.domExtractor) {
      this.domExtractor = new IframeDomExtractor()
    }
  }

  // 关闭DOM提取器
  async close() {
    if (this.domExtractor) {
      await this.domExtractor.cleanup()
    }
  }

  // 分析页面DOM结构
  async analyzePageDOM(url, cookie, options = {}) {
    console.log('📝 开始分析页面DOM:', JSON.stringify(options, null, 2))

    // 初始化DOM提取器
    await this.initialize()

    // 转换cookie格式为数组
    const cookiesArray = this.parseCookieString(cookie, url)

    try {
      // 🖼️ 获取完整DOM结构（包括iframe）
      let fullDomStructure = null
      if (options.includeIframes !== false) {
        // 默认包含iframe分析
        try {
          // 使用独立的DOM提取器获取完整结构
          const domResult = await this.domExtractor.extractFullDomStructure(url, cookiesArray, {
            timeout: options.timeout || 30000,
            pageLoadDelay: options.pageLoadDelay || 2000,
            waitUntil: 'networkidle'
          })

          fullDomStructure = domResult
          console.log(`✅ 成功获取DOM结构: 主页面 + ${domResult.summary.totalIframes} 个iframe (成功: ${domResult.summary.successfulIframes})`)
        } catch (error) {
          console.warn('⚠️ 获取完整DOM结构失败:', error.message)
        }
      }

      // 如果获取到了完整DOM结构，直接分析
      if (fullDomStructure) {
        console.log('获取完整DOM结构 fullDomStructure', JSON.stringify(fullDomStructure, null, 2))
        return await this.analyzeFullDomStructure(fullDomStructure, options)
      }

      // 否则回退到传统的单页面分析
      console.log('fullDomStructure 回退到传统的单页面分析', JSON.stringify(fullDomStructure, null, 2))
      return await this.analyzeSinglePage(url, cookiesArray, options)
    } catch (error) {
      console.error('❌ DOM分析过程中出错:', error)
      throw error
    }
  }

  // 解析Cookie字符串为数组格式
  parseCookieString(cookie, url) {
    const cookiesArray = []
    if (cookie && typeof cookie === 'string') {
      const domain = new URL(url).hostname
      const cookiePairs = cookie.split(/[,;]/).map(pair => pair.trim())

      cookiePairs.forEach(pair => {
        const [name, value] = pair.split('=')
        if (name && value) {
          cookiesArray.push({
            name: name.trim(),
            value: value.trim(),
            domain: domain,
            path: '/'
          })
        }
      })
    }
    return cookiesArray
  }

  // 分析完整DOM结构（主页面 + iframe）
  async analyzeFullDomStructure(domStructure, options = {}) {
    console.log('🔍 开始分析完整DOM结构...')

    // 分析主页面
    let mainPageElements = []
    try {
      mainPageElements = await this.analyzeHtmlContent(domStructure.mainPage.dom, options)
      console.log(`✅ 主页面分析完成，发现 ${mainPageElements.length} 个元素`)
    } catch (error) {
      console.warn('⚠️ 主页面分析失败:', error.message)
    }

    // 分析每个iframe
    const iframeElements = []
    let totalIframeElementsCount = 0

    for (let i = 0; i < domStructure.iframes.length; i++) {
      const iframe = domStructure.iframes[i]
      console.log(`🔍 分析第 ${i + 1} 个iframe: ${iframe.src || 'inline'}`)

      try {
        if (iframe.dom && !iframe.error) {
          const elements = await this.analyzeHtmlContent(iframe.dom, options, i + 1)
          iframeElements.push({
            iframeIndex: iframe.index,
            iframeId: iframe.id || null,
            iframeSrc: iframe.src || null,
            elements: elements,
            totalCount: elements.length,
            error: null
          })
          totalIframeElementsCount += elements.length
          console.log(`✅ iframe ${i + 1} 分析完成，发现 ${elements.length} 个元素`)
        } else {
          iframeElements.push({
            iframeIndex: iframe.index,
            iframeId: iframe.id || null,
            iframeSrc: iframe.src || null,
            elements: [],
            totalCount: 0,
            error: iframe.error || 'iframe DOM内容为空'
          })
          console.log(`⚠️ iframe ${i + 1} 跳过分析: ${iframe.error}`)
        }
      } catch (error) {
        console.warn(`⚠️ iframe ${i + 1} 分析失败:`, error.message)
        iframeElements.push({
          iframeIndex: iframe.index,
          iframeId: iframe.id || null,
          iframeSrc: iframe.src || null,
          elements: [],
          totalCount: 0,
          error: error.message
        })
      }
    }

    console.log(`🎉 DOM结构分析完成！主页面: ${mainPageElements.length} 个元素，iframe: ${totalIframeElementsCount} 个元素`)

    // 返回原始接口格式
    return {
      success: true,
      data: {
        url: domStructure.mainPage.url,
        elements: mainPageElements, // 主页面元素
        iframeElements: iframeElements, // iframe元素数组
        timestamp: new Date().toISOString(),
        totalCount: mainPageElements.length,
        totalIframeElementsCount: totalIframeElementsCount,
        fullDomStructure: domStructure, // 完整DOM结构
        domAnalysis: {
          mainPageDomSize: domStructure.mainPage.stats.domSize,
          totalIframes: domStructure.summary.totalIframes,
          successfulIframes: domStructure.summary.successfulIframes,
          failedIframes: domStructure.summary.failedIframes,
          iframeDetails: domStructure.iframes.map(iframe => ({
            index: iframe.index,
            id: iframe.id,
            src: iframe.src,
            type: iframe.type,
            method: iframe.method,
            success: !!iframe.dom,
            domSize: iframe.dom ? iframe.dom.length : 0,
            error: iframe.error
          }))
        }
      }
    }
  }

  // 分析HTML内容（从DOM字符串中提取元素）
  async analyzeHtmlContent(htmlContent, options = {}, iframeIndex = 0) {
    // 创建临时页面来分析HTML内容
    if (!this.domExtractor.browser) {
      await this.domExtractor.init()
    }

    const page = await this.domExtractor.context.newPage()

    try {
      // 设置HTML内容
      await page.setContent(htmlContent)
      await page.waitForTimeout(1000) // 等待内容加载

      // 注入选择器生成器
      await page.addScriptTag({
        path: './generateSelector.js'
      })

      // 执行DOM分析
      const elements = await page.evaluate(
        opts => {
          const results = []
          let elementId = 1

          // 默认要分析的元素类型及其选择器
          const defaultElementSelectors = {
            input: 'input:not([type="hidden"])',
            button: 'button, input[type="button"], input[type="submit"]',
            link: 'a[href]',
            select: 'select',
            textarea: 'textarea',
            form: 'form'
          }

          // 处理用户传入的参数
          let elementSelectors
          if (opts.elementTypes && Array.isArray(opts.elementTypes)) {
            elementSelectors = {}
            opts.elementTypes.forEach(type => {
              if (defaultElementSelectors[type]) {
                elementSelectors[type] = defaultElementSelectors[type]
              } else {
                elementSelectors[type] = type
              }
            })
          } else {
            elementSelectors = defaultElementSelectors
          }

          // 智能选择器生成函数，使用注入的类
          function generateSelectorV1(element) {
            try {
              // 使用全局的 generatePlaywrightSelector 函数
              if (typeof generatePlaywrightSelector === 'function') {
                const result = generatePlaywrightSelector(element, document)
                return result ? result : null
              }
              // 如果全局函数不存在，返回null
              return null
            } catch (error) {
              console.warn('高级选择器生成失败:', error.message)
              return null
            }
          }

          // 获取元素的可见文本
          function getElementText(element) {
            // 按钮或链接的文本
            if (element.textContent && element.textContent.trim()) {
              return element.textContent.trim().substring(0, 100)
            }

            // 输入框的placeholder
            if (element.placeholder) {
              return element.placeholder
            }

            // 表单元素的label
            const label = document.querySelector(`label[for="${element.id}"]`)
            if (label && label.textContent) {
              return label.textContent.trim()
            }

            // 查找附近的文本
            const parent = element.parentElement
            if (parent) {
              const parentText = parent.textContent?.trim()
              if (parentText && parentText.length < 200) {
                return parentText.substring(0, 100)
              }
            }

            return ''
          }

          // 检查元素是否可见
          function isElementVisible(element) {
            if (opts.includeHidden) return true

            const style = window.getComputedStyle(element)
            return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0' && element.offsetWidth > 0 && element.offsetHeight > 0
          }

          // 开始循环分析每种类型的元素
          Object.entries(elementSelectors).forEach(([type, selector]) => {
            const elements = document.querySelectorAll(selector)

            elements.forEach(element => {
              if (isElementVisible(element)) {
                const elementText = getElementText(element)
                const generatedSelector = generateSelectorV1(element)

                const elementData = {
                  id: elementId++,
                  type: type,
                  selector: generatedSelector,
                  text: elementText,
                  attributes: {},
                  position: {
                    x: element.offsetLeft,
                    y: element.offsetTop,
                    width: element.offsetWidth,
                    height: element.offsetHeight
                  },
                  iframeContext: opts.iframeIndex > 0,
                  iframeIndex: opts.iframeIndex || 0
                }

                // 收集有用的属性
                const importantAttrs = ['id', 'class', 'name', 'type', 'placeholder', 'value', 'href', 'role', 'aria-label', 'src', 'alt', 'title', 'disabled', 'checked', 'selected', 'required', 'readonly', 'action', 'target', 'onclick', 'onchange', 'ka']
                importantAttrs.forEach(attr => {
                  const value = element.getAttribute(attr)
                  if (value) {
                    elementData.attributes[attr] = value
                  }
                })

                results.push(elementData)
              }
            })
          })

          // 按位置排序（从上到下，从左到右）
          results.sort((a, b) => {
            if (Math.abs(a.position.y - b.position.y) > 10) {
              return a.position.y - b.position.y
            }
            return a.position.x - b.position.x
          })

          return results
        },
        { ...options, iframeIndex }
      )

      return elements
    } catch (error) {
      console.error('HTML内容分析失败:', error)
      throw error
    } finally {
      await page.close()
    }
  }

  // 单页面分析（回退方法）
  async analyzeSinglePage(url, cookiesArray, options = {}) {
    console.log('🔄 使用单页面分析模式...')

    if (!this.domExtractor.browser) {
      await this.domExtractor.init()
    }

    const page = await this.domExtractor.context.newPage()

    try {
      // 设置Cookie
      if (cookiesArray && cookiesArray.length > 0) {
        await this.domExtractor.context.addCookies(cookiesArray)
        console.log('✅ Cookie设置成功')
      }

      // 访问页面
      console.log('🌐 正在访问页面:', url)
      await page.goto(url, {
        waitUntil: 'networkidle',
        timeout: options.timeout || 30000
      })

      await page.waitForSelector('body', { state: 'visible' })

      // 获取页面内容
      const htmlContent = await page.content()
      const title = await page.title()

      // 分析页面元素
      const elements = await this.analyzeHtmlContent(htmlContent, options)

      return {
        success: true,
        data: {
          url: url,
          elements: elements,
          iframeElements: [], // 单页面模式没有iframe分析
          timestamp: new Date().toISOString(),
          totalCount: elements.length,
          totalIframeElementsCount: 0,
          mode: 'single-page'
        }
      }
    } catch (error) {
      console.error('单页面分析失败:', error)
      throw error
    } finally {
      await page.close()
    }
  }

  // 智能提取关键元素（基于测试场景）
  async extractKeyElements(url, scenario) {
    const analysisResult = await this.analyzePageDOM(url)

    if (!analysisResult.success) {
      return analysisResult
    }

    const allElements = analysisResult.data.elements

    // 基于场景关键词过滤元素
    const keywordMap = {
      登录: ['login', 'signin', '登录', '用户名', '密码', 'username', 'password'],
      搜索: ['search', 'query', '搜索', '查找', 'find'],
      注册: ['register', 'signup', '注册', '创建账户'],
      购买: ['buy', 'purchase', 'cart', '购买', '加入购物车', '立即购买'],
      提交: ['submit', 'send', '提交', '发送', '确认']
    }

    let relevantKeywords = []
    Object.entries(keywordMap).forEach(([key, words]) => {
      if (scenario.includes(key)) {
        relevantKeywords.push(...words)
      }
    })

    // 如果没有匹配的关键词，使用通用关键词
    if (relevantKeywords.length === 0) {
      relevantKeywords = ['button', 'input', 'link', '按钮', '输入', '链接']
    }

    // 过滤相关元素
    const keyElements = allElements.filter(element => {
      const text = (element.text || '').toLowerCase()
      const selector = String(element.selector || '').toLowerCase()
      const attrs = Object.values(element.attributes || {})
        .join(' ')
        .toLowerCase()

      return relevantKeywords.some(keyword => text.includes(keyword.toLowerCase()) || selector.includes(keyword.toLowerCase()) || attrs.includes(keyword.toLowerCase()))
    })

    return {
      success: true,
      data: {
        url: url,
        scenario: scenario,
        elements: keyElements,
        totalCount: keyElements.length,
        filteredFrom: allElements.length
      }
    }
  }

  // 验证Playwright locator并返回匹配的元素详情（混合策略版本）
  async validatePlaywrightLocators(url, locators, cookie = null) {
    console.log('🔍 开始验证Playwright locator（混合策略）...')

    // 初始化DOM提取器
    await this.initialize()

    // 转换cookie格式为数组
    const cookiesArray = this.parseCookieString(cookie, url)

    try {
      // 2. 创建页面进行实时验证
      if (!this.domExtractor.browser) {
        await this.domExtractor.init()
      }

      const page = await this.domExtractor.context.newPage()

      try {
        // 设置Cookie
        if (cookiesArray && cookiesArray.length > 0) {
          await this.domExtractor.context.addCookies(cookiesArray)
          console.log('✅ Cookie设置成功')
        }

        // 访问页面
        await page.goto(url, {
          waitUntil: 'networkidle',
          timeout: 30000
        })

        // 等待页面加载
        await page.waitForTimeout(2000)

        // 验证每个locator
        const results = []

        for (const locatorExpression of locators) {
          try {
            // 解析Playwright locator表达式
            const locatorSelector = this.parsePlaywrightLocator(locatorExpression)

            if (!locatorSelector) {
              results.push({
                locator: locatorExpression,
                valid: false,
                count: 0,
                error: '无法解析的Playwright locator格式'
              })
              continue
            }

            // 在主页面验证locator
            const mainPageResult = await this.validateLocatorOnPage(page, locatorSelector, locatorExpression, false, 0)

            // 验证iframe（仅使用直接访问和URL策略）
            const iframeResults = await this.validateLocatorInIframes(page, locatorSelector, locatorExpression)

            // 合并主页面和iframe结果
            const allElements = [...mainPageResult.elements, ...iframeResults]

            results.push({
              locator: locatorExpression,
              selector: locatorSelector,
              valid: allElements.length > 0,
              count: allElements.length,
              elements: allElements
            })
          } catch (error) {
            results.push({
              locator: locatorExpression,
              valid: false,
              count: 0,
              error: error.message
            })
          }
        }

        return {
          success: true,
          data: results
        }
      } finally {
        await page.close()
      }
    } catch (error) {
      console.error('❌ 验证过程中出错:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 验证iframe中的locator（仅使用直接访问和URL策略）
  async validateLocatorInIframes(page, locatorSelector, locatorExpression) {
    const iframeResults = []

    try {
      const iframes = await page.locator('iframe').all()
      console.log(`🖼️ 发现 ${iframes.length} 个iframe，开始验证...`)

      for (let i = 0; i < iframes.length; i++) {
        try {
          const iframe = iframes[i]

          // 策略1: 尝试直接访问iframe（同域）
          const directResult = await this.validateIframeDirectly(iframe, locatorSelector, locatorExpression, i)
          if (directResult.success) {
            iframeResults.push(...directResult.elements)
            console.log(`✅ iframe ${i} 直接验证成功，找到 ${directResult.elements.length} 个元素`)
            continue
          }

          // 策略2: 尝试通过URL验证（独立页面）
          const urlResult = await this.validateIframeByUrl(iframe, locatorSelector, locatorExpression, i)
          if (urlResult.success) {
            iframeResults.push(...urlResult.elements)
            console.log(`✅ iframe ${i} URL验证成功，找到 ${urlResult.elements.length} 个元素`)
          }
        } catch (iframeError) {
          console.warn(`⚠️ iframe ${i} 所有策略都失败:`, iframeError.message)
        }
      }
    } catch (error) {
      console.warn('⚠️ iframe验证过程出错:', error.message)
    }

    return iframeResults
  }

  // 策略1: 直接验证iframe（适用于同域）
  async validateIframeDirectly(iframe, locatorSelector, locatorExpression, iframeIndex) {
    try {
      const frame = await iframe.contentFrame()
      if (!frame) {
        return { success: false, error: '无法获取iframe frame对象' }
      }

      const result = await this.validateLocatorOnPage(frame, locatorSelector, locatorExpression, true, iframeIndex)
      return {
        success: result.elements.length > 0,
        elements: result.elements
      }
    } catch (error) {
      console.log('error validateIframeDirectly', error)
      return { success: false, error: error.message }
    }
  }

  // 策略2: 通过URL验证iframe（适用于可独立访问的iframe）
  async validateIframeByUrl(iframe, locatorSelector, locatorExpression, iframeIndex) {
    try {
      const src = await iframe.getAttribute('src')
      if (!src || !src.startsWith('http')) {
        return { success: false, error: '无有效的iframe URL' }
      }

      const tempPage = await this.domExtractor.context.newPage()
      try {
        await tempPage.goto(src, {
          waitUntil: 'networkidle',
          timeout: 15000
        })
        await tempPage.waitForTimeout(1000)

        const result = await this.validateLocatorOnPage(tempPage, locatorSelector, locatorExpression, true, iframeIndex)

        // 添加iframe上下文信息
        result.elements.forEach(element => {
          element.iframeSrc = src
          element.iframeType = 'url-accessed'
          element.iframeMethod = 'url'
        })

        return {
          success: result.elements.length > 0,
          elements: result.elements
        }
      } finally {
        await tempPage.close()
      }
    } catch (error) {
      console.log('error validateIframeByUrl', error)
      return { success: false, error: error.message }
    }
  }

  // 在指定页面或frame上验证locator
  async validateLocatorOnPage(pageOrFrame, selector, originalLocator, isIframe = false, iframeIndex = 0) {
    try {
      // 使用Playwright的locator方法
      let locator
      // 如果selector是原始的Playwright方法调用，需要执行它
      if (selector.startsWith('page.')) {
        // 将page.xxx()转换为pageOrFrame.xxx()
        const methodCall = selector.replace('page.', 'pageOrFrame.')
        console.log('methodCall', methodCall)

        try {
          // 使用eval执行Playwright方法
          locator = eval(methodCall)
        } catch (evalError) {
          console.log('eval error', evalError)
          // 如果eval失败，尝试手动解析常见的Playwright方法
          locator = this.executePlaywrightMethod(pageOrFrame, selector)
        }
      } else {
        // 传统的CSS选择器
        locator = pageOrFrame.locator(selector)
      }

      console.log('locator', locator)
      const count = await locator.count()
      console.log('count', count)

      if (count === 0) {
        return {
          elements: []
        }
      }
      // 获取所有匹配元素的详细信息
      const elements = []
      for (let i = 0; i < count; i++) {
        const element = locator.nth(i)

        try {
          const elementInfo = {
            index: i,
            text: '',
            tagName: '',
            attributes: {},
            boundingBox: null,
            isVisible: false,
            iframeContext: isIframe,
            iframeIndex: iframeIndex
          }

          // 获取元素文本
          try {
            elementInfo.text = (await element.textContent()) || ''
          } catch (e) {
            elementInfo.text = ''
          }

          // 获取标签名
          try {
            elementInfo.tagName = await element.evaluate(el => el.tagName.toLowerCase())
          } catch (e) {
            elementInfo.tagName = ''
          }

          // 获取属性
          try {
            elementInfo.attributes = await element.evaluate(el => {
              const attrs = {}
              for (let attr of el.attributes) {
                attrs[attr.name] = attr.value
              }
              return attrs
            })
          } catch (e) {
            elementInfo.attributes = {}
          }

          // 获取边界框
          try {
            elementInfo.boundingBox = await element.boundingBox()
          } catch (e) {
            elementInfo.boundingBox = null
          }

          // 检查是否可见
          try {
            elementInfo.isVisible = await element.isVisible()
          } catch (e) {
            elementInfo.isVisible = false
          }

          // 如果是iframe中的元素，获取iframe信息
          if (isIframe) {
            try {
              const iframeElement = await pageOrFrame.locator('iframe').nth(iframeIndex)
              elementInfo.iframeId = (await iframeElement.getAttribute('id')) || null
              elementInfo.iframeSrc = (await iframeElement.getAttribute('src')) || null
            } catch (e) {
              elementInfo.iframeId = null
              elementInfo.iframeSrc = null
            }
          }

          elements.push(elementInfo)
        } catch (elementError) {
          console.log('elementError', elementError)
          elements.push({
            index: i,
            error: elementError.message,
            iframeContext: isIframe,
            iframeIndex: iframeIndex
          })
        }
      }

      return {
        elements: elements
      }
    } catch (error) {
      console.log('error validateLocatorOnPage', error)
      return {
        elements: [],
        error: error.message
      }
    }
  }

  // 执行Playwright方法
  executePlaywrightMethod(pageOrFrame, selector) {
    try {
      // 解析getByRole方法
      const getByRoleMatch = selector.match(/page\.getByRole\(['"`]([^'"`]+)['"`](?:,\s*\{([^}]+)\})?\)/)
      if (getByRoleMatch) {
        const role = getByRoleMatch[1]
        const optionsStr = getByRoleMatch[2]

        if (optionsStr) {
          // 解析options对象
          const options = {}
          const nameMatch = optionsStr.match(/name:\s*['"`]([^'"`]+)['"`]/)
          if (nameMatch) {
            options.name = nameMatch[1]
          }
          return pageOrFrame.getByRole(role, options)
        } else {
          return pageOrFrame.getByRole(role)
        }
      }

      // 解析getByText方法
      const getByTextMatch = selector.match(/page\.getByText\(['"`]([^'"`]+)['"`]\)/)
      if (getByTextMatch) {
        const text = getByTextMatch[1]
        return pageOrFrame.getByText(text)
      }

      // 解析getByPlaceholder方法
      const getByPlaceholderMatch = selector.match(/page\.getByPlaceholder\(['"`]([^'"`]+)['"`]\)/)
      if (getByPlaceholderMatch) {
        const placeholder = getByPlaceholderMatch[1]
        return pageOrFrame.getByPlaceholder(placeholder)
      }

      // 解析getByLabel方法
      const getByLabelMatch = selector.match(/page\.getByLabel\(['"`]([^'"`]+)['"`]\)/)
      if (getByLabelMatch) {
        const label = getByLabelMatch[1]
        return pageOrFrame.getByLabel(label)
      }

      // 解析getByTestId方法
      const getByTestIdMatch = selector.match(/page\.getByTestId\(['"`]([^'"`]+)['"`]\)/)
      if (getByTestIdMatch) {
        const testId = getByTestIdMatch[1]
        return pageOrFrame.getByTestId(testId)
      }

      // 解析getByTitle方法
      const getByTitleMatch = selector.match(/page\.getByTitle\(['"`]([^'"`]+)['"`]\)/)
      if (getByTitleMatch) {
        const title = getByTitleMatch[1]
        return pageOrFrame.getByTitle(title)
      }

      // 解析getByAltText方法
      const getByAltTextMatch = selector.match(/page\.getByAltText\(['"`]([^'"`]+)['"`]\)/)
      if (getByAltTextMatch) {
        const altText = getByAltTextMatch[1]
        return pageOrFrame.getByAltText(altText)
      }

      // 如果都不匹配，返回null
      console.log('无法解析的Playwright方法:', selector)
      return null
    } catch (error) {
      console.log('executePlaywrightMethod error:', error)
      return null
    }
  }

  // 解析Playwright locator表达式,返回selector
  parsePlaywrightLocator(locatorExpression) {
    // 检查是否是Playwright的getBy系列方法
    if (locatorExpression.includes('getByRole') || locatorExpression.includes('getByText') || locatorExpression.includes('getByPlaceholder') || locatorExpression.includes('getByLabel') || locatorExpression.includes('getByTestId') || locatorExpression.includes('getByTitle') || locatorExpression.includes('getByAltText')) {
      // 对于getBy系列方法，直接返回原始表达式
      return locatorExpression
    }

    // 移除 page.locator(' 和 ') 部分
    const match = locatorExpression.match(/page\.locator\(['"`](.+?)['"`]\)/)
    if (match) {
      return match[1]
    }

    // 如果没有page.locator包装，直接返回
    return locatorExpression
  }
}

// 创建单例实例
const domAnalysisService = new DOMAnalysisService()

// 优雅关闭处理
process.on('SIGINT', async () => {
  console.log('正在关闭DOM分析服务...')
  await domAnalysisService.close()
})

process.on('SIGTERM', async () => {
  console.log('正在关闭DOM分析服务...')
  await domAnalysisService.close()
})

// 导出类和单例实例
module.exports = domAnalysisService
module.exports.DOMAnalysisService = DOMAnalysisService
