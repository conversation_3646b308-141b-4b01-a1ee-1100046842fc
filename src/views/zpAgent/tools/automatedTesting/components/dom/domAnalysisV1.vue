<template>
  <div class="dom-analysis-container">
    <div class="layout-container">
      <!-- 侧边栏导航 -->
      <div class="sidebar">
        <el-menu :default-active="activeTab" class="sidebar-menu" @select="switchTab" background-color="#f8f9fa" text-color="#606266" active-text-color="#409EFF">
          <el-menu-item v-for="tab in tabs" :key="tab.key" :index="tab.key" class="menu-item">
            <i :class="tab.icon" class="menu-icon"></i>
            <span slot="title">{{ tab.name }}</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 主内容区域 -->
      <div class="main-content">
        <!-- 清理HTML -->
        <div v-show="activeTab === 'clean'" class="tab-content">
          <div class="content-header">
            <h3>清理HTML</h3>
            <p>移除无用的HTML标签和属性，保留核心结构</p>
          </div>

          <div class="input-section">
            <div class="input-group">
              <label>输入HTML内容：</label>
              <el-input type="textarea" v-model="htmlInput" placeholder="粘贴您的HTML代码..." :rows="8" class="html-input" />
            </div>

            <div class="options-group">
              <el-checkbox-group v-model="cleanOptions">
                <el-checkbox label="removeComments">移除注释</el-checkbox>
                <el-checkbox label="removeScript">移除脚本</el-checkbox>
                <el-checkbox label="removeStyle">移除样式</el-checkbox>
                <el-checkbox label="removeEmptyTags">移除空标签</el-checkbox>
                <el-checkbox label="simplifyClasses">简化类名</el-checkbox>
              </el-checkbox-group>
            </div>

            <div class="action-buttons">
              <el-button type="primary" @click="cleanHTML" :loading="isProcessing">
                <i class="el-icon-refresh"></i>
                清理HTML
              </el-button>
              <el-button @click="clearInput">清空</el-button>
            </div>
          </div>

          <div v-if="cleanedHTML" class="result-section">
            <div class="result-header">
              <h4>清理结果</h4>
              <div class="result-stats">
                <span>原始: {{ htmlInput.length }} 字符</span>
                <span>清理后: {{ cleanedHTML.length }} 字符</span>
                <span>压缩率: {{ compressionRatio }}%</span>
              </div>
            </div>
            <el-input type="textarea" v-model="cleanedHTML" :rows="8" readonly class="result-textarea" />
            <div class="result-actions">
              <el-button size="small" @click="copyToClipboard(cleanedHTML)">
                <i class="el-icon-copy-document"></i>
                复制结果
              </el-button>
              <el-button size="small" @click="downloadHTML">
                <i class="el-icon-download"></i>
                下载文件
              </el-button>
            </div>
          </div>
        </div>

        <!-- 提取元素 -->
        <div v-show="activeTab === 'extract'" class="tab-content">
          <div class="content-header">
            <h3>提取元素</h3>
            <p>从HTML中提取指定类型的元素</p>
          </div>

          <div class="input-section">
            <div class="input-group">
              <label>HTML内容：</label>
              <el-input type="textarea" v-model="extractHTML" placeholder="粘贴HTML代码或从清理结果中继承..." :rows="6" />
            </div>

            <div class="extract-options">
              <div class="option-group">
                <label>提取元素类型：</label>
                <el-checkbox-group v-model="extractTypes">
                  <el-checkbox label="input">输入框</el-checkbox>
                  <el-checkbox label="button">按钮</el-checkbox>
                  <el-checkbox label="a">链接</el-checkbox>
                  <el-checkbox label="select">下拉框</el-checkbox>
                  <el-checkbox label="textarea">文本域</el-checkbox>
                  <el-checkbox label="form">表单</el-checkbox>
                  <el-checkbox label="img">图片</el-checkbox>
                  <el-checkbox label="div">容器</el-checkbox>
                </el-checkbox-group>
              </div>

              <div class="option-group">
                <label>提取选项：</label>
                <el-checkbox-group v-model="extractOptions">
                  <el-checkbox label="includeHidden">包含隐藏元素</el-checkbox>
                  <el-checkbox label="includeAttributes">包含属性</el-checkbox>
                  <el-checkbox label="includeText">包含文本内容</el-checkbox>
                  <el-checkbox label="generateSelector">生成选择器</el-checkbox>
                </el-checkbox-group>
              </div>
            </div>

            <div class="action-buttons">
              <el-button type="primary" @click="extractElements" :loading="isExtracting">
                <i class="el-icon-search"></i>
                提取元素
              </el-button>
              <el-button @click="inheritFromClean" :disabled="!cleanedHTML">
                <i class="el-icon-right"></i>
                继承清理结果
              </el-button>
            </div>
          </div>

          <div v-if="extractedElements.length > 0" class="result-section">
            <div class="result-header">
              <h4>提取结果</h4>
              <div class="result-stats">
                <span>共找到 {{ extractedElements.length }} 个元素</span>
              </div>
            </div>

            <div class="elements-list">
              <div v-for="(element, index) in extractedElements" :key="index" class="element-item">
                <div class="element-header">
                  <el-tag :type="getElementTypeColor(element.type)" size="small">
                    {{ element.type.toUpperCase() }}
                  </el-tag>
                  <span class="element-text">{{ element.text || '无文本' }}</span>
                </div>

                <div class="element-details">
                  <div v-if="element.selector" class="element-selector">
                    <label>选择器：</label>
                    <code>{{ element.selector }}</code>
                  </div>

                  <div v-if="element.attributes && Object.keys(element.attributes).length > 0" class="element-attributes">
                    <label>属性：</label>
                    <div class="attributes-tags">
                      <el-tag v-for="(value, key) in element.attributes" :key="key" size="mini" type="info">{{ key }}="{{ value }}"</el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="result-actions">
              <el-button size="small" @click="copyToClipboard(JSON.stringify(extractedElements, null, 2))">
                <i class="el-icon-copy-document"></i>
                复制JSON
              </el-button>
              <el-button size="small" @click="exportElements">
                <i class="el-icon-download"></i>
                导出数据
              </el-button>
            </div>
          </div>
        </div>

        <!-- 生成测试 -->
        <div v-show="activeTab === 'generate'" class="tab-content">
          <div class="content-header">
            <h3>生成测试</h3>
            <p>基于提取的元素生成自动化测试脚本</p>
          </div>

          <div class="input-section">
            <div class="input-group">
              <label>测试描述：</label>
              <el-input type="textarea" v-model="testDescription" placeholder="描述您想要生成的测试场景，例如：登录页面测试、表单提交测试等..." :rows="4" />
            </div>

            <div class="test-options">
              <div class="option-group">
                <label>测试框架：</label>
                <el-radio-group v-model="testFramework">
                  <el-radio label="playwright">Playwright</el-radio>
                  <el-radio label="selenium">Selenium</el-radio>
                  <el-radio label="cypress">Cypress</el-radio>
                </el-radio-group>
              </div>

              <div class="option-group">
                <label>编程语言：</label>
                <el-radio-group v-model="testLanguage">
                  <el-radio label="javascript">JavaScript</el-radio>
                  <el-radio label="python">Python</el-radio>
                  <el-radio label="java">Java</el-radio>
                </el-radio-group>
              </div>
            </div>

            <div class="action-buttons">
              <el-button type="primary" @click="generateTest" :loading="isGenerating">
                <i class="el-icon-magic-stick"></i>
                生成测试脚本
              </el-button>
              <el-button @click="useExtractedElements" :disabled="extractedElements.length === 0">
                <i class="el-icon-right"></i>
                使用提取的元素
              </el-button>
            </div>
          </div>

          <div v-if="generatedTest" class="result-section">
            <div class="result-header">
              <h4>生成的测试脚本</h4>
              <div class="result-stats">
                <span>框架: {{ testFramework }}</span>
                <span>语言: {{ testLanguage }}</span>
              </div>
            </div>

            <div class="code-editor">
              <pre><code>{{ generatedTest }}</code></pre>
            </div>

            <div class="result-actions">
              <el-button size="small" @click="copyToClipboard(generatedTest)">
                <i class="el-icon-copy-document"></i>
                复制代码
              </el-button>
              <el-button size="small" @click="downloadTest">
                <i class="el-icon-download"></i>
                下载文件
              </el-button>
              <el-button size="small" @click="optimizeTest">
                <i class="el-icon-magic-stick"></i>
                优化代码
              </el-button>
            </div>
          </div>
        </div>

        <!-- AI分析 -->
        <div v-show="activeTab === 'analyze'" class="tab-content">
          <div class="content-header">
            <h3>AI分析</h3>
            <p>使用AI智能分析DOM结构和元素关系</p>
          </div>

          <div class="input-section">
            <div class="input-group">
              <label>分析内容：</label>
              <el-input type="textarea" v-model="analyzeHTML" placeholder="输入HTML内容进行AI分析..." :rows="6" />
            </div>

            <div class="analyze-options">
              <div class="option-group">
                <label>分析类型：</label>
                <el-checkbox-group v-model="analyzeTypes">
                  <el-checkbox label="structure">结构分析</el-checkbox>
                  <el-checkbox label="usability">可用性分析</el-checkbox>
                  <el-checkbox label="accessibility">可访问性分析</el-checkbox>
                  <el-checkbox label="performance">性能分析</el-checkbox>
                  <el-checkbox label="seo">SEO分析</el-checkbox>
                </el-checkbox-group>
              </div>

              <div class="option-group">
                <label>分析深度：</label>
                <el-radio-group v-model="analyzeDepth">
                  <el-radio label="basic">基础分析</el-radio>
                  <el-radio label="detailed">详细分析</el-radio>
                  <el-radio label="comprehensive">全面分析</el-radio>
                </el-radio-group>
              </div>
            </div>

            <div class="action-buttons">
              <el-button type="primary" @click="analyzeWithAI" :loading="isAnalyzing">
                <i class="el-icon-view"></i>
                AI分析
              </el-button>
              <el-button @click="inheritFromExtract" :disabled="!extractHTML">
                <i class="el-icon-right"></i>
                继承提取内容
              </el-button>
            </div>
          </div>

          <div v-if="analysisResult" class="result-section">
            <div class="result-header">
              <h4>AI分析结果</h4>
              <div class="result-stats">
                <span>分析时间: {{ analysisTime }}</span>
              </div>
            </div>

            <div class="analysis-content">
              <div class="analysis-item" v-for="(item, index) in analysisResult" :key="index">
                <div class="analysis-title">
                  <i :class="item.icon"></i>
                  <span>{{ item.title }}</span>
                  <el-tag :type="item.level" size="mini">{{ item.level }}</el-tag>
                </div>
                <div class="analysis-description">
                  {{ item.description }}
                </div>
                <div v-if="item.suggestions" class="analysis-suggestions">
                  <h5>建议：</h5>
                  <ul>
                    <li v-for="suggestion in item.suggestions" :key="suggestion">
                      {{ suggestion }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="result-actions">
              <el-button size="small" @click="copyToClipboard(formatAnalysisResult())">
                <i class="el-icon-copy-document"></i>
                复制分析报告
              </el-button>
              <el-button size="small" @click="downloadAnalysis">
                <i class="el-icon-download"></i>
                下载报告
              </el-button>
            </div>
          </div>
        </div>

        <!-- 批量处理 -->
        <div v-show="activeTab === 'batch'" class="tab-content">
          <div class="content-header">
            <h3>批量处理</h3>
            <p>批量处理多个HTML文件或URL</p>
          </div>

          <div class="input-section">
            <div class="batch-input-type">
              <el-radio-group v-model="batchInputType">
                <el-radio label="files">文件上传</el-radio>
                <el-radio label="urls">URL列表</el-radio>
                <el-radio label="text">文本输入</el-radio>
              </el-radio-group>
            </div>

            <div v-if="batchInputType === 'files'" class="file-upload">
              <el-upload class="upload-demo" drag multiple :on-change="handleFileChange" :auto-upload="false" accept=".html,.htm,.txt">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将HTML文件拖到此处，或
                  <em>点击上传</em>
                </div>
                <div class="el-upload__tip" slot="tip">只能上传HTML/HTM/TXT文件</div>
              </el-upload>
            </div>

            <div v-if="batchInputType === 'urls'" class="url-input">
              <el-input type="textarea" v-model="batchUrls" placeholder="输入URL列表，每行一个URL..." :rows="6" />
            </div>

            <div v-if="batchInputType === 'text'" class="text-input">
              <el-input type="textarea" v-model="batchTexts" placeholder="输入多个HTML内容，用分隔符分开..." :rows="6" />
            </div>

            <div class="batch-options">
              <div class="option-group">
                <label>处理操作：</label>
                <el-checkbox-group v-model="batchOperations">
                  <el-checkbox label="clean">清理HTML</el-checkbox>
                  <el-checkbox label="extract">提取元素</el-checkbox>
                  <el-checkbox label="analyze">AI分析</el-checkbox>
                  <el-checkbox label="generate">生成测试</el-checkbox>
                </el-checkbox-group>
              </div>

              <div class="option-group">
                <label>输出格式：</label>
                <el-radio-group v-model="batchOutputFormat">
                  <el-radio label="json">JSON</el-radio>
                  <el-radio label="excel">Excel</el-radio>
                  <el-radio label="csv">CSV</el-radio>
                </el-radio-group>
              </div>
            </div>

            <div class="action-buttons">
              <el-button type="primary" @click="startBatchProcess" :loading="isBatchProcessing">
                <i class="el-icon-s-promotion"></i>
                开始批量处理
              </el-button>
              <el-button @click="clearBatchInput">清空输入</el-button>
            </div>
          </div>

          <div v-if="batchResults.length > 0" class="result-section">
            <div class="result-header">
              <h4>批量处理结果</h4>
              <div class="result-stats">
                <span>处理项目: {{ batchResults.length }}</span>
                <span>成功: {{ batchResults.filter(r => r.success).length }}</span>
                <span>失败: {{ batchResults.filter(r => !r.success).length }}</span>
              </div>
            </div>

            <div class="batch-results">
              <div v-for="(result, index) in batchResults" :key="index" class="batch-result-item" :class="{ success: result.success, error: !result.success }">
                <div class="result-header">
                  <span class="result-index">{{ index + 1 }}</span>
                  <span class="result-name">{{ result.name }}</span>
                  <el-tag :type="result.success ? 'success' : 'danger'" size="mini">
                    {{ result.success ? '成功' : '失败' }}
                  </el-tag>
                </div>

                <div v-if="result.success" class="result-content">
                  <div v-if="result.cleaned" class="result-item">
                    <label>清理结果:</label>
                    <span>{{ result.cleaned.length }} 字符</span>
                  </div>
                  <div v-if="result.elements" class="result-item">
                    <label>提取元素:</label>
                    <span>{{ result.elements.length }} 个</span>
                  </div>
                  <div v-if="result.analysis" class="result-item">
                    <label>AI分析:</label>
                    <span>{{ result.analysis.length }} 项</span>
                  </div>
                </div>

                <div v-else class="result-error">
                  <span>错误: {{ result.error }}</span>
                </div>
              </div>
            </div>

            <div class="result-actions">
              <el-button size="small" @click="downloadBatchResults">
                <i class="el-icon-download"></i>
                下载所有结果
              </el-button>
              <el-button size="small" @click="clearBatchResults">
                <i class="el-icon-delete"></i>
                清空结果
              </el-button>
            </div>
          </div>
        </div>

        <!-- 帮助对话框 -->
        <el-dialog title="使用帮助" :visible.sync="showHelp" width="60%">
          <div class="help-content">
            <h3>HTML DOM 清理工具使用说明</h3>

            <div class="help-section">
              <h4>🧹 清理HTML</h4>
              <p>移除HTML中的无用标签、注释、脚本等，保留核心结构。适用于：</p>
              <ul>
                <li>清理从网页复制的HTML代码</li>
                <li>移除不必要的样式和脚本</li>
                <li>简化HTML结构便于分析</li>
              </ul>
            </div>

            <div class="help-section">
              <h4>🔍 提取元素</h4>
              <p>从HTML中提取指定类型的交互元素，生成结构化数据。适用于：</p>
              <ul>
                <li>分析页面中的表单元素</li>
                <li>提取可点击的按钮和链接</li>
                <li>生成元素选择器</li>
              </ul>
            </div>

            <div class="help-section">
              <h4>🧪 生成测试</h4>
              <p>基于提取的元素自动生成测试脚本。支持：</p>
              <ul>
                <li>Playwright、Selenium、Cypress框架</li>
                <li>JavaScript、Python、Java语言</li>
                <li>自定义测试场景</li>
              </ul>
            </div>

            <div class="help-section">
              <h4>🤖 AI分析</h4>
              <p>使用AI智能分析DOM结构，提供优化建议。包括：</p>
              <ul>
                <li>结构合理性分析</li>
                <li>可用性和可访问性检查</li>
                <li>性能优化建议</li>
                <li>SEO优化提示</li>
              </ul>
            </div>

            <div class="help-section">
              <h4>📦 批量处理</h4>
              <p>批量处理多个HTML文件或URL，提高工作效率。支持：</p>
              <ul>
                <li>文件上传批量处理</li>
                <li>URL列表批量分析</li>
                <li>多种输出格式</li>
              </ul>
            </div>
          </div>

          <span slot="footer" class="dialog-footer">
            <el-button @click="showHelp = false">关闭</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'domAnalysis',
  data() {
    return {
      // 当前激活的选项卡
      activeTab: 'clean',

      // 选项卡配置
      tabs: [
        { key: 'clean', name: '清理HTML', icon: 'el-icon-refresh' },
        { key: 'extract', name: '提取元素', icon: 'el-icon-search' },
        { key: 'generate', name: '生成测试', icon: 'el-icon-magic-stick' },
        { key: 'analyze', name: 'AI分析', icon: 'el-icon-view' },
        { key: 'batch', name: '批量处理', icon: 'el-icon-s-promotion' }
      ],

      // 清理HTML相关
      htmlInput: '',
      cleanedHTML: '',
      cleanOptions: ['removeComments', 'removeScript', 'removeStyle', 'removeEmptyTags'],
      isProcessing: false,

      // 提取元素相关
      extractHTML: '',
      extractedElements: [],
      extractTypes: ['input', 'button', 'a', 'select'],
      extractOptions: ['includeAttributes', 'includeText', 'generateSelector'],
      isExtracting: false,

      // 生成测试相关
      testDescription: '',
      testFramework: 'playwright',
      testLanguage: 'javascript',
      generatedTest: '',
      isGenerating: false,

      // AI分析相关
      analyzeHTML: '',
      analysisResult: null,
      analyzeTypes: ['structure', 'usability'],
      analyzeDepth: 'basic',
      isAnalyzing: false,
      analysisTime: '',

      // 批量处理相关
      batchInputType: 'files',
      batchUrls: '',
      batchTexts: '',
      batchOperations: ['clean', 'extract'],
      batchOutputFormat: 'json',
      batchResults: [],
      isBatchProcessing: false,

      // 其他
      showHelp: false
    }
  },

  computed: {
    compressionRatio() {
      if (!this.htmlInput || !this.cleanedHTML) return 0
      return Math.round((1 - this.cleanedHTML.length / this.htmlInput.length) * 100)
    }
  },

  methods: {
    // 切换选项卡
    switchTab(tab) {
      this.activeTab = tab
    },

    // 清理HTML
    async cleanHTML() {
      if (!this.htmlInput.trim()) {
        this.$message.warning('请输入HTML内容')
        return
      }

      this.isProcessing = true
      try {
        // 模拟清理过程
        await new Promise(resolve => setTimeout(resolve, 1000))

        let cleaned = this.htmlInput

        // 根据选项进行清理
        if (this.cleanOptions.includes('removeComments')) {
          cleaned = cleaned.replace(/<!--[\s\S]*?-->/g, '')
        }
        if (this.cleanOptions.includes('removeScript')) {
          cleaned = cleaned.replace(/<script[\s\S]*?<\/script>/gi, '')
        }
        if (this.cleanOptions.includes('removeStyle')) {
          cleaned = cleaned.replace(/<style[\s\S]*?<\/style>/gi, '')
          cleaned = cleaned.replace(/style\s*=\s*"[^"]*"/gi, '')
        }
        if (this.cleanOptions.includes('removeEmptyTags')) {
          cleaned = cleaned.replace(/<(\w+)[^>]*>\s*<\/\1>/g, '')
        }
        if (this.cleanOptions.includes('simplifyClasses')) {
          cleaned = cleaned.replace(/class\s*=\s*"[^"]*"/gi, match => {
            const classes = match.match(/class\s*=\s*"([^"]*)"/i)
            if (classes && classes[1]) {
              const simpleClasses = classes[1].split(' ').slice(0, 2).join(' ')
              return `class="${simpleClasses}"`
            }
            return match
          })
        }

        // 格式化HTML
        this.cleanedHTML = this.formatHTML(cleaned)
        this.$message.success('HTML清理完成')
      } catch (error) {
        this.$message.error('清理失败: ' + error.message)
      } finally {
        this.isProcessing = false
      }
    },

    // 提取元素
    async extractElements() {
      if (!this.extractHTML.trim()) {
        this.$message.warning('请输入HTML内容')
        return
      }

      this.isExtracting = true
      try {
        // 模拟提取过程
        await new Promise(resolve => setTimeout(resolve, 1000))

        const parser = new DOMParser()
        const doc = parser.parseFromString(this.extractHTML, 'text/html')
        const elements = []

        this.extractTypes.forEach(type => {
          const nodes = doc.querySelectorAll(type)
          nodes.forEach((node, index) => {
            const element = {
              id: `${type}_${index}`,
              type: type,
              text: node.textContent?.trim() || node.placeholder || '',
              attributes: {},
              position: { x: 0, y: 0, width: 0, height: 0 }
            }

            if (this.extractOptions.includes('includeAttributes')) {
              Array.from(node.attributes).forEach(attr => {
                element.attributes[attr.name] = attr.value
              })
            }

            if (this.extractOptions.includes('generateSelector')) {
              element.selector = this.generateSelector(node)
            }

            elements.push(element)
          })
        })

        this.extractedElements = elements
        this.$message.success(`成功提取 ${elements.length} 个元素`)
      } catch (error) {
        this.$message.error('提取失败: ' + error.message)
      } finally {
        this.isExtracting = false
      }
    },

    // 生成测试脚本
    async generateTest() {
      if (!this.testDescription.trim()) {
        this.$message.warning('请输入测试描述')
        return
      }

      this.isGenerating = true
      try {
        // 模拟AI生成过程
        await new Promise(resolve => setTimeout(resolve, 2000))

        const template = this.getTestTemplate()
        this.generatedTest = template
        this.$message.success('测试脚本生成完成')
      } catch (error) {
        this.$message.error('生成失败: ' + error.message)
      } finally {
        this.isGenerating = false
      }
    },

    // AI分析
    async analyzeWithAI() {
      if (!this.analyzeHTML.trim()) {
        this.$message.warning('请输入要分析的HTML内容')
        return
      }

      this.isAnalyzing = true
      try {
        const startTime = Date.now()
        // 模拟AI分析过程
        await new Promise(resolve => setTimeout(resolve, 3000))

        this.analysisResult = this.generateAnalysisResult()
        this.analysisTime = `${Date.now() - startTime}ms`
        this.$message.success('AI分析完成')
      } catch (error) {
        this.$message.error('分析失败: ' + error.message)
      } finally {
        this.isAnalyzing = false
      }
    },

    // 开始批量处理
    async startBatchProcess() {
      if (this.batchOperations.length === 0) {
        this.$message.warning('请选择至少一个处理操作')
        return
      }

      this.isBatchProcessing = true
      this.batchResults = []

      try {
        let items = []

        // 根据输入类型准备处理项目
        if (this.batchInputType === 'urls') {
          items = this.batchUrls.split('\n').filter(url => url.trim())
        } else if (this.batchInputType === 'text') {
          items = this.batchTexts.split('---').filter(text => text.trim())
        }

        // 模拟批量处理
        for (let i = 0; i < items.length; i++) {
          const item = items[i]
          await new Promise(resolve => setTimeout(resolve, 1000))

          const result = {
            name: `Item ${i + 1}`,
            success: Math.random() > 0.2, // 80%成功率
            cleaned: this.batchOperations.includes('clean') ? `cleaned_${i}` : null,
            elements: this.batchOperations.includes('extract')
              ? Array(5)
                  .fill(null)
                  .map((_, j) => ({ id: j, type: 'button' }))
              : null,
            analysis: this.batchOperations.includes('analyze')
              ? Array(3)
                  .fill(null)
                  .map((_, j) => ({ title: `Analysis ${j}` }))
              : null,
            error: Math.random() > 0.8 ? '处理失败' : null
          }

          this.batchResults.push(result)
        }

        this.$message.success(`批量处理完成，共处理 ${items.length} 个项目`)
      } catch (error) {
        this.$message.error('批量处理失败: ' + error.message)
      } finally {
        this.isBatchProcessing = false
      }
    },

    // 工具方法
    formatHTML(html) {
      // 简单的HTML格式化
      return html.replace(/></g, '>\n<').replace(/^\s+|\s+$/g, '')
    },

    generateSelector(element) {
      // 生成CSS选择器
      if (element.id) return `#${element.id}`
      if (element.className) return `.${element.className.split(' ')[0]}`
      return element.tagName.toLowerCase()
    },

    getTestTemplate() {
      // 根据框架和语言生成测试模板
      const templates = {
        playwright: {
          javascript: `const { test, expect } = require('@playwright/test');
  
  test('${this.testDescription}', async ({ page }) => {
    // 访问页面
    await page.goto('https://example.com');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 执行测试操作
    ${this.extractedElements
      .map(
        el => `
    // ${el.type}: ${el.text}
    await page.click('${el.selector || el.type}');`
      )
      .join('')}
    
    // 验证结果
    await expect(page).toHaveTitle(/Expected Title/);
  });`
        }
      }

      return templates[this.testFramework]?.[this.testLanguage] || '// 测试脚本模板'
    },

    generateAnalysisResult() {
      // 生成模拟的分析结果
      return [
        {
          title: '结构分析',
          level: 'success',
          icon: 'el-icon-s-grid',
          description: 'HTML结构良好，层次清晰，语义化标签使用合理。',
          suggestions: ['建议添加更多语义化标签', '优化嵌套层级']
        },
        {
          title: '可用性分析',
          level: 'warning',
          icon: 'el-icon-user',
          description: '部分交互元素缺少适当的标签和提示。',
          suggestions: ['为表单元素添加label', '增加按钮的描述文本']
        },
        {
          title: '性能分析',
          level: 'info',
          icon: 'el-icon-lightning',
          description: 'DOM结构相对简单，性能影响较小。',
          suggestions: ['减少不必要的嵌套', '优化CSS选择器']
        }
      ]
    },

    formatAnalysisResult() {
      // 格式化分析结果为文本
      return this.analysisResult.map(item => `${item.title}: ${item.description}\n建议: ${item.suggestions.join(', ')}`).join('\n\n')
    },

    getElementTypeColor(type) {
      const colors = {
        input: 'primary',
        button: 'success',
        a: 'info',
        select: 'warning',
        textarea: 'primary',
        form: 'danger',
        img: 'info',
        div: ''
      }
      return colors[type] || ''
    },

    // 复制到剪贴板
    async copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        this.$message.success('已复制到剪贴板')
      } catch (error) {
        this.$message.error('复制失败')
      }
    },

    // 继承功能
    inheritFromClean() {
      this.extractHTML = this.cleanedHTML
    },

    inheritFromExtract() {
      this.analyzeHTML = this.extractHTML
    },

    useExtractedElements() {
      if (this.extractedElements.length === 0) return
      this.testDescription = `测试页面包含 ${this.extractedElements.length} 个交互元素`
    },

    // 清空功能
    clearInput() {
      this.htmlInput = ''
      this.cleanedHTML = ''
    },

    clearBatchInput() {
      this.batchUrls = ''
      this.batchTexts = ''
    },

    clearBatchResults() {
      this.batchResults = []
    },

    // 下载功能
    downloadHTML() {
      this.downloadFile(this.cleanedHTML, 'cleaned.html', 'text/html')
    },

    downloadTest() {
      const ext = this.testLanguage === 'javascript' ? 'js' : this.testLanguage === 'python' ? 'py' : 'java'
      this.downloadFile(this.generatedTest, `test.${ext}`, 'text/plain')
    },

    downloadAnalysis() {
      this.downloadFile(this.formatAnalysisResult(), 'analysis.txt', 'text/plain')
    },

    downloadBatchResults() {
      const data = JSON.stringify(this.batchResults, null, 2)
      this.downloadFile(data, 'batch_results.json', 'application/json')
    },

    downloadFile(content, filename, type) {
      const blob = new Blob([content], { type })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      a.click()
      URL.revokeObjectURL(url)
    },

    // 文件处理
    handleFileChange(file) {
      const reader = new FileReader()
      reader.onload = e => {
        // 处理文件内容
        console.log('File content:', e.target.result)
      }
      reader.readAsText(file.raw)
    },

    // 导出功能
    exportElements() {
      const data = JSON.stringify(this.extractedElements, null, 2)
      this.downloadFile(data, 'elements.json', 'application/json')
    },

    // 优化测试
    optimizeTest() {
      // 模拟优化过程
      this.generatedTest = this.generatedTest.replace(/\/\/ 优化前/g, '// 优化后')
      this.$message.success('代码优化完成')
    }
  }
}
</script>

<style scoped>
.dom-analysis-container {
  height: 100vh;
  background: #f0f2f5;
  overflow: hidden;
}

/* 布局容器 */
.layout-container {
  display: flex;
  height: 100%;
}

/* 侧边栏 */
.sidebar {
  width: 260px;
  background: #ffffff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 24px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: 1px solid #e8e8e8;
}

.sidebar-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sidebar-title i {
  font-size: 20px;
}

/* 侧边栏菜单 */
.sidebar-menu {
  flex: 1;
  border-right: none;
  background-color: #f8f9fa !important;
  padding: 8px 0;
}

.sidebar-menu .el-menu-item {
  height: 48px;
  line-height: 48px;
  padding: 0 20px !important;
  margin: 2px 8px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.sidebar-menu .el-menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: transparent;
  transition: all 0.3s ease;
}

.sidebar-menu .el-menu-item:hover {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  transform: translateX(2px);
}

.sidebar-menu .el-menu-item:hover::before {
  background: #1890ff;
}

.sidebar-menu .el-menu-item.is-active {
  background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateX(2px);
}

.sidebar-menu .el-menu-item.is-active::before {
  background: rgba(255, 255, 255, 0.3);
}

.sidebar-menu .el-menu-item.is-active .menu-icon {
  color: white !important;
}

.menu-icon {
  font-size: 16px;
  margin-right: 12px;
  width: 16px;
  text-align: center;
  transition: all 0.3s ease;
}

.sidebar-menu .el-menu-item:hover .menu-icon {
  transform: scale(1.1);
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: 16px 20px;
  border-top: 1px solid #e8e8e8;
  background: #fafafa;
}

.help-button {
  width: 100%;
  justify-content: flex-start;
  color: #666;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.help-button:hover {
  background: #e6f7ff;
  color: #1890ff;
  border-color: #d9d9d9;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.help-button i {
  margin-right: 8px;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #f0f2f5;
}

/* 选项卡内容 */
.tab-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: calc(100vh - 48px);
  overflow-y: auto;
  margin-bottom: 0;
}

.content-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f0f0;
}

.content-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #333;
}

.content-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 输入区域 */
.input-section {
  margin-bottom: 32px;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.html-input,
.result-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.options-group,
.extract-options,
.test-options,
.analyze-options {
  margin: 20px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.option-group {
  margin-bottom: 16px;
}

.option-group:last-child {
  margin-bottom: 0;
}

.option-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

/* 结果区域 */
.result-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 2px solid #f0f0f0;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.result-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.result-stats {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #666;
}

.result-stats span {
  padding: 4px 8px;
  background: #f0f0f0;
  border-radius: 4px;
}

.result-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

/* 元素列表 */
.elements-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.element-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.2s;
}

.element-item:hover {
  background: #f8f9fa;
}

.element-item:last-child {
  border-bottom: none;
}

.element-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.element-text {
  font-weight: 500;
  color: #333;
}

.element-details {
  margin-left: 24px;
}

.element-selector {
  margin-bottom: 8px;
}

.element-selector label {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.element-selector code {
  background: #f1f3f4;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #d73a49;
}

.element-attributes label {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.attributes-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 代码编辑器 */
.code-editor {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.code-editor pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
}

.code-editor code {
  background: none;
  padding: 0;
  color: inherit;
}

/* AI分析结果 */
.analysis-content {
  max-height: 500px;
  overflow-y: auto;
}

.analysis-item {
  padding: 16px;
  margin-bottom: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.analysis-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.analysis-description {
  margin-bottom: 12px;
  color: #666;
  line-height: 1.5;
}

.analysis-suggestions h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
}

.analysis-suggestions ul {
  margin: 0;
  padding-left: 20px;
}

.analysis-suggestions li {
  margin-bottom: 4px;
  color: #666;
  font-size: 13px;
}

/* 批量处理 */
.batch-input-type {
  margin-bottom: 20px;
}

.file-upload,
.url-input,
.text-input {
  margin-bottom: 20px;
}

.batch-options {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.batch-results {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.batch-result-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.batch-result-item:last-child {
  border-bottom: none;
}

.batch-result-item.success {
  border-left: 4px solid #67c23a;
}

.batch-result-item.error {
  border-left: 4px solid #f56c6c;
}

.batch-result-item .result-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.result-index {
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.result-name {
  font-weight: 500;
  color: #333;
}

.result-content {
  margin-left: 24px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.result-item label {
  color: #666;
  font-weight: 500;
}

.result-item span {
  color: #333;
}

.result-error {
  margin-left: 24px;
  color: #f56c6c;
  font-size: 13px;
}

/* 帮助对话框 */
.help-content {
  max-height: 60vh;
  overflow-y: auto;
}

.help-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.help-section:last-child {
  border-bottom: none;
}

.help-section h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
}

.help-section p {
  margin: 0 0 12px 0;
  color: #666;
  line-height: 1.5;
}

.help-section ul {
  margin: 0;
  padding-left: 20px;
}

.help-section li {
  margin-bottom: 4px;
  color: #666;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #e8e8e8;
  }

  .sidebar-menu {
    display: flex;
    overflow-x: auto;
    background-color: #f8f9fa !important;
  }

  .sidebar-menu .el-menu-item {
    white-space: nowrap;
    min-width: 120px;
    margin: 4px 2px;
  }

  .main-content {
    padding: 16px;
  }

  .tab-content {
    padding: 16px;
    height: auto;
    max-height: calc(100vh - 200px);
  }

  .action-buttons {
    flex-direction: column;
  }

  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .result-stats {
    flex-direction: column;
    gap: 8px;
  }

  .result-actions {
    flex-direction: column;
  }
}

/* 动画效果 */
.tab-content {
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 侧边栏进入动画 */
.sidebar {
  animation: slideInLeft 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 菜单项进入动画 */
.sidebar-menu .el-menu-item {
  animation: fadeInUp 0.3s ease;
  animation-fill-mode: both;
}

.sidebar-menu .el-menu-item:nth-child(1) {
  animation-delay: 0.1s;
}
.sidebar-menu .el-menu-item:nth-child(2) {
  animation-delay: 0.2s;
}
.sidebar-menu .el-menu-item:nth-child(3) {
  animation-delay: 0.3s;
}
.sidebar-menu .el-menu-item:nth-child(4) {
  animation-delay: 0.4s;
}
.sidebar-menu .el-menu-item:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.elements-list::-webkit-scrollbar,
.analysis-content::-webkit-scrollbar,
.batch-results::-webkit-scrollbar,
.code-editor::-webkit-scrollbar {
  width: 6px;
}

.elements-list::-webkit-scrollbar-track,
.analysis-content::-webkit-scrollbar-track,
.batch-results::-webkit-scrollbar-track,
.code-editor::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.elements-list::-webkit-scrollbar-thumb,
.analysis-content::-webkit-scrollbar-thumb,
.batch-results::-webkit-scrollbar-thumb,
.code-editor::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.elements-list::-webkit-scrollbar-thumb:hover,
.analysis-content::-webkit-scrollbar-thumb:hover,
.batch-results::-webkit-scrollbar-thumb:hover,
.code-editor::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
