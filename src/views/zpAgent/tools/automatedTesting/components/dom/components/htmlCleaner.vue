<template>
  <div class="html-cleaner">
    <div class="cleaner-content">
      <!-- 输入区域 -->
      <div class="input-section">
        <div class="section-title">
          <i class="ri-code-line"></i>
          输入HTML代码
        </div>
        <div class="input-container">
          <textarea v-model="inputHtml" class="html-input" placeholder="请粘贴需要清理的HTML代码..." rows="10"></textarea>
          <div class="input-actions">
            <!-- 清理选项 -->
            <div class="options-inline" v-if="inputHtml.trim()">
              <div class="option-item">
                <el-checkbox v-model="cleanOptions.removeEmptyTags">删除空白标签</el-checkbox>
              </div>
              <div class="option-item">
                <el-checkbox v-model="cleanOptions.minify">压缩HTML</el-checkbox>
              </div>
              <div class="option-item">
                <el-checkbox v-model="cleanOptions.prettify">美化HTML</el-checkbox>
              </div>
              <div class="option-item">
                <el-checkbox v-model="cleanOptions.removeComments">删除注释</el-checkbox>
              </div>
            </div>

            <div class="action-buttons">
              <button class="btn btn-primary" @click="cleanHtml" :disabled="!inputHtml.trim() || isProcessing">
                <i class="ri-magic-line" v-if="!isProcessing"></i>
                <i class="ri-loader-4-line rotating" v-else></i>
                {{ isProcessing ? '清理中...' : '开始清理' }}
              </button>
              <button class="btn btn-secondary" @click="clearInput" :disabled="!inputHtml.trim()">
                <i class="ri-delete-bin-line"></i>
                清空
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计数据 -->
      <div class="stats-section" v-if="cleanStats">
        <div class="section-title">
          <i class="ri-bar-chart-line"></i>
          清理统计
        </div>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ cleanStats.originalSize }}</div>
            <div class="stat-label">原始大小</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ cleanStats.cleanedSize }}</div>
            <div class="stat-label">清理后大小</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ cleanStats.removedAttributes }}</div>
            <div class="stat-label">删除属性</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ cleanStats.removedTags }}</div>
            <div class="stat-label">删除标签</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ cleanStats.reductionPercentage }}%</div>
            <div class="stat-label">减少比例</div>
          </div>
        </div>
      </div>

      <!-- 清理结果 -->
      <div class="result-section" v-if="cleanedHtml">
        <div class="section-title">
          <i class="ri-file-code-line"></i>
          清理结果
          <div class="title-actions">
            <button class="btn btn-small btn-success" @click="copyResult" :disabled="!cleanedHtml">
              <i class="ri-file-copy-line"></i>
              复制
            </button>
            <button class="btn btn-small btn-info" @click="downloadResult" :disabled="!cleanedHtml">
              <i class="ri-download-line"></i>
              下载
            </button>
            <button class="btn btn-small btn-warning" @click="selectDomElement" :disabled="!cleanedHtml">
              <i class="ri-cursor-line"></i>
              选择元素
            </button>
          </div>
        </div>

        <!-- 结果展示选项卡 -->
        <div class="result-tabs">
          <div class="tab-headers">
            <div class="tab-header" :class="{ active: activeTab === 'editor' }" @click="activeTab = 'editor'">
              <i class="ri-code-line"></i>
              代码编辑器
            </div>
            <div class="tab-header" :class="{ active: activeTab === 'tree' }" @click="activeTab = 'tree'">
              <i class="ri-node-tree"></i>
              DOM树结构
            </div>
          </div>

          <div class="tab-content">
            <!-- Monaco Editor 选项卡 -->
            <div v-show="activeTab === 'editor'" class="tab-panel">
              <div class="monaco-container">
                <div ref="monacoEditor" class="monaco-editor" style="height: 400px; border: 1px solid var(--border-color); border-radius: var(--radius)"></div>
              </div>
            </div>

            <!-- DOM树结构 选项卡 -->
            <div v-show="activeTab === 'tree'" class="tab-panel">
              <div class="tree-container">
                <dom-tree-viewer :html-content="cleanedHtml" @node-select="handleNodeSelect" @node-click="handleNodeClick" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- AI分析结果 -->
      <!-- <div class="ai-analysis-section" v-if="aiAnalysis">
        <div class="section-title">
          <i class="ri-brain-line"></i>
          AI结构分析
        </div>
        <div class="analysis-content">
          <div class="analysis-item" v-if="aiAnalysis.structure.length">
            <h4>页面结构</h4>
            <div class="structure-list">
              <div v-for="(item, index) in aiAnalysis.structure.slice(0, 10)" :key="index" class="structure-item">
                <span class="tag-name">{{ item.tag }}</span>
                <span class="tag-info">{{ item.classes || item.id || item.text.substring(0, 50) }}</span>
              </div>
            </div>
          </div>

          <div class="analysis-item" v-if="aiAnalysis.formElements.length">
            <h4>表单元素</h4>
            <div class="form-elements-list">
              <div v-for="(item, index) in aiAnalysis.formElements.slice(0, 5)" :key="index" class="form-element-item">
                <span class="element-tag">{{ item.tag }}</span>
                <span class="element-type">{{ item.type }}</span>
                <span class="element-name">{{ item.name || item.placeholder || item.text }}</span>
              </div>
            </div>
          </div>

          <div class="analysis-item" v-if="aiAnalysis.links.length">
            <h4>链接 ({{ aiAnalysis.links.length }})</h4>
          </div>

          <div class="analysis-item" v-if="aiAnalysis.images.length">
            <h4>图片 ({{ aiAnalysis.images.length }})</h4>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
import src from '@orh/vue-kityminder'
import DomTreeViewer from './domTreeViewer.vue'

export default {
  name: 'htmlCleaner',
  components: {
    DomTreeViewer
  },
  data() {
    return {
      inputHtml: '',
      cleanedHtml: '',
      isProcessing: false,
      monacoEditor: null,
      selectedElement: null,
      cleanOptions: {
        removeEmptyTags: true,
        minify: false,
        prettify: true,
        removeComments: true
      },
      cleanStats: null,
      aiAnalysis: null,
      activeTab: 'editor', // 新增：控制当前激活的选项卡
      // HTML清洗配置
      defaultRemovePatterns: [
        /^data-v-[a-f0-9]+$/, // Vue scoped styles: data-v-ec3b0716
        /^data-reactid$/, // React ID
        /^data-react-/, // React相关属性
        /^data-testid$/, // 测试ID
        /^data-cy$/, // Cypress测试
        /^data-test/, // 通用测试属性
        /^data-v/, // Vue scoped styles
        /^data-n/, // NPM scoped styles
        /^data-pv/, // Svelte scoped styles
        /^data-/, // 其他框架 scoped styles
        /^ng-/, // Angular指令
        /^v-/, // Vue指令
        /^:[a-z]/, // Vue动态属性
        /^@[a-z]/, // Vue事件
        /^data-gtm/, // Google Tag Manager
        /^data-ga/, // Google Analytics
        /^data-track/, // 追踪相关
        /^data-analytics/, // 分析相关
        /^_ngcontent/, // Angular内容
        /^_nghost/, // Angular宿主
        /^ka$/, // 卡拉云属性
        /^style$/, // 样式属性
        /^value$/, // 值属性
        /^alt$/, // 替代文本属性
        /^rel$/, // 关系属性
        /^src$/, // 源属性
        /^href$/, // 链接属性
        /^transform$/, // 转换属性
        /^d$/ // 数据属性
      ],
      customRemovePatterns: [],
      removeSpecificValues: {
        href: ['javascript:;', 'javascript:', '#'] // 删除无效的href值
      },
      keepAttributes: new Set(['id', 'class', 'name', 'type', 'placeholder', 'disabled', 'readonly', 'required', 'for', 'role', 'aria-label', 'aria-describedby', 'tabindex', 'target', 'download', 'lang', 'dir']),
      removeTags: new Set(['script', 'style', 'noscript', 'meta', 'link', 'head', 'title', 'href', 'src', 'svg', 'symbol'])
    }
  },

  beforeDestroy() {
    // 清理Monaco Editor
    if (this.monacoEditor) {
      this.monacoEditor.dispose()
    }
  },

  methods: {
    // 清理HTML主方法
    async cleanHtml() {
      if (!this.inputHtml.trim()) {
        this.$message.warning('请输入要清理的HTML代码')
        return
      }

      this.isProcessing = true

      try {
        // 模拟处理时间
        await new Promise(resolve => setTimeout(resolve, 500))

        const originalSize = this.formatBytes(new Blob([this.inputHtml]).size)

        // 清理HTML
        const result = this.performHtmlClean(this.inputHtml, this.cleanOptions)
        this.cleanedHtml = result.html

        const cleanedSize = this.formatBytes(new Blob([this.cleanedHtml]).size)
        const reduction = (((this.inputHtml.length - this.cleanedHtml.length) / this.inputHtml.length) * 100).toFixed(1)

        // 生成统计数据
        this.cleanStats = {
          originalSize,
          cleanedSize,
          removedAttributes: result.stats.removedAttributes,
          removedTags: result.stats.removedTags,
          reductionPercentage: reduction
        }

        // 生成AI分析
        this.aiAnalysis = this.generateAIDescription(this.cleanedHtml)

        // 初始化Monaco Editor
        this.$nextTick(() => {
          this.initMonacoEditor()
        })

        this.$message.success('HTML清理完成！')
      } catch (error) {
        console.error('清理失败:', error)
        this.$message.error('清理失败，请检查HTML格式')
      } finally {
        this.isProcessing = false
      }
    },

    // 执行HTML清理
    performHtmlClean(html, options = {}) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(html, 'text/html')

      let removedAttributes = 0
      let removedTags = 0

      // 1. 删除指定的标签
      this.removeTags.forEach(tag => {
        const elements = doc.querySelectorAll(tag)
        elements.forEach(el => {
          el.remove()
          removedTags++
        })
      })

      // 2. 清理属性
      const allElements = doc.querySelectorAll('*')
      allElements.forEach(element => {
        const attributes = Array.from(element.attributes)

        attributes.forEach(attr => {
          if (this.shouldRemoveAttribute(attr.name, attr.value)) {
            element.removeAttribute(attr.name)
            removedAttributes++
          }
        })
      })

      // 3. 删除空白标签（可选）
      if (options.removeEmptyTags) {
        this.removeEmptyElements(doc)
      }

      // 4. 删除注释
      if (options.removeComments) {
        this.removeComments(doc)
      }

      // 5. 格式化输出
      let result = doc.documentElement.outerHTML

      if (options.minify) {
        result = this.minifyHTML(result)
      }

      if (options.prettify) {
        result = this.prettifyHTML(result)
      }

      return {
        html: result,
        stats: {
          removedAttributes,
          removedTags
        }
      }
    },

    // 判断是否应该删除属性
    shouldRemoveAttribute(attrName, attrValue) {
      // 检查特定属性值是否需要删除
      if (this.removeSpecificValues[attrName]) {
        if (this.removeSpecificValues[attrName].includes(attrValue)) {
          return true
        }
      }

      // 保留重要属性
      if (this.keepAttributes.has(attrName)) {
        return false
      }

      // 检查默认删除模式
      for (const pattern of this.defaultRemovePatterns) {
        if (pattern.test(attrName)) {
          return true
        }
      }

      // 检查自定义删除模式
      for (const pattern of this.customRemovePatterns) {
        if (pattern.test(attrName)) {
          return true
        }
      }

      // 删除空属性
      if (!attrValue || attrValue.trim() === '') {
        return true
      }

      return false
    },

    // 删除空白元素
    removeEmptyElements(doc) {
      const emptyElements = doc.querySelectorAll('*')
      emptyElements.forEach(element => {
        const tagName = element.tagName.toLowerCase()

        // 跳过自闭合标签
        if (['img', 'br', 'hr', 'input', 'meta', 'link'].includes(tagName)) {
          return
        }

        // 如果元素为空且没有属性，则删除
        if (element.textContent.trim() === '' && element.children.length === 0 && element.attributes.length === 0) {
          element.remove()
        }
      })
    },

    // 删除注释
    removeComments(doc) {
      const walker = doc.createTreeWalker(doc.documentElement, NodeFilter.SHOW_COMMENT, null, false)

      const comments = []
      let node
      while ((node = walker.nextNode())) {
        comments.push(node)
      }

      comments.forEach(comment => comment.remove())
    },

    // 压缩HTML
    minifyHTML(html) {
      return html.replace(/>\s+</g, '><').replace(/\s+/g, ' ').trim()
    },

    // 美化HTML
    prettifyHTML(html) {
      let depth = 0
      const indent = '  '

      return html
        .replace(/></g, '>\n<')
        .split('\n')
        .map(line => {
          const trimmed = line.trim()
          if (!trimmed) return ''

          if (trimmed.startsWith('</')) {
            depth--
          }

          const result = indent.repeat(Math.max(0, depth)) + trimmed

          if (trimmed.startsWith('<') && !trimmed.startsWith('</') && !trimmed.endsWith('/>')) {
            depth++
          }

          return result
        })
        .join('\n')
    },

    // 生成AI友好的描述
    generateAIDescription(html) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(html, 'text/html')

      const description = {
        structure: [],
        formElements: [],
        links: [],
        images: [],
        text: doc.body ? doc.body.textContent.trim().substring(0, 500) + '...' : ''
      }

      // 分析结构
      const structureElements = doc.querySelectorAll('div, section, article, nav, header, footer, main')
      structureElements.forEach((element, index) => {
        if (index < 20) {
          // 限制数量
          description.structure.push({
            tag: element.tagName.toLowerCase(),
            classes: element.className || '',
            id: element.id || '',
            text: element.textContent.trim().substring(0, 100)
          })
        }
      })

      // 表单元素
      const formElements = doc.querySelectorAll('input, select, textarea, button')
      formElements.forEach((element, index) => {
        if (index < 10) {
          // 限制数量
          description.formElements.push({
            tag: element.tagName.toLowerCase(),
            type: element.type || '',
            name: element.name || '',
            placeholder: element.placeholder || '',
            text: element.textContent.trim()
          })
        }
      })

      // 链接
      const links = doc.querySelectorAll('a[href]')
      links.forEach((element, index) => {
        if (index < 10) {
          // 限制数量
          description.links.push({
            href: element.href,
            text: element.textContent.trim()
          })
        }
      })

      // 图片
      const images = doc.querySelectorAll('img')
      images.forEach((element, index) => {
        if (index < 10) {
          // 限制数量
          description.images.push({
            src: element.src,
            alt: element.alt || ''
          })
        }
      })

      return description
    },

    // 清空输入
    clearInput() {
      this.inputHtml = ''
      this.cleanedHtml = ''
      this.cleanStats = null
      this.aiAnalysis = null
    },

    // 复制结果
    async copyResult() {
      try {
        await navigator.clipboard.writeText(this.cleanedHtml)
        this.$message.success('已复制到剪贴板')
      } catch (error) {
        // 降级方案
        this.fallbackCopy()
      }
    },

    // 降级复制方案
    fallbackCopy() {
      try {
        const textArea = document.createElement('textarea')
        textArea.value = this.cleanedHtml
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()

        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)

        if (successful) {
          this.$message.success('已复制到剪贴板')
        } else {
          this.$message.error('复制失败，请手动复制')
        }
      } catch (err) {
        console.error('降级复制失败:', err)
        this.$message.error('复制失败，请手动复制')
      }
    },

    // 下载结果
    downloadResult() {
      const blob = new Blob([this.cleanedHtml], { type: 'text/html' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'cleaned.html'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      this.$message.success('文件下载完成')
    },

    // 格式化字节大小
    formatBytes(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    },

    // 初始化Monaco Editor
    async initMonacoEditor() {
      if (!this.cleanedHtml) return

      try {
        // 等待Monaco Editor加载
        await this.loadMonacoEditor()

        // 销毁现有编辑器
        if (this.monacoEditor) {
          this.monacoEditor.dispose()
        }

        // 创建新的编辑器实例
        this.monacoEditor = window.monaco.editor.create(this.$refs.monacoEditor, {
          value: this.cleanedHtml,
          language: 'html',
          theme: 'vs-dark',
          readOnly: false,
          automaticLayout: true,
          fontSize: 14,
          lineNumbers: 'on',
          roundedSelection: false,
          scrollBeyondLastLine: false,
          minimap: {
            enabled: true
          },
          folding: true,
          foldingStrategy: 'indentation',
          showFoldingControls: 'always',
          wordWrap: 'on',
          contextmenu: true,
          selectOnLineNumbers: true,
          glyphMargin: true,
          lineDecorationsWidth: 20,
          renderLineHighlight: 'all'
        })

        // 监听选择变化
        this.monacoEditor.onDidChangeCursorSelection(e => {
          const selection = this.monacoEditor.getModel().getValueInRange(e.selection)
          if (selection) {
            this.selectedElement = selection
          }
        })

        // 监听内容变化
        this.monacoEditor.onDidChangeModelContent(() => {
          this.cleanedHtml = this.monacoEditor.getValue()
        })
      } catch (error) {
        console.error('Monaco Editor 初始化失败:', error)
        this.$message.error('代码编辑器初始化失败')
      }
    },

    // 加载Monaco Editor
    async loadMonacoEditor() {
      return new Promise((resolve, reject) => {
        if (window.monaco) {
          resolve()
          return
        }

        if (window.require) {
          window.require(['vs/editor/editor.main'], () => {
            resolve()
          })
        } else {
          reject(new Error('Monaco Editor 未加载'))
        }
      })
    },

    // 选择DOM元素
    selectDomElement() {
      if (!this.monacoEditor) return

      const selection = this.monacoEditor.getSelection()
      const selectedText = this.monacoEditor.getModel().getValueInRange(selection)

      if (selectedText) {
        this.$message.success(`已选择: ${selectedText.substring(0, 50)}${selectedText.length > 50 ? '...' : ''}`)
        this.selectedElement = selectedText
      } else {
        this.$message.info('请先在编辑器中选择要操作的HTML元素')
      }
    },

    // DOM树查看器事件处理
    handleNodeSelect(node) {
      console.log('选择了节点:', node)
      this.selectedElement = node.element.outerHTML
      this.$message.success(`已选择元素: ${node.tagName}`)
    },

    handleNodeClick(node) {
      console.log('点击了节点:', node)
      // 可以在这里添加更多的节点点击处理逻辑
    }
  }
}
</script>

<style scoped>
@import '../../../../../assets/css/style.css';

.html-cleaner {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--card-bg-color);
}

.html-cleaner-header {
  padding: 1rem 0 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

.html-cleaner-header h3 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: 1.2rem;
  font-weight: 600;
}

.subtitle {
  color: var(--text-light);
  font-size: 0.9rem;
  margin: 0;
}

.cleaner-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.title-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: auto;
}

/* 输入区域 */
.input-section {
  margin-bottom: 1.5rem;
  background-color: var(--card-bg-color);
  border-radius: var(--radius);
  padding: 1rem;
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-color);
}

/* 选项区域 */
.options-section {
  margin-bottom: 1.5rem;
  background-color: var(--card-bg-color);
  border-radius: var(--radius);
  padding: 1rem;
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-color);
}

/* 统计数据 */
.stats-section {
  margin-bottom: 1.5rem;
  background-color: var(--card-bg-color);
  border-radius: var(--radius);
  padding: 1rem;
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-color);
}

/* 结果区域 */
.result-section {
  margin-bottom: 1.5rem;
  background-color: var(--card-bg-color);
  border-radius: var(--radius);
  padding: 1rem;
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-color);
}

/* AI分析区域 */
.ai-analysis-section {
  margin-bottom: 1.5rem;
  background-color: var(--card-bg-color);
  border-radius: var(--radius);
  padding: 1rem;
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-color);
}

.input-container {
  background-color: var(--background-color);
  border-radius: var(--radius);
  padding: 1rem;
  border: 1px solid var(--border-color);
  margin-top: 1rem;
}

.html-input {
  width: 100%;
  min-height: 200px;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: vertical;
  background-color: var(--card-bg-color);
  color: var(--text-color);
  transition: var(--transition);
}

.html-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.input-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  justify-content: space-between;
  align-items: center;
}

.options-inline {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
  background-color: var(--background-color);
  border-radius: var(--radius);
  padding: 1rem;
  border: 1px solid var(--border-color);
  margin-top: 1rem;
}

.option-item {
  display: flex;
  align-items: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.stat-item {
  background-color: var(--background-color);
  border-radius: var(--radius-sm);
  padding: 1rem;
  text-align: center;
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* .stat-item.reduction {
  background: linear-gradient(135deg, var(--success-color) 0%, #06d6a0 100%);
  color: white;
  border: none;
} */

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: var(--primary-color);
}

.stat-item.reduction .stat-value {
  color: white;
}

.stat-label {
  font-size: 0.8rem;
  opacity: 0.8;
  font-weight: 500;
  color: var(--text-light);
}

.stat-item.reduction .stat-label {
  color: white;
}

.result-tabs {
  background-color: var(--background-color);
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
  margin-top: 1rem;
  overflow: hidden;
}

.tab-headers {
  display: flex;
  background-color: var(--card-bg-color);
  border-bottom: 1px solid var(--border-color);
}

.tab-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  cursor: pointer;
  font-weight: 600;
  color: var(--text-light);
  transition: var(--transition);
  border-right: 1px solid var(--border-color);
}

.tab-header:last-child {
  border-right: none;
}

.tab-header.active {
  color: var(--primary-color);
  background-color: var(--card-bg-color);
  border-bottom: 1px solid var(--card-bg-color);
}

.tab-content {
  padding: 1rem;
}

.tab-panel {
  /* 移除 display: none 规则，因为我们使用 v-show 控制显示 */
}

.tab-panel.active {
  display: block;
}

.monaco-container {
  margin-bottom: 1rem;
}

.monaco-editor {
  border-radius: var(--radius);
  overflow: hidden;
}

.tree-container {
  height: 400px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  overflow: hidden;
}

.result-container {
  background-color: var(--background-color);
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
  max-height: 400px;
  overflow-y: auto;
}

.html-output {
  margin: 0;
  padding: 1rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85rem;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
  color: var(--text-color);
  background: transparent;
}

.analysis-content {
  background-color: var(--background-color);
  border-radius: var(--radius);
  padding: 1rem;
  border: 1px solid var(--border-color);
  margin-top: 1rem;
}

.analysis-item {
  margin-bottom: 1.5rem;
}

.analysis-item:last-child {
  margin-bottom: 0;
}

.analysis-item h4 {
  color: var(--primary-color);
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.structure-list,
.form-elements-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.structure-item,
.form-element-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background-color: var(--card-bg-color);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  font-size: 0.85rem;
}

.tag-name,
.element-tag {
  background: var(--primary-color);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: var(--radius-sm);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.element-type {
  background: var(--warning-color);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.75rem;
}

.tag-info,
.element-name {
  color: var(--text-light);
  font-size: 0.8rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-secondary {
  background: var(--text-light);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: var(--text-color);
  transform: translateY(-1px);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #06d6a0;
  transform: translateY(-1px);
}

.btn-info {
  background: var(--accent-color);
  color: white;
}

.btn-info:hover:not(:disabled) {
  background: var(--secondary-color);
  transform: translateY(-1px);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #f39c12;
  transform: translateY(-1px);
}

.btn-small {
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
}

/* 动画 */
.rotating {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 滚动条样式 */
.cleaner-content::-webkit-scrollbar,
.result-container::-webkit-scrollbar {
  width: 6px;
}

.cleaner-content::-webkit-scrollbar-track,
.result-container::-webkit-scrollbar-track {
  background: var(--background-color);
}

.cleaner-content::-webkit-scrollbar-thumb,
.result-container::-webkit-scrollbar-thumb {
  background-color: rgba(var(--primary-color-rgb), 0.2);
  border-radius: 3px;
}

.cleaner-content::-webkit-scrollbar-thumb:hover,
.result-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(var(--primary-color-rgb), 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cleaner-content {
    padding: 0.5rem;
    gap: 1rem;
  }

  .input-section,
  .options-section,
  .stats-section,
  .result-section,
  .ai-analysis-section {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .options-grid {
    grid-template-columns: 1fr;
  }

  .input-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .options-inline {
    justify-content: center;
    margin-bottom: 1rem;
  }

  .action-buttons {
    justify-content: center;
  }

  .title-actions {
    flex-direction: column;
    gap: 0.25rem;
  }
}
</style>
