<template>
  <div class="dom-ai-integrated">
    <div class="workflow-container">
      <!-- 工作流进度 -->
      <div class="workflow-steps">
        <div
          v-for="(step, index) in workflowSteps"
          :key="step.key"
          class="workflow-step"
          :class="{
            active: currentStep === step.key,
            completed: completedSteps.includes(step.key),
            disabled: !isStepEnabled(step.key)
          }"
          @click="switchStep(step.key)">
          <div class="step-icon">
            <i :class="step.icon"></i>
          </div>
          <div class="step-info">
            <div class="step-title">{{ step.title }}</div>
            <div class="step-desc">{{ step.description }}</div>
          </div>
        </div>
      </div>

      <!-- 主内容区 -->
      <div class="main-workspace">
        <!-- 步骤1: HTML输入与清理 -->
        <div v-show="currentStep === 'input'" class="step-content">
          <div class="content-header">
            <h3>HTML代码输入与清理</h3>
            <p>输入原始HTML代码，系统将自动清理无用标签和属性</p>
          </div>

          <div class="input-section">
            <div class="input-group">
              <label>输入HTML代码：</label>
              <textarea v-model="rawHtml" class="html-textarea" placeholder="请粘贴需要分析的HTML代码..." rows="12"></textarea>
            </div>

            <div class="clean-options">
              <div class="options-title">清理选项：</div>
              <div class="options-grid">
                <el-checkbox v-model="cleanOptions.removeComments">移除注释</el-checkbox>
                <el-checkbox v-model="cleanOptions.removeScript">移除脚本</el-checkbox>
                <el-checkbox v-model="cleanOptions.removeStyle">移除样式</el-checkbox>
                <el-checkbox v-model="cleanOptions.removeEmptyTags">移除空标签</el-checkbox>
                <el-checkbox v-model="cleanOptions.simplifyClasses">简化类名</el-checkbox>
                <el-checkbox v-model="cleanOptions.removeDataAttrs">移除数据属性</el-checkbox>
              </div>
            </div>

            <div class="action-buttons">
              <el-button type="primary" @click="cleanAndAnalyze" :loading="isProcessing" size="medium">
                <i class="el-icon-refresh"></i>
                清理并分析HTML
              </el-button>
              <el-button @click="clearAll" size="medium">清空重置</el-button>
            </div>
          </div>

          <!-- 清理结果预览 -->
          <div v-if="cleanedHtml" class="result-preview">
            <div class="preview-header">
              <h4>清理结果</h4>
              <div class="stats">
                <span>原始大小: {{ formatBytes(rawHtml.length) }}</span>
                <span>清理后: {{ formatBytes(cleanedHtml.length) }}</span>
                <span class="reduction">压缩率: {{ compressionRatio }}%</span>
              </div>
            </div>
            <div class="preview-content">
              <pre><code>{{ cleanedHtml.substring(0, 500) }}{{ cleanedHtml.length > 500 ? '...' : '' }}</code></pre>
            </div>
          </div>
        </div>

        <!-- 步骤2: 元素选择 -->
        <div v-show="currentStep === 'select'" class="step-content">
          <div class="content-header">
            <h3>智能元素选择</h3>
            <p>从清理后的HTML中选择需要测试的关键元素</p>
          </div>

          <!-- CSS选择器输入 -->
          <div class="selector-section">
            <div class="selector-input-group">
              <el-input v-model="cssSelector" placeholder="输入CSS选择器，如: .btn, #submit, input[type='text']" @input="onSelectorChange" @keyup.enter.native="executeSelector" class="selector-input"></el-input>
              <el-button type="primary" @click="executeSelector" :loading="isSelecting">
                <i class="el-icon-search"></i>
                查找元素
              </el-button>
            </div>

            <!-- 快捷选择器 -->
            <div class="quick-selectors">
              <span class="quick-label">常用选择器：</span>
              <el-button-group>
                <el-button v-for="quick in quickSelectors" :key="quick.value" size="small" @click="useQuickSelector(quick.value)" :title="quick.description">
                  {{ quick.label }}
                </el-button>
              </el-button-group>
            </div>
          </div>

          <!-- 匹配的元素列表 -->
          <div v-if="selectedElements.length > 0" class="elements-list">
            <div class="list-header">
              <h4>匹配的元素 ({{ selectedElements.length }}个)</h4>
              <div class="list-actions">
                <el-button size="small" @click="selectAllElements">全选</el-button>
                <el-button size="small" @click="clearSelection">清空选择</el-button>
              </div>
            </div>

            <div class="elements-grid">
              <div v-for="(element, index) in selectedElements" :key="index" class="element-card" :class="{ selected: checkedElements.includes(element) }" @click="toggleElement(element)">
                <div class="element-header">
                  <el-checkbox :value="checkedElements.includes(element)" @change="toggleElement(element)" @click.stop></el-checkbox>
                  <el-tag :type="getElementTypeColor(element.tagName)" size="small">
                    {{ element.tagName.toUpperCase() }}
                  </el-tag>
                  <span class="element-text">{{ getElementDisplayText(element) }}</span>
                </div>

                <div class="element-details">
                  <div v-if="element.id" class="detail-item">
                    <span class="label">ID:</span>
                    <code>{{ element.id }}</code>
                  </div>
                  <div v-if="element.className" class="detail-item">
                    <span class="label">类名:</span>
                    <code>{{ element.className }}</code>
                  </div>
                  <div class="detail-item">
                    <span class="label">选择器:</span>
                    <code>{{ generateBestSelector(element) }}</code>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="action-buttons">
            <el-button type="primary" @click="proceedToAI" :disabled="checkedElements.length === 0" size="medium">
              <i class="el-icon-cpu"></i>
              进入AI分析 ({{ checkedElements.length }}个元素)
            </el-button>
          </div>
        </div>

        <!-- 步骤3: AI分析 -->
        <div v-show="currentStep === 'analyze'" class="step-content">
          <div class="content-header">
            <h3>AI智能分析</h3>
            <p>结合AI大模型分析页面结构和测试需求</p>
          </div>

          <div class="ai-config-section">
            <div class="config-row">
              <div class="config-group">
                <label>测试场景描述：</label>
                <el-input v-model="testScenario" type="textarea" :rows="3" placeholder="请描述你想要测试的功能场景，例如：用户登录流程、商品搜索、表单提交等..." class="scenario-input"></el-input>
              </div>
            </div>

            <div class="config-row">
              <div class="config-group">
                <label>测试框架：</label>
                <el-select v-model="testFramework" style="width: 150px">
                  <el-option label="Playwright" value="playwright"></el-option>
                  <el-option label="Selenium" value="selenium"></el-option>
                  <el-option label="Cypress" value="cypress"></el-option>
                </el-select>
              </div>

              <div class="config-group">
                <label>编程语言：</label>
                <el-select v-model="testLanguage" style="width: 150px">
                  <el-option label="JavaScript" value="javascript"></el-option>
                  <el-option label="Python" value="python"></el-option>
                  <el-option label="Java" value="java"></el-option>
                </el-select>
              </div>

              <div class="config-group">
                <label>测试类型：</label>
                <el-select v-model="testType" style="width: 150px">
                  <el-option label="功能测试" value="functional"></el-option>
                  <el-option label="UI测试" value="ui"></el-option>
                  <el-option label="回归测试" value="regression"></el-option>
                </el-select>
              </div>
            </div>
          </div>

          <!-- 选中元素预览 -->
          <div class="selected-elements-preview">
            <h4>已选择的元素 ({{ checkedElements.length }}个)</h4>
            <div class="elements-summary">
              <div v-for="(element, index) in checkedElements" :key="index" class="element-summary">
                <el-tag :type="getElementTypeColor(element.tagName)" size="mini">
                  {{ element.tagName }}
                </el-tag>
                <span>{{ getElementDisplayText(element) }}</span>
                <code>{{ generateBestSelector(element) }}</code>
              </div>
            </div>
          </div>

          <div class="action-buttons">
            <el-button type="primary" @click="generateWithAI" :loading="isGenerating" size="medium">
              <i class="el-icon-magic-stick"></i>
              {{ isGenerating ? 'AI生成中...' : '生成测试代码' }}
            </el-button>
            <el-button @click="previewContext" size="medium">
              <i class="el-icon-view"></i>
              预览上下文
            </el-button>
          </div>
        </div>

        <!-- 步骤4: 生成结果 -->
        <div v-show="currentStep === 'result'" class="step-content">
          <div class="content-header">
            <h3>生成的测试代码</h3>
            <p>AI根据您的需求生成的自动化测试代码</p>
          </div>

          <div v-if="generatedCode" class="result-section">
            <div class="result-header">
              <div class="result-title">
                <h4>{{ testFramework }} {{ testLanguage }} 测试代码</h4>
                <div class="result-meta">
                  <span>元素数量: {{ checkedElements.length }}</span>
                  <span>代码行数: {{ generatedCode.split('\n').length }}</span>
                  <span>生成时间: {{ new Date().toLocaleString() }}</span>
                </div>
              </div>
              <div class="result-actions">
                <el-button size="small" @click="copyCode">
                  <i class="el-icon-document-copy"></i>
                  复制代码
                </el-button>
                <el-button size="small" @click="downloadCode">
                  <i class="el-icon-download"></i>
                  下载文件
                </el-button>
                <el-button size="small" @click="optimizeCode" :loading="isOptimizing">
                  <i class="el-icon-magic-stick"></i>
                  优化代码
                </el-button>
              </div>
            </div>

            <div class="code-editor">
              <pre><code class="language-javascript">{{ generatedCode }}</code></pre>
            </div>
          </div>

          <!-- AI分析建议 -->
          <div v-if="aiSuggestions" class="ai-suggestions">
            <h4>AI分析建议</h4>
            <div class="suggestions-list">
              <div v-for="(suggestion, index) in aiSuggestions" :key="index" class="suggestion-item">
                <div class="suggestion-type">{{ suggestion.type }}</div>
                <div class="suggestion-content">{{ suggestion.content }}</div>
              </div>
            </div>
          </div>

          <div class="action-buttons">
            <el-button type="primary" @click="regenerateCode" size="medium">
              <i class="el-icon-refresh"></i>
              重新生成
            </el-button>
            <el-button @click="startNewAnalysis" size="medium">
              <i class="el-icon-plus"></i>
              新建分析
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 上下文预览对话框 -->
    <el-dialog title="AI上下文预览" :visible.sync="showContextDialog" width="60%">
      <div class="context-preview">
        <h4>将发送给AI的上下文信息：</h4>
        <pre class="context-content">{{ aiContext }}</pre>
      </div>
      <div slot="footer">
        <el-button @click="showContextDialog = false">关闭</el-button>
        <el-button type="primary" @click="copyContext">复制上下文</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DomAIIntegrated',
  data() {
    return {
      // 工作流状态
      currentStep: 'input',
      completedSteps: [],
      workflowSteps: [
        {
          key: 'input',
          title: '输入清理',
          description: 'HTML输入与清理',
          icon: 'el-icon-edit'
        },
        {
          key: 'select',
          title: '元素选择',
          description: '选择测试元素',
          icon: 'el-icon-search'
        },
        {
          key: 'analyze',
          title: 'AI分析',
          description: 'AI智能分析',
          icon: 'el-icon-cpu'
        },
        {
          key: 'result',
          title: '生成结果',
          description: '测试代码生成',
          icon: 'el-icon-document'
        }
      ],

      // HTML清理相关
      rawHtml: '',
      cleanedHtml: '',
      cleanOptions: {
        removeComments: true,
        removeScript: true,
        removeStyle: true,
        removeEmptyTags: true,
        simplifyClasses: false,
        removeDataAttrs: true
      },
      isProcessing: false,

      // 元素选择相关
      cssSelector: '',
      selectedElements: [],
      checkedElements: [],
      isSelecting: false,
      quickSelectors: [
        { label: 'button', value: 'button', description: '所有按钮' },
        { label: 'input', value: 'input', description: '所有输入框' },
        { label: 'a', value: 'a', description: '所有链接' },
        { label: '.btn', value: '.btn', description: '按钮类' },
        { label: 'form', value: 'form', description: '表单元素' },
        { label: '[data-test]', value: '[data-test]', description: '测试属性' }
      ],

      // AI分析相关
      testScenario: '',
      testFramework: 'playwright',
      testLanguage: 'javascript',
      testType: 'functional',
      aiContext: '',
      showContextDialog: false,
      isGenerating: false,

      // 结果相关
      generatedCode: '',
      aiSuggestions: null,
      isOptimizing: false
    }
  },

  computed: {
    compressionRatio() {
      if (!this.rawHtml || !this.cleanedHtml) return 0
      return Math.round((1 - this.cleanedHtml.length / this.rawHtml.length) * 100)
    }
  },

  methods: {
    // 工作流控制
    isStepEnabled(step) {
      const stepIndex = this.workflowSteps.findIndex(s => s.key === step)
      const currentIndex = this.workflowSteps.findIndex(s => s.key === this.currentStep)
      return stepIndex <= currentIndex + 1
    },

    switchStep(step) {
      if (this.isStepEnabled(step)) {
        this.currentStep = step
      }
    },

    completeStep(step) {
      if (!this.completedSteps.includes(step)) {
        this.completedSteps.push(step)
      }
    },

    // HTML清理
    async cleanAndAnalyze() {
      if (!this.rawHtml.trim()) {
        this.$message.warning('请输入HTML代码')
        return
      }

      this.isProcessing = true
      try {
        // 模拟清理过程
        await new Promise(resolve => setTimeout(resolve, 1000))

        this.cleanedHtml = this.performHtmlClean(this.rawHtml)
        this.completeStep('input')

        // 自动解析元素
        this.parseElements()

        this.$message.success('HTML清理完成')
        this.currentStep = 'select'
      } catch (error) {
        this.$message.error('清理失败: ' + error.message)
      } finally {
        this.isProcessing = false
      }
    },

    performHtmlClean(html) {
      let cleaned = html

      if (this.cleanOptions.removeComments) {
        cleaned = cleaned.replace(/<!--[\s\S]*?-->/g, '')
      }
      if (this.cleanOptions.removeScript) {
        cleaned = cleaned.replace(/<script[\s\S]*?<\/script>/gi, '')
      }
      if (this.cleanOptions.removeStyle) {
        cleaned = cleaned.replace(/<style[\s\S]*?<\/style>/gi, '')
        cleaned = cleaned.replace(/style\s*=\s*"[^"]*"/gi, '')
      }
      if (this.cleanOptions.removeEmptyTags) {
        cleaned = cleaned.replace(/<(\w+)[^>]*>\s*<\/\1>/g, '')
      }
      if (this.cleanOptions.removeDataAttrs) {
        cleaned = cleaned.replace(/data-[a-zA-Z0-9-_]*\s*=\s*"[^"]*"/gi, '')
      }

      return cleaned.trim()
    },

    // 元素解析
    parseElements() {
      try {
        const parser = new DOMParser()
        const doc = parser.parseFromString(this.cleanedHtml, 'text/html')

        const elements = []
        const interactiveSelectors = 'input, button, a, select, textarea, [role="button"], [onclick]'
        const nodes = doc.querySelectorAll(interactiveSelectors)

        nodes.forEach(node => {
          elements.push(this.elementToObject(node))
        })

        this.selectedElements = elements
        this.$message.success(`找到 ${elements.length} 个可交互元素`)
      } catch (error) {
        console.error('元素解析失败:', error)
        this.$message.error('元素解析失败')
      }
    },

    elementToObject(element) {
      return {
        tagName: element.tagName.toLowerCase(),
        id: element.id || '',
        className: element.className || '',
        textContent: element.textContent?.trim() || '',
        placeholder: element.placeholder || '',
        type: element.type || '',
        name: element.name || '',
        attributes: this.getElementAttributes(element)
      }
    },

    getElementAttributes(element) {
      const attrs = {}
      Array.from(element.attributes).forEach(attr => {
        attrs[attr.name] = attr.value
      })
      return attrs
    },

    // CSS选择器
    onSelectorChange() {
      clearTimeout(this.selectorTimeout)
      this.selectorTimeout = setTimeout(() => {
        if (this.cssSelector.trim()) {
          this.executeSelector()
        }
      }, 500)
    },

    async executeSelector() {
      if (!this.cssSelector.trim()) return

      this.isSelecting = true
      try {
        const parser = new DOMParser()
        const doc = parser.parseFromString(this.cleanedHtml, 'text/html')
        const nodes = doc.querySelectorAll(this.cssSelector)

        const elements = Array.from(nodes).map(node => this.elementToObject(node))
        this.selectedElements = elements

        this.$message.success(`找到 ${elements.length} 个匹配元素`)
      } catch (error) {
        this.$message.error('选择器语法错误')
      } finally {
        this.isSelecting = false
      }
    },

    useQuickSelector(selector) {
      this.cssSelector = selector
      this.executeSelector()
    },

    // 元素选择
    toggleElement(element) {
      const index = this.checkedElements.findIndex(el => el.tagName === element.tagName && el.id === element.id && el.className === element.className)

      if (index >= 0) {
        this.checkedElements.splice(index, 1)
      } else {
        this.checkedElements.push(element)
      }
    },

    selectAllElements() {
      this.checkedElements = [...this.selectedElements]
    },

    clearSelection() {
      this.checkedElements = []
    },

    proceedToAI() {
      if (this.checkedElements.length === 0) {
        this.$message.warning('请至少选择一个元素')
        return
      }

      this.completeStep('select')
      this.currentStep = 'analyze'
    },

    // AI分析
    generateAIContext() {
      const context = {
        testScenario: this.testScenario,
        framework: this.testFramework,
        language: this.testLanguage,
        testType: this.testType,
        elements: this.checkedElements.map(el => ({
          type: el.tagName,
          selector: this.generateBestSelector(el),
          text: this.getElementDisplayText(el),
          attributes: el.attributes
        })),
        pageStructure: this.generatePageStructure()
      }

      return JSON.stringify(context, null, 2)
    },

    generatePageStructure() {
      return `页面包含 ${this.checkedElements.length} 个可交互元素，主要包括：
${this.checkedElements.map(el => `- ${el.tagName}: ${this.getElementDisplayText(el)}`).join('\n')}`
    },

    async generateWithAI() {
      if (!this.testScenario.trim()) {
        this.$message.warning('请描述测试场景')
        return
      }

      this.isGenerating = true
      try {
        // 生成AI上下文
        this.aiContext = this.generateAIContext()

        // 模拟AI生成过程
        await new Promise(resolve => setTimeout(resolve, 2000))

        this.generatedCode = this.generateMockCode()
        this.aiSuggestions = this.generateMockSuggestions()

        this.completeStep('analyze')
        this.currentStep = 'result'

        this.$message.success('测试代码生成完成')
      } catch (error) {
        this.$message.error('生成失败: ' + error.message)
      } finally {
        this.isGenerating = false
      }
    },

    generateMockCode() {
      const elements = this.checkedElements
      let code = ''

      if (this.testFramework === 'playwright') {
        code = `const { test, expect } = require('@playwright/test');

test('${this.testScenario}', async ({ page }) => {
  // 导航到页面
  await page.goto('YOUR_PAGE_URL');
  
${elements
  .map(el => {
    const selector = this.generateBestSelector(el)
    const text = this.getElementDisplayText(el)

    if (el.tagName === 'input') {
      return `  // 填写输入框: ${text}\n  await page.fill('${selector}', 'test_value');`
    } else if (el.tagName === 'button') {
      return `  // 点击按钮: ${text}\n  await page.click('${selector}');`
    } else if (el.tagName === 'a') {
      return `  // 点击链接: ${text}\n  await page.click('${selector}');`
    } else {
      return `  // 操作元素: ${text}\n  await page.click('${selector}');`
    }
  })
  .join('\n\n')}

  // 验证结果
  await expect(page).toHaveTitle(/.*$/);
});`
      }

      return code
    },

    generateMockSuggestions() {
      return [
        {
          type: '性能优化',
          content: '建议添加元素等待逻辑，确保页面完全加载后再进行操作'
        },
        {
          type: '稳定性提升',
          content: '推荐使用更稳定的选择器，如data-testid属性'
        },
        {
          type: '测试覆盖',
          content: '可以添加更多断言来验证操作结果'
        }
      ]
    },

    previewContext() {
      this.aiContext = this.generateAIContext()
      this.showContextDialog = true
    },

    // 辅助方法
    getElementTypeColor(tagName) {
      const colors = {
        input: 'primary',
        button: 'success',
        a: 'info',
        select: 'warning',
        textarea: 'primary'
      }
      return colors[tagName] || ''
    },

    getElementDisplayText(element) {
      return element.textContent || element.placeholder || element.name || element.id || `${element.tagName}元素`
    },

    generateBestSelector(element) {
      if (element.id) return `#${element.id}`
      if (element.name) return `[name="${element.name}"]`
      if (element.className) {
        const firstClass = element.className.split(' ')[0]
        return `.${firstClass}`
      }
      return element.tagName
    },

    formatBytes(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    },

    // 操作方法
    async copyCode() {
      try {
        await navigator.clipboard.writeText(this.generatedCode)
        this.$message.success('代码已复制到剪贴板')
      } catch (error) {
        this.$message.error('复制失败')
      }
    },

    async copyContext() {
      try {
        await navigator.clipboard.writeText(this.aiContext)
        this.$message.success('上下文已复制到剪贴板')
        this.showContextDialog = false
      } catch (error) {
        this.$message.error('复制失败')
      }
    },

    downloadCode() {
      const ext = this.testLanguage === 'javascript' ? 'js' : this.testLanguage === 'python' ? 'py' : 'java'
      const filename = `test_${Date.now()}.${ext}`

      const blob = new Blob([this.generatedCode], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      a.click()
      URL.revokeObjectURL(url)

      this.$message.success('文件下载完成')
    },

    async optimizeCode() {
      this.isOptimizing = true
      try {
        await new Promise(resolve => setTimeout(resolve, 1500))
        // 模拟代码优化
        this.generatedCode = this.generatedCode.replace(/test_value/g, 'optimized_value')
        this.$message.success('代码优化完成')
      } finally {
        this.isOptimizing = false
      }
    },

    regenerateCode() {
      this.generateWithAI()
    },

    startNewAnalysis() {
      this.currentStep = 'input'
      this.completedSteps = []
      this.clearAll()
    },

    clearAll() {
      this.rawHtml = ''
      this.cleanedHtml = ''
      this.selectedElements = []
      this.checkedElements = []
      this.cssSelector = ''
      this.testScenario = ''
      this.generatedCode = ''
      this.aiSuggestions = null
    }
  }
}
</script>

<style scoped>
.dom-ai-integrated {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
}

.workflow-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1.5rem;
}

/* 工作流步骤 */
.workflow-steps {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: var(--card-bg-color);
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.workflow-step {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: var(--transition);
  border: 2px solid transparent;
}

.workflow-step.active {
  background: rgba(var(--primary-color-rgb), 0.1);
  border-color: var(--primary-color);
}

.workflow-step.completed {
  background: rgba(var(--success-color-rgb), 0.1);
  border-color: var(--success-color);
}

.workflow-step.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  color: white;
  font-size: 1.2rem;
}

.workflow-step.completed .step-icon {
  background: var(--success-color);
}

.step-info {
  flex: 1;
}

.step-title {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.step-desc {
  font-size: 0.85rem;
  color: var(--text-light);
}

/* 主工作区 */
.main-workspace {
  flex: 1;
  background: var(--card-bg-color);
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
  overflow-y: auto;
}

.step-content {
  padding: 1.5rem;
}

.content-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.content-header h3 {
  margin: 0 0 0.5rem 0;
  color: var(--primary-color);
  font-size: 1.3rem;
}

.content-header p {
  margin: 0;
  color: var(--text-light);
  font-size: 0.95rem;
}

/* 输入区域 */
.input-section {
  margin-bottom: 1.5rem;
}

.input-group {
  margin-bottom: 1rem;
}

.input-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.html-textarea {
  width: 100%;
  min-height: 200px;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: vertical;
  background: var(--background-color);
  color: var(--text-color);
}

.clean-options {
  margin-bottom: 1rem;
}

.options-title {
  margin-bottom: 0.75rem;
  font-weight: 500;
  color: var(--text-color);
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

/* 结果预览 */
.result-preview {
  margin-top: 1.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: var(--background-color);
  border-bottom: 1px solid var(--border-color);
}

.preview-header h4 {
  margin: 0;
  color: var(--text-color);
}

.stats {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: var(--text-light);
}

.reduction {
  color: var(--success-color);
  font-weight: 600;
}

.preview-content {
  padding: 1rem;
}

.preview-content pre {
  margin: 0;
  background: var(--background-color);
  padding: 1rem;
  border-radius: var(--radius-sm);
  overflow-x: auto;
  font-size: 0.85rem;
}

/* 选择器区域 */
.selector-section {
  margin-bottom: 1.5rem;
}

.selector-input-group {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.selector-input {
  flex: 1;
}

.quick-selectors {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.quick-label {
  font-size: 0.9rem;
  color: var(--text-light);
  white-space: nowrap;
}

/* 元素列表 */
.elements-list {
  margin-top: 1.5rem;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.list-header h4 {
  margin: 0;
  color: var(--text-color);
}

.list-actions {
  display: flex;
  gap: 0.5rem;
}

.elements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.element-card {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: 1rem;
  background: var(--background-color);
  cursor: pointer;
  transition: var(--transition);
}

.element-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.element-card.selected {
  border-color: var(--primary-color);
  background: rgba(var(--primary-color-rgb), 0.05);
}

.element-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.element-text {
  flex: 1;
  font-weight: 500;
  color: var(--text-color);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.element-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.detail-item .label {
  color: var(--text-light);
  min-width: 60px;
}

.detail-item code {
  background: var(--card-bg-color);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  flex: 1;
}

/* AI配置区域 */
.ai-config-section {
  margin-bottom: 1.5rem;
}

.config-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.config-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.config-group label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.9rem;
}

.scenario-input {
  width: 100%;
  min-width: 400px;
}

/* 选中元素预览 */
.selected-elements-preview {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.selected-elements-preview h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
}

.elements-summary {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.element-summary {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: var(--card-bg-color);
  border-radius: var(--radius-sm);
  font-size: 0.9rem;
}

.element-summary code {
  background: var(--background-color);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
}

/* 结果区域 */
.result-section {
  margin-bottom: 1.5rem;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.result-title h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
}

.result-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: var(--text-light);
}

.result-actions {
  display: flex;
  gap: 0.5rem;
}

.code-editor {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.code-editor pre {
  margin: 0;
  padding: 1rem;
  background: var(--background-color);
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* AI建议 */
.ai-suggestions {
  margin-top: 1.5rem;
  padding: 1rem;
  background: var(--background-color);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.ai-suggestions h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.suggestion-item {
  padding: 0.75rem;
  background: var(--card-bg-color);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--primary-color);
}

.suggestion-type {
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.suggestion-content {
  color: var(--text-color);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* 上下文预览 */
.context-preview {
  max-height: 60vh;
  overflow-y: auto;
}

.context-preview h4 {
  margin: 0 0 1rem 0;
  color: var(--text-color);
}

.context-content {
  background: var(--background-color);
  padding: 1rem;
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .workflow-steps {
    flex-direction: column;
    gap: 0.5rem;
  }

  .workflow-step {
    padding: 0.5rem;
  }

  .elements-grid {
    grid-template-columns: 1fr;
  }

  .config-row {
    flex-direction: column;
  }

  .result-header {
    flex-direction: column;
    gap: 1rem;
  }

  .scenario-input {
    min-width: auto;
  }
}
</style>
