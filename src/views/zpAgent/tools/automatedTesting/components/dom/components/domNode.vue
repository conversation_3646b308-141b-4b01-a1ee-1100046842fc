<template>
  <div class="dom-node" :class="{ 'is-selected': isSelected, 'is-highlighted': isHighlighted }">
    <!-- 节点内容 -->
    <div class="node-content" :style="{ paddingLeft: `${level * 20}px` }" @click="handleNodeClick">
      <!-- 展开/收起按钮 -->
      <span class="expand-btn" @click.stop="toggleExpand" v-if="node.children.length > 0">
        <i class="ri-arrow-right-s-line" :class="{ expanded: node.expanded }"></i>
      </span>
      <span class="expand-placeholder" v-else></span>

      <!-- 节点图标 -->
      <span class="node-icon">
        <i :class="getNodeIcon(node.tagName)"></i>
      </span>

      <!-- 节点标签 -->
      <span class="node-tag" :class="getTagClass(node.tagName)">
        {{ node.tagName }}
      </span>

      <!-- 节点属性 -->
      <span class="node-attributes">
        <span v-if="node.id" class="attribute id-attr">#{{ node.id }}</span>
        <span v-if="node.className" class="attribute class-attr">.{{ node.className.split(' ').join('.') }}</span>
        <span v-if="getImportantAttributes(node.attributes).length > 0" class="attribute other-attr">
          {{ getImportantAttributes(node.attributes).join(' ') }}
        </span>
      </span>

      <!-- 节点文本内容 -->
      <span v-if="node.textContent && !hasChildren" class="node-text">"{{ truncateText(node.textContent, 30) }}"</span>

      <!-- 子元素数量 -->
      <span v-if="node.children.length > 0" class="children-count">({{ node.children.length }})</span>

      <!-- 操作按钮 -->
      <div class="node-actions">
        <button class="action-btn" @click.stop="handleSelect" title="选择此元素">
          <i class="ri-cursor-line"></i>
        </button>
        <button class="action-btn" @click.stop="copySelector" title="复制选择器">
          <i class="ri-file-copy-line"></i>
        </button>
        <button class="action-btn" @click.stop="highlightInPreview" title="在预览中高亮">
          <i class="ri-focus-3-line"></i>
        </button>
      </div>
    </div>

    <!-- 子节点 -->
    <div class="node-children" v-if="node.expanded && node.children.length > 0">
      <dom-node v-for="child in node.children" :key="child.id" :node="child" :level="level + 1" :search-keyword="searchKeyword" :selected-node="selectedNode" @node-click="$emit('node-click', $event)" @node-select="$emit('node-select', $event)" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'DomNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 0
    },
    searchKeyword: {
      type: String,
      default: ''
    },
    selectedNode: {
      type: Object,
      default: null
    }
  },
  computed: {
    isSelected() {
      return this.selectedNode && this.selectedNode.id === this.node.id
    },
    isHighlighted() {
      if (!this.searchKeyword) return false
      const keyword = this.searchKeyword.toLowerCase()
      return this.node.tagName.includes(keyword) || this.node.className.toLowerCase().includes(keyword) || this.node.id.toLowerCase().includes(keyword) || this.node.textContent.toLowerCase().includes(keyword)
    },
    hasChildren() {
      return this.node.children.length > 0
    }
  },
  methods: {
    // 切换展开/收起
    toggleExpand() {
      this.node.expanded = !this.node.expanded
    },

    // 处理节点点击
    handleNodeClick() {
      this.$emit('node-click', this.node)
    },

    // 处理节点选择
    handleSelect() {
      this.$emit('node-select', this.node)
    },

    // 获取节点图标
    getNodeIcon(tagName) {
      const iconMap = {
        div: 'ri-layout-4-line',
        span: 'ri-text',
        p: 'ri-paragraph',
        h1: 'ri-h-1',
        h2: 'ri-h-2',
        h3: 'ri-h-3',
        h4: 'ri-h-4',
        h5: 'ri-h-5',
        h6: 'ri-h-6',
        a: 'ri-link',
        img: 'ri-image-line',
        button: 'ri-checkbox-blank-line',
        input: 'ri-input-cursor-move',
        textarea: 'ri-file-text-line',
        select: 'ri-arrow-down-s-line',
        form: 'ri-file-list-3-line',
        table: 'ri-table-line',
        tr: 'ri-layout-row-line',
        td: 'ri-layout-column-line',
        th: 'ri-layout-column-line',
        ul: 'ri-list-unordered',
        ol: 'ri-list-ordered',
        li: 'ri-record-circle-line',
        nav: 'ri-navigation-line',
        header: 'ri-layout-top-line',
        footer: 'ri-layout-bottom-line',
        main: 'ri-layout-3-line',
        section: 'ri-layout-2-line',
        article: 'ri-article-line',
        aside: 'ri-layout-left-line',
        video: 'ri-video-line',
        audio: 'ri-volume-up-line',
        canvas: 'ri-artboard-line',
        svg: 'ri-shape-line',
        script: 'ri-code-s-slash-line',
        style: 'ri-css3-line',
        link: 'ri-link-m',
        meta: 'ri-information-line'
      }
      return iconMap[tagName] || 'ri-markup-line'
    },

    // 获取标签样式类
    getTagClass(tagName) {
      const classMap = {
        div: 'tag-container',
        span: 'tag-inline',
        p: 'tag-text',
        h1: 'tag-heading',
        h2: 'tag-heading',
        h3: 'tag-heading',
        h4: 'tag-heading',
        h5: 'tag-heading',
        h6: 'tag-heading',
        a: 'tag-link',
        img: 'tag-media',
        video: 'tag-media',
        audio: 'tag-media',
        button: 'tag-interactive',
        input: 'tag-interactive',
        textarea: 'tag-interactive',
        select: 'tag-interactive',
        form: 'tag-form',
        table: 'tag-table',
        tr: 'tag-table',
        td: 'tag-table',
        th: 'tag-table',
        ul: 'tag-list',
        ol: 'tag-list',
        li: 'tag-list',
        nav: 'tag-semantic',
        header: 'tag-semantic',
        footer: 'tag-semantic',
        main: 'tag-semantic',
        section: 'tag-semantic',
        article: 'tag-semantic',
        aside: 'tag-semantic',
        script: 'tag-script',
        style: 'tag-script',
        link: 'tag-script',
        meta: 'tag-script'
      }
      return classMap[tagName] || 'tag-default'
    },

    // 获取重要属性
    getImportantAttributes(attributes) {
      const important = []
      const importantAttrs = ['type', 'name', 'href', 'src', 'alt', 'title', 'placeholder', 'value']

      for (const attr of importantAttrs) {
        if (attributes[attr]) {
          important.push(`${attr}="${attributes[attr]}"`)
        }
      }

      return important.slice(0, 2) // 最多显示2个属性
    },

    // 截断文本
    truncateText(text, maxLength) {
      if (!text) return ''
      text = text.trim()
      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
    },

    // 复制选择器
    async copySelector() {
      try {
        await navigator.clipboard.writeText(this.node.selector)
        this.$message.success('选择器已复制')
      } catch (error) {
        this.fallbackCopy(this.node.selector)
      }
    },

    // 在预览中高亮
    highlightInPreview() {
      this.$emit('highlight-in-preview', this.node)
    },

    // 降级复制方案
    fallbackCopy(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        document.execCommand('copy')
        this.$message.success('已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败')
      }

      document.body.removeChild(textArea)
    }
  }
}
</script>

<style scoped>
.dom-node {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.node-content {
  display: flex;
  align-items: center;
  padding: 2px 4px;
  cursor: pointer;
  border-radius: 3px;
  transition: all 0.2s ease;
  position: relative;
}

.node-content:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.node-content:hover .node-actions {
  opacity: 1;
}

.is-selected .node-content {
  background-color: rgba(var(--primary-color-rgb), 0.2);
  border-left: 3px solid var(--primary-color);
}

.is-highlighted .node-content {
  background-color: rgba(var(--warning-color-rgb), 0.2);
}

/* 展开按钮 */
.expand-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 4px;
  cursor: pointer;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.expand-btn:hover {
  background-color: rgba(var(--text-color-rgb), 0.1);
}

.expand-btn i {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.expand-btn i.expanded {
  transform: rotate(90deg);
}

.expand-placeholder {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

/* 节点图标 */
.node-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  color: var(--text-light);
}

.node-icon i {
  font-size: 12px;
}

/* 节点标签 */
.node-tag {
  font-weight: 600;
  margin-right: 6px;
  color: var(--text-color);
}

.node-tag.tag-container {
  color: #e74c3c;
}
.node-tag.tag-inline {
  color: #3498db;
}
.node-tag.tag-text {
  color: #2ecc71;
}
.node-tag.tag-heading {
  color: #9b59b6;
}
.node-tag.tag-link {
  color: #1abc9c;
}
.node-tag.tag-media {
  color: #f39c12;
}
.node-tag.tag-interactive {
  color: #e67e22;
}
.node-tag.tag-form {
  color: #34495e;
}
.node-tag.tag-table {
  color: #95a5a6;
}
.node-tag.tag-list {
  color: #16a085;
}
.node-tag.tag-semantic {
  color: #8e44ad;
}
.node-tag.tag-script {
  color: #7f8c8d;
}
.node-tag.tag-default {
  color: var(--text-color);
}

/* 节点属性 */
.node-attributes {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: 6px;
}

.attribute {
  font-size: 11px;
  padding: 1px 4px;
  border-radius: 2px;
  font-weight: 500;
}

.id-attr {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.class-attr {
  background-color: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
}

.other-attr {
  background-color: rgba(var(--text-light-rgb), 0.1);
  color: var(--text-light);
}

/* 节点文本 */
.node-text {
  font-style: italic;
  color: var(--text-light);
  margin-right: 6px;
  font-size: 11px;
}

/* 子元素数量 */
.children-count {
  font-size: 11px;
  color: var(--text-light);
  margin-right: 6px;
}

/* 操作按钮 */
.node-actions {
  display: flex;
  gap: 2px;
  margin-left: auto;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 2px;
  transition: all 0.2s ease;
  color: var(--text-light);
}

.action-btn:hover {
  background-color: rgba(var(--text-color-rgb), 0.1);
  color: var(--text-color);
}

.action-btn i {
  font-size: 12px;
}

/* 子节点 */
.node-children {
  border-left: 1px solid rgba(var(--border-color-rgb), 0.3);
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .node-content {
    font-size: 12px;
  }

  .node-actions {
    opacity: 1;
  }

  .action-btn {
    width: 24px;
    height: 24px;
  }
}
</style>
