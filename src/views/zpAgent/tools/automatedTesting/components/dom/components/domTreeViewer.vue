<template>
  <div class="dom-tree-viewer">
    <div class="tree-header">
      <div class="tree-title">
        <i class="ri-node-tree"></i>
        DOM 结构树
      </div>
      <div class="tree-actions">
        <button class="btn btn-small btn-primary" @click="expandAll">
          <i class="ri-add-circle-line"></i>
          展开全部
        </button>
        <button class="btn btn-small btn-secondary" @click="collapseAll">
          <i class="ri-subtract-circle-line"></i>
          收起全部
        </button>
        <button class="btn btn-small btn-info" @click="searchInTree">
          <i class="ri-search-line"></i>
          搜索
        </button>
      </div>
    </div>

    <div class="tree-search" v-if="showSearch">
      <el-input v-model="searchKeyword" placeholder="输入标签名、类名或ID进行搜索..." size="small" @input="handleSearch" clearable>
        <template #prefix>
          <i class="ri-search-line"></i>
        </template>
      </el-input>
    </div>

    <div class="tree-container">
      <div class="tree-content" v-loading="loading">
        <div class="tree-node" v-for="node in filteredNodes" :key="node.id">
          <dom-node :node="node" :level="0" :search-keyword="searchKeyword" @node-click="handleNodeClick" @node-select="handleNodeSelect" />
        </div>
      </div>
    </div>

    <div class="tree-footer" v-if="selectedNode">
      <div class="selected-info">
        <div class="selected-title">已选择元素</div>
        <div class="selected-details">
          <div class="detail-item">
            <span class="detail-label">标签:</span>
            <span class="detail-value">{{ selectedNode.tagName }}</span>
          </div>
          <div class="detail-item" v-if="selectedNode.id">
            <span class="detail-label">ID:</span>
            <span class="detail-value">{{ selectedNode.id }}</span>
          </div>
          <div class="detail-item" v-if="selectedNode.className">
            <span class="detail-label">类名:</span>
            <span class="detail-value">{{ selectedNode.className }}</span>
          </div>
          <div class="detail-item" v-if="selectedNode.textContent">
            <span class="detail-label">内容:</span>
            <span class="detail-value">{{ selectedNode.textContent.substring(0, 50) }}...</span>
          </div>
        </div>
        <div class="selected-actions">
          <button class="btn btn-small btn-success" @click="copySelector">
            <i class="ri-file-copy-line"></i>
            复制选择器
          </button>
          <button class="btn btn-small btn-warning" @click="copyXPath">
            <i class="ri-route-line"></i>
            复制XPath
          </button>
          <button class="btn btn-small btn-info" @click="copyElement">
            <i class="ri-code-line"></i>
            复制元素
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DomNode from './domNode.vue'

export default {
  name: 'DomTreeViewer',
  components: {
    DomNode
  },
  props: {
    htmlContent: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      showSearch: false,
      searchKeyword: '',
      nodes: [],
      filteredNodes: [],
      selectedNode: null,
      expandedNodes: new Set(),
      nodeIdCounter: 0
    }
  },
  watch: {
    htmlContent: {
      handler(newVal) {
        if (newVal) {
          this.parseHtml()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 解析HTML为树结构
    parseHtml() {
      this.loading = true
      try {
        const parser = new DOMParser()
        const doc = parser.parseFromString(this.htmlContent, 'text/html')

        // 从body开始解析，忽略head
        const bodyElement = doc.body || doc.documentElement
        this.nodes = this.parseElement(bodyElement)
        this.filteredNodes = [...this.nodes]
      } catch (error) {
        console.error('HTML解析失败:', error)
        this.$message.error('HTML解析失败')
      } finally {
        this.loading = false
      }
    },

    // 递归解析元素
    parseElement(element, parentPath = '') {
      const nodes = []

      for (let i = 0; i < element.children.length; i++) {
        const child = element.children[i]
        const node = {
          id: ++this.nodeIdCounter,
          tagName: child.tagName.toLowerCase(),
          className: child.className || '',
          id: child.id || '',
          textContent: child.textContent?.trim() || '',
          attributes: this.getAttributes(child),
          children: [],
          element: child,
          path: `${parentPath}/${child.tagName.toLowerCase()}[${i + 1}]`,
          selector: this.generateSelector(child),
          xpath: this.generateXPath(child),
          expanded: false
        }

        // 递归解析子元素
        if (child.children.length > 0) {
          node.children = this.parseElement(child, node.path)
        }

        nodes.push(node)
      }

      return nodes
    },

    // 获取元素属性
    getAttributes(element) {
      const attrs = {}
      for (let i = 0; i < element.attributes.length; i++) {
        const attr = element.attributes[i]
        attrs[attr.name] = attr.value
      }
      return attrs
    },

    // 生成CSS选择器
    generateSelector(element) {
      const parts = []
      let current = element

      while (current && current.tagName) {
        let selector = current.tagName.toLowerCase()

        if (current.id) {
          selector += `#${current.id}`
          parts.unshift(selector)
          break
        }

        if (current.className) {
          const classes = current.className.split(' ').filter(c => c.trim())
          if (classes.length > 0) {
            selector += '.' + classes.join('.')
          }
        }

        parts.unshift(selector)
        current = current.parentElement

        if (parts.length > 5) break // 限制深度
      }

      return parts.join(' > ')
    },

    // 生成XPath
    generateXPath(element) {
      const parts = []
      let current = element

      while (current && current.tagName) {
        let index = 1
        let sibling = current.previousElementSibling

        while (sibling) {
          if (sibling.tagName === current.tagName) {
            index++
          }
          sibling = sibling.previousElementSibling
        }

        const tagName = current.tagName.toLowerCase()
        parts.unshift(`${tagName}[${index}]`)
        current = current.parentElement

        if (parts.length > 8) break // 限制深度
      }

      return '//' + parts.join('/')
    },

    // 搜索处理
    searchInTree() {
      this.showSearch = !this.showSearch
      if (!this.showSearch) {
        this.searchKeyword = ''
        this.filteredNodes = [...this.nodes]
      }
    },

    handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.filteredNodes = [...this.nodes]
        return
      }

      const keyword = this.searchKeyword.toLowerCase()
      this.filteredNodes = this.filterNodes(this.nodes, keyword)
    },

    filterNodes(nodes, keyword) {
      const filtered = []

      for (const node of nodes) {
        const matches = node.tagName.includes(keyword) || node.className.toLowerCase().includes(keyword) || node.id.toLowerCase().includes(keyword) || node.textContent.toLowerCase().includes(keyword)

        if (matches) {
          filtered.push({
            ...node,
            children: this.filterNodes(node.children, keyword)
          })
        } else if (node.children.length > 0) {
          const childMatches = this.filterNodes(node.children, keyword)
          if (childMatches.length > 0) {
            filtered.push({
              ...node,
              children: childMatches
            })
          }
        }
      }

      return filtered
    },

    // 展开/收起所有节点
    expandAll() {
      this.setAllNodesExpanded(this.nodes, true)
    },

    collapseAll() {
      this.setAllNodesExpanded(this.nodes, false)
      this.expandedNodes.clear()
    },

    setAllNodesExpanded(nodes, expanded) {
      nodes.forEach(node => {
        node.expanded = expanded
        if (expanded) {
          this.expandedNodes.add(node.id)
        } else {
          this.expandedNodes.delete(node.id)
        }
        if (node.children.length > 0) {
          this.setAllNodesExpanded(node.children, expanded)
        }
      })
    },

    // 节点事件处理
    handleNodeClick(node) {
      this.$emit('node-click', node)
    },

    handleNodeSelect(node) {
      this.selectedNode = node
      this.$emit('node-select', node)
    },

    // 复制功能
    async copySelector() {
      if (!this.selectedNode) return

      try {
        await navigator.clipboard.writeText(this.selectedNode.selector)
        this.$message.success('CSS选择器已复制')
      } catch (error) {
        this.fallbackCopy(this.selectedNode.selector)
      }
    },

    async copyXPath() {
      if (!this.selectedNode) return

      try {
        await navigator.clipboard.writeText(this.selectedNode.xpath)
        this.$message.success('XPath已复制')
      } catch (error) {
        this.fallbackCopy(this.selectedNode.xpath)
      }
    },

    async copyElement() {
      if (!this.selectedNode) return

      try {
        await navigator.clipboard.writeText(this.selectedNode.element.outerHTML)
        this.$message.success('元素HTML已复制')
      } catch (error) {
        this.fallbackCopy(this.selectedNode.element.outerHTML)
      }
    },

    fallbackCopy(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        document.execCommand('copy')
        this.$message.success('已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败')
      }

      document.body.removeChild(textArea)
    }
  }
}
</script>

<style scoped>
@import '../../../../../assets/css/style.css';

.dom-tree-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--card-bg-color);
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-color);
}

.tree-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.tree-actions {
  display: flex;
  gap: 0.5rem;
}

.tree-search {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-color);
}

.tree-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tree-content {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

.tree-footer {
  border-top: 1px solid var(--border-color);
  background-color: var(--background-color);
  padding: 1rem;
}

.selected-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.selected-title {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 0.9rem;
}

.selected-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.detail-label {
  font-weight: 500;
  color: var(--text-light);
}

.detail-value {
  color: var(--text-color);
  background-color: var(--card-bg-color);
  padding: 0.2rem 0.5rem;
  border-radius: var(--radius-sm);
  font-family: monospace;
}

.selected-actions {
  display: flex;
  gap: 0.5rem;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.85rem;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-small {
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-secondary {
  background: var(--text-light);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: var(--text-color);
  transform: translateY(-1px);
}

.btn-info {
  background: var(--accent-color);
  color: white;
}

.btn-info:hover:not(:disabled) {
  background: var(--secondary-color);
  transform: translateY(-1px);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #06d6a0;
  transform: translateY(-1px);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #f39c12;
  transform: translateY(-1px);
}

/* 滚动条样式 */
.tree-content::-webkit-scrollbar {
  width: 6px;
}

.tree-content::-webkit-scrollbar-track {
  background: var(--background-color);
}

.tree-content::-webkit-scrollbar-thumb {
  background-color: rgba(var(--primary-color-rgb), 0.2);
  border-radius: 3px;
}

.tree-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(var(--primary-color-rgb), 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tree-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .tree-actions {
    justify-content: center;
  }

  .selected-details {
    flex-direction: column;
    gap: 0.5rem;
  }

  .selected-actions {
    flex-direction: column;
  }
}
</style>
