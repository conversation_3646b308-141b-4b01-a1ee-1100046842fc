<template>
  <div class="ai-generate">
    <!-- 导入WebUI自动化测试智能体样式 -->
    <div class="ai-generate-content">
      <!-- 左侧输入面板 -->
      <div class="left-panel">
        <!-- 测试描述输入 -->
        <div class="input-section">
          <div class="section-title">
            <i class="ri-brain-line"></i>
            测试需求描述
          </div>
          <div class="input-container">
            <textarea 
              v-model="testDescription" 
              class="description-input" 
              placeholder="请描述您的测试需求，例如：登录网站，搜索商品，验证搜索结果等..."
              rows="4"
            ></textarea>
            <div class="input-tip">
              <i class="ri-lightbulb-line"></i>
              <span>💡 提示：详细描述测试步骤，AI将根据您的描述和选中的元素生成精确的Playwright脚本</span>
            </div>
          </div>
        </div>

        <!-- 元素选择区域 -->
        <div class="element-section">
          <div class="section-title">
            <i class="ri-cursor-line"></i>
            选中的元素
            <span class="element-count">({{ selectedElements.length }})</span>
          </div>
          <div class="element-container">
            <div v-if="selectedElements.length === 0" class="no-elements">
              <i class="ri-information-line"></i>
              <p>暂无选中的元素</p>
              <p class="tip">请在DOM分析页面选择需要操作的元素</p>
            </div>
            <div v-else class="element-list">
              <div 
                v-for="(element, index) in selectedElements" 
                :key="index" 
                class="element-item"
              >
                <div class="element-header">
                  <el-tag :type="getElementTypeColor(element.tagName)" size="small">
                    {{ element.tagName.toUpperCase() }}
                  </el-tag>
                  <span class="element-text">{{ getElementDisplayText(element) }}</span>
                  <button 
                    class="btn btn-tiny btn-outline" 
                    @click="removeElement(index)"
                    title="移除元素"
                  >
                    <i class="ri-close-line"></i>
                  </button>
                </div>
                <div class="element-details">
                  <div class="detail-item">
                    <span class="label">选择器:</span>
                    <code class="selector-code">{{ element.selector }}</code>
                  </div>
                  <div v-if="element.textContent" class="detail-item">
                    <span class="label">文本:</span>
                    <span class="text-content">{{ truncateText(element.textContent, 30) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- DOM树形结构 -->
        <div class="dom-tree-section">
          <div class="section-title">
            <i class="ri-node-tree"></i>
            DOM结构上下文
            <button 
              class="btn btn-small btn-outline" 
              @click="toggleDomTree"
              :class="{ active: showDomTree }"
            >
              {{ showDomTree ? '隐藏' : '显示' }}
            </button>
          </div>
          <div v-show="showDomTree" class="dom-tree-container">
            <textarea 
              v-model="domContext" 
              class="dom-tree-input" 
              placeholder="DOM结构将自动从选中的元素中提取..."
              rows="6"
              readonly
            ></textarea>
          </div>
        </div>

        <!-- 生成配置 -->
        <div class="config-section">
          <div class="section-title">
            <i class="ri-settings-3-line"></i>
            生成配置
          </div>
          <div class="config-container">
            <div class="config-group">
              <label class="config-label">测试框架:</label>
              <select v-model="framework" class="config-select">
                <option value="playwright">Playwright</option>
                <option value="selenium">Selenium</option>
                <option value="cypress">Cypress</option>
              </select>
            </div>
            <div class="config-group">
              <label class="config-label">编程语言:</label>
              <select v-model="language" class="config-select">
                <option value="javascript">JavaScript</option>
                <option value="typescript">TypeScript</option>
                <option value="python">Python</option>
                <option value="java">Java</option>
              </select>
            </div>
            <div class="config-group">
              <label class="config-label">测试类型:</label>
              <select v-model="testType" class="config-select">
                <option value="e2e">端到端测试</option>
                <option value="integration">集成测试</option>
                <option value="unit">单元测试</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 生成按钮 -->
        <div class="action-section">
          <button 
            class="btn btn-primary generate-btn" 
            @click="generateTestScript"
            :disabled="!canGenerate || isGenerating"
          >
            <i class="ri-ai-generate" v-if="!isGenerating"></i>
            <i class="ri-loader-4-line rotating" v-else></i>
            {{ isGenerating ? '生成中...' : 'AI生成测试脚本' }}
          </button>
        </div>
      </div>

      <!-- 右侧结果面板 -->
      <div class="right-panel">
        <div class="result-section">
          <div class="section-title">
            <i class="ri-code-s-slash-line"></i>
            生成的测试脚本
            <div class="title-actions" v-if="generatedScript">
              <button class="btn btn-small btn-success" @click="copyScript">
                <i class="ri-file-copy-line"></i>
                复制
              </button>
              <button class="btn btn-small btn-info" @click="downloadScript">
                <i class="ri-download-line"></i>
                下载
              </button>
            </div>
          </div>
          
          <div class="result-container">
            <div v-if="!generatedScript && !isGenerating" class="no-result">
              <i class="ri-code-s-slash-line"></i>
              <h4>等待生成测试脚本</h4>
              <p>请填写测试描述并选择元素后点击生成按钮</p>
            </div>
            
            <div v-else-if="isGenerating" class="generating">
              <div class="loading-animation">
                <i class="ri-loader-4-line rotating"></i>
              </div>
              <h4>AI正在生成测试脚本...</h4>
              <p>请稍候，这可能需要几秒钟时间</p>
            </div>
            
            <div v-else class="script-result">
              <MarkdownRendererEdit 
                :content="generatedScript"
                :editable="true"
                @content-updated="onScriptUpdated"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MarkdownRendererEdit from '@/views/zpAgent/components/MarkdownRendererEdit.vue'

export default {
  name: 'AIGenerate',
  components: {
    MarkdownRendererEdit
  },
  props: {
    // 从父组件传入的选中元素
    selectedElements: {
      type: Array,
      default: () => []
    },
    // DOM上下文信息
    domContext: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 测试描述
      testDescription: '',
      
      // DOM树显示状态
      showDomTree: false,
      
      // 生成配置
      framework: 'playwright',
      language: 'javascript',
      testType: 'e2e',
      
      // 生成状态
      isGenerating: false,
      generatedScript: '',
      
      // 内部DOM上下文（可编辑）
      internalDomContext: this.domContext
    }
  },
  computed: {
    // 是否可以生成
    canGenerate() {
      return this.testDescription.trim().length > 0 && this.selectedElements.length > 0
    }
  },
  watch: {
    // 监听传入的DOM上下文变化
    domContext(newVal) {
      this.internalDomContext = newVal
    },
    
    // 监听选中元素变化，自动更新DOM上下文
    selectedElements: {
      handler(newElements) {
        this.updateDomContext()
      },
      deep: true
    }
  },
  methods: {
    // 获取元素类型颜色
    getElementTypeColor(tagName) {
      const colors = {
        input: 'primary',
        button: 'success',
        a: 'info',
        select: 'warning',
        textarea: 'primary',
        div: '',
        span: '',
        p: 'info'
      }
      return colors[tagName.toLowerCase()] || ''
    },
    
    // 获取元素显示文本
    getElementDisplayText(element) {
      if (element.textContent && element.textContent.trim()) {
        return this.truncateText(element.textContent, 20)
      }
      if (element.id) {
        return `#${element.id}`
      }
      if (element.className) {
        const firstClass = element.className.split(/\s+/)[0]
        return `.${firstClass}`
      }
      return `${element.tagName}元素`
    },
    
    // 截断文本
    truncateText(text, maxLength = 50) {
      if (!text) return ''
      const cleaned = text.replace(/\s+/g, ' ').trim()
      return cleaned.length > maxLength ? cleaned.substring(0, maxLength) + '...' : cleaned
    },
    
    // 移除元素
    removeElement(index) {
      this.$emit('remove-element', index)
    },
    
    // 切换DOM树显示
    toggleDomTree() {
      this.showDomTree = !this.showDomTree
    },
    
    // 更新DOM上下文
    updateDomContext() {
      if (this.selectedElements.length === 0) {
        this.internalDomContext = ''
        return
      }

      // 根据选中的元素生成DOM上下文
      const contextLines = []
      this.selectedElements.forEach((element, index) => {
        contextLines.push(`元素${index + 1}: ${element.tagName}`)
        if (element.id) contextLines.push(`  ID: ${element.id}`)
        if (element.className) contextLines.push(`  Class: ${element.className}`)
        if (element.textContent) contextLines.push(`  文本: ${this.truncateText(element.textContent, 50)}`)
        contextLines.push(`  选择器: ${element.selector}`)
        contextLines.push('')
      })

      this.internalDomContext = contextLines.join('\n')
    },

    // 生成测试脚本
    async generateTestScript() {
      if (!this.canGenerate) {
        this.$message.warning('请填写测试描述并选择至少一个元素')
        return
      }

      this.isGenerating = true

      try {
        // 构建生成请求的数据
        const generateData = {
          description: this.testDescription,
          elements: this.selectedElements.map(el => ({
            tagName: el.tagName,
            selector: el.selector,
            id: el.id,
            className: el.className,
            textContent: el.textContent,
            attributes: el.attributes
          })),
          domContext: this.internalDomContext,
          framework: this.framework,
          language: this.language,
          testType: this.testType
        }

        // 模拟API调用（这里需要替换为实际的API调用）
        const response = await this.callGenerateAPI(generateData)

        this.generatedScript = response.script
        this.$message.success('测试脚本生成成功！')

      } catch (error) {
        console.error('生成测试脚本失败:', error)
        this.$message.error('生成失败，请重试')
      } finally {
        this.isGenerating = false
      }
    },

    // 调用生成API（模拟实现）
    async callGenerateAPI(data) {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 根据配置生成不同的脚本模板
      const script = this.generateScriptTemplate(data)

      return { script }
    },

    // 生成脚本模板
    generateScriptTemplate(data) {
      const { description, elements, framework, language } = data

      if (framework === 'playwright' && language === 'javascript') {
        return this.generatePlaywrightJS(description, elements)
      } else if (framework === 'playwright' && language === 'typescript') {
        return this.generatePlaywrightTS(description, elements)
      } else if (framework === 'playwright' && language === 'python') {
        return this.generatePlaywrightPython(description, elements)
      }

      // 默认返回Playwright JavaScript
      return this.generatePlaywrightJS(description, elements)
    },

    // 生成Playwright JavaScript脚本
    generatePlaywrightJS(description, elements) {
      const elementActions = elements.map((el, index) => {
        const selector = el.selector
        const action = this.inferActionFromElement(el)
        return `  // ${el.tagName}元素操作\n  ${action.replace('SELECTOR', `'${selector}'`)}`
      }).join('\n\n')

      return `\`\`\`javascript
// ${description}
const { test, expect } = require('@playwright/test');

test('${description}', async ({ page }) => {
  // 导航到页面
  await page.goto('YOUR_URL_HERE');

${elementActions}

  // 验证结果
  await expect(page).toHaveTitle(/.*页面标题.*/);
});
\`\`\``
    },

    // 生成Playwright TypeScript脚本
    generatePlaywrightTS(description, elements) {
      const elementActions = elements.map((el, index) => {
        const selector = el.selector
        const action = this.inferActionFromElement(el)
        return `  // ${el.tagName}元素操作\n  ${action.replace('SELECTOR', `'${selector}'`)}`
      }).join('\n\n')

      return `\`\`\`typescript
// ${description}
import { test, expect } from '@playwright/test';

test('${description}', async ({ page }) => {
  // 导航到页面
  await page.goto('YOUR_URL_HERE');

${elementActions}

  // 验证结果
  await expect(page).toHaveTitle(/.*页面标题.*/);
});
\`\`\``
    },

    // 生成Playwright Python脚本
    generatePlaywrightPython(description, elements) {
      const elementActions = elements.map((el, index) => {
        const selector = el.selector
        const action = this.inferActionFromElement(el, 'python')
        return `    # ${el.tagName}元素操作\n    ${action.replace('SELECTOR', `"${selector}"`)}`
      }).join('\n\n')

      return `\`\`\`python
# ${description}
import pytest
from playwright.sync_api import Page, expect

def test_${description.replace(/\s+/g, '_').toLowerCase()}(page: Page):
    # 导航到页面
    page.goto("YOUR_URL_HERE")

${elementActions}

    # 验证结果
    expect(page).to_have_title(re.compile(".*页面标题.*"))
\`\`\``
    },

    // 根据元素推断操作
    inferActionFromElement(el, lang = 'javascript') {
      const tagName = el.tagName.toLowerCase()
      const type = el.attributes?.type?.toLowerCase()

      if (lang === 'python') {
        if (tagName === 'button' || (tagName === 'input' && type === 'submit')) {
          return 'page.click(SELECTOR)'
        } else if (tagName === 'input' && ['text', 'email', 'password'].includes(type)) {
          return 'page.fill(SELECTOR, "输入内容")'
        } else if (tagName === 'select') {
          return 'page.select_option(SELECTOR, "选项值")'
        } else if (tagName === 'a') {
          return 'page.click(SELECTOR)'
        } else if (tagName === 'textarea') {
          return 'page.fill(SELECTOR, "输入内容")'
        }
        return 'page.click(SELECTOR)'
      }

      // JavaScript/TypeScript
      if (tagName === 'button' || (tagName === 'input' && type === 'submit')) {
        return 'await page.click(SELECTOR);'
      } else if (tagName === 'input' && ['text', 'email', 'password'].includes(type)) {
        return 'await page.fill(SELECTOR, "输入内容");'
      } else if (tagName === 'select') {
        return 'await page.selectOption(SELECTOR, "选项值");'
      } else if (tagName === 'a') {
        return 'await page.click(SELECTOR);'
      } else if (tagName === 'textarea') {
        return 'await page.fill(SELECTOR, "输入内容");'
      }
      return 'await page.click(SELECTOR);'
    },

    // 脚本更新回调
    onScriptUpdated(newContent) {
      this.generatedScript = newContent
    },

    // 复制脚本
    async copyScript() {
      try {
        // 提取代码块内容
        const codeMatch = this.generatedScript.match(/```[\s\S]*?\n([\s\S]*?)```/)
        const code = codeMatch ? codeMatch[1] : this.generatedScript

        await navigator.clipboard.writeText(code)
        this.$message.success('脚本已复制到剪贴板')
      } catch (error) {
        this.$message.error('复制失败')
      }
    },

    // 下载脚本
    downloadScript() {
      try {
        // 提取代码块内容
        const codeMatch = this.generatedScript.match(/```[\s\S]*?\n([\s\S]*?)```/)
        const code = codeMatch ? codeMatch[1] : this.generatedScript

        // 根据语言确定文件扩展名
        const extensions = {
          javascript: 'js',
          typescript: 'ts',
          python: 'py',
          java: 'java'
        }
        const ext = extensions[this.language] || 'js'

        const blob = new Blob([code], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)

        const a = document.createElement('a')
        a.href = url
        a.download = `test_script_${Date.now()}.${ext}`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        this.$message.success('脚本已下载')
      } catch (error) {
        this.$message.error('下载失败')
      }
    }
  },

  mounted() {
    // 初始化DOM上下文
    this.updateDomContext()
  }
  }

</script>

<style scoped>
/* 导入WebUI自动化测试智能体样式 */
@import '../../../../../assets/css/style.css';

.ai-generate {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color);
}

.ai-generate-content {
  display: flex;
  gap: 1rem;
  height: 100%;
  padding: 1rem;
}

/* 左侧面板 */
.left-panel {
  width: 400px;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  overflow-y: auto;
}

/* 右侧面板 */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 通用样式 */
.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.section-title i {
  color: var(--primary-color);
}

.element-count {
  background: var(--primary-color);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.title-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: auto;
}

/* 输入区域 */
.input-section,
.element-section,
.dom-tree-section,
.config-section,
.result-section {
  background-color: var(--card-bg-color);
  border-radius: var(--radius);
  padding: 1.5rem;
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-color);
}

.input-container,
.element-container,
.dom-tree-container,
.config-container,
.result-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.description-input,
.dom-tree-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: vertical;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: var(--transition);
}

.description-input:focus,
.dom-tree-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.input-tip {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(var(--primary-color-rgb), 0.05);
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  color: var(--text-light);
  border-left: 3px solid var(--primary-color);
}

.input-tip i {
  color: var(--primary-color);
  margin-top: 0.1rem;
}

/* 元素列表 */
.no-elements {
  text-align: center;
  padding: 2rem;
  color: var(--text-light);
}

.no-elements i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.no-elements .tip {
  font-size: 0.85rem;
  margin-top: 0.5rem;
}

.element-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 300px;
  overflow-y: auto;
}

.element-item {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: 1rem;
  background: var(--background-color);
  transition: var(--transition);
}

.element-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.element-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.element-text {
  flex: 1;
  font-weight: 500;
  color: var(--text-color);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.element-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.detail-item .label {
  color: var(--text-light);
  font-weight: 600;
  min-width: 60px;
}

.selector-code {
  background: var(--card-bg-color);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.text-content {
  color: var(--text-color);
  font-style: italic;
}

/* 配置区域 */
.config-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.config-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
}

.config-select {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--background-color);
  color: var(--text-color);
  font-size: 0.9rem;
  transition: var(--transition);
}

.config-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

/* 操作区域 */
.action-section {
  display: flex;
  justify-content: center;
}

.generate-btn {
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  gap: 0.75rem;
}

.generate-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* 结果区域 */
.result-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.result-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.no-result,
.generating {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--text-light);
  padding: 3rem;
}

.no-result i,
.generating .loading-animation i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.generating .loading-animation i {
  color: var(--primary-color);
}

.no-result h4,
.generating h4 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.script-result {
  flex: 1;
  overflow: hidden;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 3px 8px rgba(var(--primary-color-rgb), 0.25);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(var(--primary-color-rgb), 0.35);
}

.btn-success {
  background: var(--success-gradient);
  color: white;
  box-shadow: 0 3px 8px rgba(16, 185, 129, 0.25);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(16, 185, 129, 0.35);
}

.btn-info {
  background: linear-gradient(120deg, #6366f1, #4f46e5);
  color: white;
  box-shadow: 0 3px 8px rgba(99, 102, 241, 0.25);
}

.btn-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(99, 102, 241, 0.35);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: white;
}

.btn-outline.active {
  background-color: var(--primary-color);
  color: white;
}

.btn-small {
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
}

.btn-tiny {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* 动画 */
.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .ai-generate-content {
    flex-direction: column;
  }

  .left-panel {
    width: 100%;
    max-height: 50vh;
  }

  .right-panel {
    min-height: 50vh;
  }
}

@media (max-width: 768px) {
  .ai-generate-content {
    padding: 0.5rem;
    gap: 1rem;
  }

  .input-section,
  .element-section,
  .dom-tree-section,
  .config-section,
  .result-section {
    padding: 1rem;
  }

  .element-header {
    flex-wrap: wrap;
  }

  .title-actions {
    flex-direction: column;
    gap: 0.25rem;
  }
}
</style>
