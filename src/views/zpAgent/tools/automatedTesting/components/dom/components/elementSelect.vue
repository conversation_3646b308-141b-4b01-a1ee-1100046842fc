<template>
  <div class="element-select">
    <div class="element-select-content">
      <!-- 输入区域 -->
      <div class="input-section">
        <div class="section-title">
          <i class="ri-code-s-slash-line"></i>
          HTML 代码输入
        </div>
        <div class="input-container">
          <textarea v-model="inputHtml" class="html-input" placeholder="请粘贴需要分析的HTML代码..." rows="8"></textarea>
          <div class="input-actions">
            <button class="btn btn-primary" @click="parseHtml" :disabled="!inputHtml.trim() || isParsing">
              <i class="ri-search-line" v-if="!isParsing"></i>
              <i class="ri-loader-4-line rotating" v-else></i>
              {{ isParsing ? '解析中...' : '解析HTML' }}
            </button>
            <button class="btn btn-primary" @click="toggleSmartFilter" :class="{ 'smart-filter-active': isSmartFilterActive }">
              <i class="ri-brain-line" v-if="!isSmartFilterActive"></i>
              <i class="ri-brain-fill" v-else></i>
              {{ isSmartFilterActive ? '智能筛选中' : '智能筛选' }}
            </button>
            <button class="btn btn-secondary" @click="clearInput" :disabled="!inputHtml.trim()">
              <i class="ri-delete-bin-line"></i>
              清空
            </button>
          </div>
        </div>
      </div>

      <!-- 选择器输入区域 -->
      <div class="selector-section" v-if="parsedElements.length > 0">
        <div class="section-title">
          <i class="ri-search-2-line"></i>
          CSS 选择器
        </div>
        <div class="selector-container">
          <div class="selector-input-group">
            <input v-model="cssSelector" class="selector-input" placeholder="输入CSS选择器，如: .button, #header, div.card > .title" @input="onSelectorChange" @keyup.enter="executeSelector" />
            <button class="btn btn-primary selector-btn" @click="executeSelector" :disabled="!cssSelector.trim() || isExecuting">
              <i class="ri-play-line" v-if="!isExecuting"></i>
              <i class="ri-loader-4-line rotating" v-else></i>
              {{ isExecuting ? '执行中...' : '执行' }}
            </button>
          </div>

          <!-- 快捷选择器 -->
          <div class="quick-selectors">
            <div class="quick-selector-title">常用选择器：</div>
            <div class="quick-selector-buttons">
              <button v-for="quick in quickSelectors" :key="quick.value" class="btn btn-small btn-outline" @click="useQuickSelector(quick.value)" :title="quick.description">
                {{ quick.label }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 选择器历史 -->
      <div class="history-section" v-if="selectorHistory.length > 0">
        <div class="section-title">
          <i class="ri-history-line"></i>
          选择器历史
        </div>
        <div class="history-list">
          <div v-for="(item, index) in selectorHistory.slice(0, 5)" :key="index" class="history-item" @click="useHistorySelector(item)">
            <span class="history-selector">{{ item.selector }}</span>
            <span class="history-count">{{ item.count }} 个匹配</span>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section" v-if="parsedElements.length > 0">
        <div class="section-title">
          <i class="ri-bar-chart-line"></i>
          解析统计
        </div>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ parsedElements.length }}</div>
            <div class="stat-label">总元素数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ matchedElements.length }}</div>
            <div class="stat-label">匹配元素</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ uniqueClasses.length }}</div>
            <div class="stat-label">唯一类名</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ uniqueIds.length }}</div>
            <div class="stat-label">唯一ID</div>
          </div>
        </div>
      </div>

      <!-- 所有元素展示 -->
      <div class="all-elements-section" v-if="parsedElements.length > 0 && !cssSelector">
        <div class="section-title">
          <i class="ri-list-unordered"></i>
          所有元素 ({{ parsedElements.length }})
          <span v-if="isSmartFilterActive" class="smart-filter-indicator">
            <i class="ri-brain-fill"></i>
            智能筛选已启用 (显示 {{ filteredAllElements.length }} / {{ parsedElements.length }} 个元素)
          </span>
          <div class="title-actions">
            <button class="btn btn-small btn-success" @click="copyAllElements" :disabled="parsedElements.length === 0">
              <i class="ri-file-copy-line" style="color: #fff"></i>
              复制所有
            </button>
            <button class="btn btn-small btn-info" @click="exportAllElements" :disabled="parsedElements.length === 0">
              <i class="ri-download-line" style="color: #fff"></i>
              导出
            </button>
          </div>
        </div>

        <!-- 全局过滤器 -->
        <div class="result-filters">
          <div class="filter-group">
            <label>按标签过滤:</label>
            <select v-model="globalFilterTag" @change="applyGlobalFilters" class="filter-select">
              <option value="">全部标签</option>
              <option v-for="tag in allAvailableTags" :key="tag" :value="tag">{{ tag }}</option>
            </select>
          </div>
          <div class="filter-group">
            <label>按类型过滤:</label>
            <select v-model="globalFilterType" @change="applyGlobalFilters" class="filter-select">
              <option value="">全部类型</option>
              <option value="interactive">交互元素</option>
              <option value="form">表单元素</option>
              <option value="text">文本元素</option>
              <option value="media">媒体元素</option>
            </select>
          </div>
          <div class="filter-group">
            <input v-model="globalFilterText" @input="applyGlobalFilters" placeholder="搜索文本内容..." class="filter-input" />
          </div>
        </div>

        <!-- 所有元素列表 -->
        <div class="elements-grid">
          <div v-for="(element, index) in filteredAllElements.slice(0, allElementsDisplayLimit)" :key="index" class="element-card" @click="selectElement(element)">
            <div class="element-header">
              <el-tag :type="getElementTypeColor(element.tagName)" size="small">
                {{ element.tagName.toUpperCase() }}
              </el-tag>
              <span class="element-text">{{ getElementDisplayText(element) }}</span>
              <!-- 显示相同选择器的序号 -->
              <span v-if="getSelectorCount(element) > 1" class="element-index">第{{ getSelectorIndex(element, index) }}个</span>
              <div class="element-actions">
                <button class="btn btn-tiny btn-outline" @click.stop="copyElementSelector(element)" title="复制选择器">
                  <i class="ri-file-copy-line"></i>
                </button>
                <button class="btn btn-tiny btn-outline" @click.stop="highlightElement(element)" title="高亮显示">
                  <i class="ri-focus-line"></i>
                </button>
                <button class="btn btn-tiny btn-primary" @click.stop="useElementAsSelector(element)" title="设为选择器">
                  <i class="ri-focus-3-line"></i>
                </button>
              </div>
            </div>

            <div class="element-details">
              <!-- <div v-if="element.textContent" class="detail-item">
                <span class="label">文本内容:</span>
                <div class="detail-value">{{ truncateText(element.textContent) }}</div>
              </div> -->

              <div v-if="element.attributes && Object.keys(element.attributes).length > 0" class="detail-item">
                <span class="label">属性:</span>
                <div class="attributes-list">
                  <div v-for="(value, attr) in getMainAttributes(element.attributes)" :key="attr" class="attribute-item">{{ attr }}="{{ value }}"</div>
                </div>
              </div>

              <div class="detail-item">
                <span class="label">CSS选择器:</span>
                <div class="selector-suggestions">
                  <code v-for="selector in generateSelectors(element).slice(0, 2)" :key="selector" class="selector-code" @click="useSelector(selector)">
                    {{ selector }}
                  </code>
                </div>
              </div>

              <div class="detail-item">
                <span class="label">Playwright选择器:</span>
                <div class="playwright-selectors">
                  <code v-for="selector in generatePlaywrightSelectors(element, index).slice(0, 1)" :key="selector" class="playwright-code" @click="copyToClipboard(selector)">
                    {{ selector }}
                  </code>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载更多 -->
          <div v-if="filteredAllElements.length > allElementsDisplayLimit" class="load-more">
            <button class="btn btn-outline" @click="loadMoreAllElements">显示更多 ({{ filteredAllElements.length - allElementsDisplayLimit }} 个剩余)</button>
          </div>
        </div>
      </div>

      <!-- 匹配结果 -->
      <div class="results-section" v-if="matchedElements.length > 0 && cssSelector">
        <div class="section-title">
          <i class="ri-list-check-line"></i>
          匹配结果 ({{ matchedElements.length }})
          <div class="title-actions">
            <button class="btn btn-small btn-success" @click="copyResults" :disabled="matchedElements.length === 0">
              <i class="ri-file-copy-line"></i>
              复制结果
            </button>
            <button class="btn btn-small btn-info" @click="exportResults" :disabled="matchedElements.length === 0">
              <i class="ri-download-line"></i>
              导出
            </button>
          </div>
        </div>

        <!-- 结果过滤器 -->
        <div class="result-filters">
          <div class="filter-group">
            <label>按标签过滤:</label>
            <select v-model="filterTag" @change="applyFilters" class="filter-select">
              <option value="">全部标签</option>
              <option v-for="tag in availableTags" :key="tag" :value="tag">{{ tag }}</option>
            </select>
          </div>
          <div class="filter-group">
            <label>按类型过滤:</label>
            <select v-model="filterType" @change="applyFilters" class="filter-select">
              <option value="">全部类型</option>
              <option value="interactive">交互元素</option>
              <option value="form">表单元素</option>
              <option value="text">文本元素</option>
              <option value="media">媒体元素</option>
            </select>
          </div>
          <div class="filter-group">
            <input v-model="filterText" @input="applyFilters" placeholder="搜索文本内容..." class="filter-input" />
          </div>
        </div>

        <!-- 结果列表 -->
        <div class="results-list">
          <div v-for="(element, index) in filteredResults.slice(0, displayLimit)" :key="index" class="result-item" @click="selectElement(element)" :class="{ active: selectedElement === element }">
            <div class="result-header">
              <div class="element-info">
                <span class="element-tag">{{ element.tagName }}</span>
                <span v-if="element.id" class="element-id">#{{ element.id }}</span>
                <span v-if="element.className" class="element-classes">{{ formatClasses(element.className) }}</span>
              </div>
              <div class="element-actions">
                <button class="btn btn-tiny btn-outline" @click.stop="copyElementSelector(element)" title="复制选择器">
                  <i class="ri-file-copy-line"></i>
                </button>
                <button class="btn btn-tiny btn-outline" @click.stop="highlightElement(element)" title="高亮显示">
                  <i class="ri-focus-line"></i>
                </button>
              </div>
            </div>

            <div class="result-details" v-if="selectedElement === element">
              <div class="detail-row" v-if="element.textContent">
                <label>文本内容:</label>
                <span class="detail-value">{{ truncateText(element.textContent) }}</span>
              </div>
              <div class="detail-row" v-if="element.attributes && Object.keys(element.attributes).length > 0">
                <label>属性:</label>
                <div class="attributes-list">
                  <span v-for="(value, attr) in element.attributes" :key="attr" class="attribute-tag">{{ attr }}="{{ value }}"</span>
                </div>
              </div>
              <div class="detail-row">
                <label>CSS选择器:</label>
                <div class="selector-suggestions">
                  <code v-for="selector in generateSelectors(element)" :key="selector" class="selector-code" @click="useSelector(selector)">
                    {{ selector }}
                  </code>
                </div>
              </div>
              <div class="detail-row">
                <label>Playwright选择器:</label>
                <div class="playwright-selectors">
                  <code v-for="selector in generatePlaywrightSelectors(element, index)" :key="selector" class="playwright-code" @click="copyToClipboard(selector)">
                    {{ selector }}
                  </code>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载更多 -->
          <div v-if="filteredResults.length > displayLimit" class="load-more">
            <button class="btn btn-outline" @click="loadMore">显示更多 ({{ filteredResults.length - displayLimit }} 个剩余)</button>
          </div>
        </div>
      </div>

      <!-- 无结果提示 -->
      <div v-else-if="cssSelector && !isExecuting" class="no-results">
        <div class="no-results-icon">
          <i class="ri-search-line"></i>
        </div>
        <div class="no-results-text">
          <h4>未找到匹配的元素</h4>
          <p>请检查CSS选择器语法是否正确，或尝试其他选择器</p>
        </div>
        <div class="no-results-suggestions">
          <h5>建议尝试:</h5>
          <ul>
            <li>
              使用更通用的选择器，如
              <code>div</code>
              、
              <code>.class-name</code>
            </li>
            <li>检查类名和ID是否存在于HTML中</li>
            <li>使用浏览器开发者工具验证选择器</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'elementSelect',
  data() {
    return {
      inputHtml: '',
      cssSelector: '',
      parsedElements: [],
      matchedElements: [],
      filteredResults: [],
      filteredAllElements: [], // 过滤后的所有元素
      selectedElement: null,
      isParsing: false,
      isExecuting: false,
      selectorHistory: [],
      displayLimit: 100,
      allElementsDisplayLimit: 100, // 所有元素显示限制

      // 过滤器
      filterTag: '',
      filterType: '',
      filterText: '',

      // 全局过滤器（用于所有元素）
      globalFilterTag: '',
      globalFilterType: '',
      globalFilterText: '',

      // 智能筛选
      isSmartFilterActive: false,
      smartFilterThreshold: 3, // 智能筛选分数阈值（降低到3分）

      // 快捷选择器
      quickSelectors: [
        { label: 'button', value: 'button', description: '所有按钮元素' },
        { label: 'input', value: 'input', description: '所有输入框' },
        { label: 'a', value: 'a', description: '所有链接' },
        { label: '.btn', value: '.btn', description: '按钮类' },
        { label: '#main', value: '#main', description: 'ID为main的元素' },
        { label: 'div > p', value: 'div > p', description: 'div直接子元素p' },
        { label: '.container .item', value: '.container .item', description: '容器内的项目' },
        { label: '[data-id]', value: '[data-id]', description: '有data-id属性的元素' }
      ]
    }
  },

  computed: {
    uniqueClasses() {
      const classes = new Set()
      this.parsedElements.forEach(el => {
        if (el.className) {
          // el.className 在 elementToObject 中已经被处理为字符串
          el.className.split(/\s+/).forEach(cls => {
            if (cls.trim()) classes.add(cls.trim())
          })
        }
      })
      return Array.from(classes)
    },

    uniqueIds() {
      const ids = new Set()
      this.parsedElements.forEach(el => {
        if (el.id) ids.add(el.id)
      })
      return Array.from(ids)
    },

    availableTags() {
      const tags = new Set()
      this.matchedElements.forEach(el => tags.add(el.tagName.toLowerCase()))
      return Array.from(tags).sort()
    },

    allAvailableTags() {
      const tags = new Set()
      this.parsedElements.forEach(el => tags.add(el.tagName.toLowerCase()))
      return Array.from(tags).sort()
    }
  },

  methods: {
    // 解析HTML
    async parseHtml() {
      if (!this.inputHtml.trim()) {
        this.$message.warning('请输入HTML代码')
        return
      }

      this.isParsing = true

      try {
        // 创建临时DOM解析器
        const parser = new DOMParser()
        const doc = parser.parseFromString(this.inputHtml, 'text/html')

        // 提取所有元素
        const elements = []
        const walker = document.createTreeWalker(doc.body || doc.documentElement, NodeFilter.SHOW_ELEMENT, null, false)

        let node
        while ((node = walker.nextNode())) {
          elements.push(this.elementToObject(node))
        }

        this.parsedElements = elements
        this.applyGlobalFilters() // 初始化所有元素显示
        this.$message.success(`成功解析 ${elements.length} 个元素`)

        // 自动执行当前选择器
        if (this.cssSelector) {
          this.executeSelector()
        }
      } catch (error) {
        console.error('解析HTML失败:', error)
        this.$message.error('HTML解析失败，请检查代码格式')
      } finally {
        this.isParsing = false
      }
    },

    // 将DOM元素转换为对象
    elementToObject(element) {
      // 处理 className 可能是 SVGAnimatedString 或其他类型的情况
      let className = ''
      if (element.className) {
        className = typeof element.className === 'string' ? element.className : element.className.baseVal || element.className.toString()
      }

      const obj = {
        tagName: element.tagName.toLowerCase(),
        id: element.id || '',
        className: className || '',
        textContent: element.textContent?.trim() || '',
        attributes: {},
        path: this.getElementPath(element)
      }

      // 收集属性
      Array.from(element.attributes).forEach(attr => {
        obj.attributes[attr.name] = attr.value
      })

      return obj
    },

    // 获取元素路径
    getElementPath(element) {
      const path = []
      let current = element

      while (current && current.tagName) {
        let selector = current.tagName.toLowerCase()

        if (current.id) {
          selector += '#' + current.id
          path.unshift(selector)
          break // ID是唯一的，可以停止
        }

        if (current.className) {
          // 处理 className 可能是 SVGAnimatedString 或其他类型的情况
          const classNameStr = typeof current.className === 'string' ? current.className : current.className.baseVal || current.className.toString()

          if (classNameStr) {
            const classes = classNameStr.split(/\s+/).filter(c => c.trim())
            if (classes.length > 0) {
              selector += '.' + classes.join('.')
            }
          }
        }

        path.unshift(selector)
        current = current.parentElement

        // 防止路径过长
        if (path.length > 10) break
      }

      return path.join(' > ')
    },

    // 执行CSS选择器
    async executeSelector() {
      if (!this.cssSelector.trim() || this.parsedElements.length === 0) {
        return
      }

      this.isExecuting = true

      try {
        // 创建临时DOM进行查询
        const parser = new DOMParser()
        const doc = parser.parseFromString(this.inputHtml, 'text/html')

        const matchedNodes = doc.querySelectorAll(this.cssSelector)
        const matched = Array.from(matchedNodes).map(node => this.elementToObject(node))

        this.matchedElements = matched
        this.applyFilters()

        // 添加到历史记录
        this.addToHistory(this.cssSelector, matched.length)

        if (matched.length === 0) {
          this.$message.warning('未找到匹配的元素')
        } else {
          this.$message.success(`找到 ${matched.length} 个匹配元素`)
        }
      } catch (error) {
        console.error('选择器执行失败:', error)
        this.$message.error('选择器语法错误，请检查')
      } finally {
        this.isExecuting = false
      }
    },

    // 选择器变化处理
    onSelectorChange() {
      // 实时执行（防抖）
      clearTimeout(this.selectorTimeout)
      this.selectorTimeout = setTimeout(() => {
        if (this.cssSelector.trim() && this.parsedElements.length > 0) {
          this.executeSelector()
        }
      }, 500)
    },

    // 使用快捷选择器
    useQuickSelector(selector) {
      this.cssSelector = selector
      this.executeSelector()
    },

    // 使用历史选择器
    useHistorySelector(item) {
      this.cssSelector = item.selector
      this.executeSelector()
    },

    // 添加到历史记录
    addToHistory(selector, count) {
      // 检查是否已存在
      const existingIndex = this.selectorHistory.findIndex(item => item.selector === selector)

      if (existingIndex >= 0) {
        // 更新现有记录
        this.selectorHistory[existingIndex].count = count
        this.selectorHistory[existingIndex].timestamp = Date.now()
      } else {
        // 添加新记录
        this.selectorHistory.unshift({
          selector,
          count,
          timestamp: Date.now()
        })
      }

      // 限制历史记录数量
      if (this.selectorHistory.length > 10) {
        this.selectorHistory = this.selectorHistory.slice(0, 10)
      }
    },

    // 应用过滤器
    applyFilters() {
      let results = [...this.matchedElements]

      // 按标签过滤
      if (this.filterTag) {
        results = results.filter(el => el.tagName === this.filterTag)
      }

      // 按类型过滤
      if (this.filterType) {
        results = results.filter(el => this.getElementType(el) === this.filterType)
      }

      // 按文本过滤
      if (this.filterText) {
        const text = this.filterText.toLowerCase()
        results = results.filter(el => el.textContent.toLowerCase().includes(text) || el.tagName.toLowerCase().includes(text) || el.className.toLowerCase().includes(text) || el.id.toLowerCase().includes(text))
      }

      this.filteredResults = results
      this.displayLimit = 100 // 重置显示限制
    },

    // 获取元素类型
    getElementType(element) {
      const tag = element.tagName.toLowerCase()
      const interactiveElements = ['button', 'a', 'input', 'select', 'textarea']
      const formElements = ['input', 'select', 'textarea', 'form', 'label']
      const textElements = ['p', 'span', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']
      const mediaElements = ['img', 'video', 'audio', 'canvas', 'svg']

      if (interactiveElements.includes(tag)) return 'interactive'
      if (formElements.includes(tag)) return 'form'
      if (textElements.includes(tag)) return 'text'
      if (mediaElements.includes(tag)) return 'media'
      return 'other'
    },

    // 选择元素
    selectElement(element) {
      this.selectedElement = this.selectedElement === element ? null : element
    },

    // 格式化类名
    formatClasses(className) {
      if (!className) return ''
      // className 在这里已经是字符串类型
      const classes = className.split(/\s+/).filter(c => c.trim())
      if (classes.length <= 3) {
        return '.' + classes.join('.')
      }
      return '.' + classes.slice(0, 3).join('.') + `... (+${classes.length - 3})`
    },

    // 生成CSS选择器
    generateSelectors(element) {
      const selectors = []

      // ID选择器
      if (element.id) {
        selectors.push(`#${element.id}`)
      }

      // 类选择器
      if (element.className) {
        // element.className 在 elementToObject 中已经被处理为字符串
        const classes = element.className.split(/\s+/).filter(c => c.trim())
        if (classes.length > 0) {
          selectors.push(`.${classes[0]}`)
          if (classes.length > 1) {
            selectors.push(`.${classes.join('.')}`)
          }
        }
      }

      // 标签选择器
      selectors.push(element.tagName)

      // 属性选择器
      Object.keys(element.attributes).forEach(attr => {
        if (['id', 'class'].includes(attr)) return
        const value = element.attributes[attr]
        if (value) {
          selectors.push(`[${attr}="${value}"]`)
        } else {
          selectors.push(`[${attr}]`)
        }
      })

      return selectors.slice(0, 5) // 限制数量
    },

    // 生成Playwright选择器
    generatePlaywrightSelectors(element, elementIndex = 0) {
      const selectors = []
      const elementCount = this.getSelectorCount(element)
      const selectorIndex = this.getSelectorIndex(element, elementIndex)
      const nthSuffix = elementCount > 1 ? `.nth(${selectorIndex - 1})` : ''

      // getByRole
      if (element.attributes.role) {
        const baseSelector = `page.getByRole('${element.attributes.role}')`
        selectors.push(baseSelector + nthSuffix)
      }

      // locator
      if (element.id) {
        const baseSelector = `page.locator('#${element.id}')`
        selectors.push(baseSelector + nthSuffix)
      } else if (element.className) {
        // element.className 在 elementToObject 中已经被处理为字符串
        const firstClass = element.className.split(/\s+/)[0]
        const baseSelector = `page.locator('.${firstClass}')`
        selectors.push(baseSelector + nthSuffix)
      } else {
        // 使用标签名作为选择器
        const baseSelector = `page.locator('${element.tagName}')`
        selectors.push(baseSelector + nthSuffix)
      }

      // getByText
      if (element.textContent && element.textContent.length < 50) {
        const cleanText = element.textContent.replace(/'/g, "\\'")
        const baseSelector = `page.getByText('${cleanText}')`
        selectors.push(baseSelector + nthSuffix)
      }

      // getByLabel
      if (element.attributes['aria-label']) {
        const baseSelector = `page.getByLabel('${element.attributes['aria-label']}')`
        selectors.push(baseSelector + nthSuffix)
      }

      // getByPlaceholder
      if (element.attributes.placeholder) {
        const baseSelector = `page.getByPlaceholder('${element.attributes.placeholder}')`
        selectors.push(baseSelector + nthSuffix)
      }

      // getByTestId
      if (element.attributes['data-testid']) {
        const baseSelector = `page.getByTestId('${element.attributes['data-testid']}')`
        selectors.push(baseSelector + nthSuffix)
      }

      return selectors.slice(0, 3)
    },

    // 使用选择器
    useSelector(selector) {
      this.cssSelector = selector
      this.executeSelector()
    },

    // 复制元素选择器
    copyElementSelector(element) {
      const selector = element.id ? `#${element.id}` : element.className ? `.${element.className.split(/\s+/)[0]}` : element.tagName
      this.copyToClipboard(selector)
    },

    // 高亮元素（模拟）
    highlightElement(element) {
      this.$message.info(`高亮元素: ${element.tagName}${element.id ? '#' + element.id : ''}`)
    },

    // 复制到剪贴板
    async copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        this.$message.success('已复制到剪贴板')
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('已复制到剪贴板')
      }
    },

    // 复制结果
    copyResults() {
      const results = this.filteredResults.map(el => ({
        selector: el.id ? `#${el.id}` : el.className ? `.${el.className.split(/\s+/)[0]}` : el.tagName,
        tagName: el.tagName,
        id: el.id,
        className: el.className,
        textContent: el.textContent
      }))

      this.copyToClipboard(JSON.stringify(results, null, 2))
    },

    // 导出结果
    exportResults() {
      const results = this.filteredResults.map(el => ({
        selector: el.id ? `#${el.id}` : el.className ? `.${el.className.split(/\s+/)[0]}` : el.tagName,
        tagName: el.tagName,
        id: el.id,
        className: el.className,
        textContent: el.textContent,
        attributes: el.attributes,
        path: el.path
      }))

      const dataStr = JSON.stringify(results, null, 2)
      const blob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(blob)

      const a = document.createElement('a')
      a.href = url
      a.download = `element-selector-results-${Date.now()}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      this.$message.success('结果已导出')
    },

    // 加载更多
    loadMore() {
      this.displayLimit += 100
    },

    // 应用全局过滤器（用于所有元素）
    applyGlobalFilters() {
      let results = [...this.parsedElements]

      // 智能筛选
      if (this.isSmartFilterActive) {
        results = this.applySmartFilter(results)
      }

      // 按标签过滤
      if (this.globalFilterTag) {
        results = results.filter(el => el.tagName === this.globalFilterTag)
      }

      // 按类型过滤
      if (this.globalFilterType) {
        results = results.filter(el => this.getElementType(el) === this.globalFilterType)
      }

      // 按文本过滤
      if (this.globalFilterText) {
        const text = this.globalFilterText.toLowerCase()
        results = results.filter(el => el.textContent.toLowerCase().includes(text) || el.tagName.toLowerCase().includes(text) || el.className.toLowerCase().includes(text) || el.id.toLowerCase().includes(text))
      }

      this.filteredAllElements = results
      this.allElementsDisplayLimit = 100 // 重置显示限制
    },

    // 获取相同选择器的数量（在当前显示的过滤结果中）
    getSelectorCount(element) {
      const selector = this.getElementSelector(element)
      return this.filteredAllElements.filter(el => this.getElementSelector(el) === selector).length
    },

    // 获取元素在相同选择器中的索引
    getSelectorIndex(element, currentIndex) {
      const selector = this.getElementSelector(element)
      const sameElements = this.filteredAllElements.slice(0, currentIndex + 1).filter(el => this.getElementSelector(el) === selector)
      return sameElements.length
    },

    // 获取元素的主要选择器
    getElementSelector(element) {
      if (element.id) return `#${element.id}`
      if (element.className) {
        const firstClass = element.className.split(/\s+/)[0]
        return `.${firstClass}`
      }
      return element.tagName
    },

    // 使用元素作为选择器
    useElementAsSelector(element) {
      const selector = this.getElementSelector(element)
      this.cssSelector = selector
      this.executeSelector()
    },

    // 复制所有元素
    copyAllElements() {
      const results = this.filteredAllElements.map((el, index) => ({
        index: index + 1,
        selector: this.getElementSelector(el),
        tagName: el.tagName,
        id: el.id,
        className: el.className,
        textContent: el.textContent,
        selectorIndex: this.getSelectorIndex(el, index),
        selectorCount: this.getSelectorCount(el)
      }))

      this.copyToClipboard(JSON.stringify(results, null, 2))
    },

    // 导出所有元素
    exportAllElements() {
      const results = this.filteredAllElements.map((el, index) => ({
        index: index + 1,
        selector: this.getElementSelector(el),
        tagName: el.tagName,
        id: el.id,
        className: el.className,
        textContent: el.textContent,
        attributes: el.attributes,
        path: el.path,
        selectorIndex: this.getSelectorIndex(el, index),
        selectorCount: this.getSelectorCount(el)
      }))

      const dataStr = JSON.stringify(results, null, 2)
      const blob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(blob)

      const a = document.createElement('a')
      a.href = url
      a.download = `all-elements-${Date.now()}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      this.$message.success('所有元素已导出')
    },

    // 加载更多所有元素
    loadMoreAllElements() {
      this.allElementsDisplayLimit += 100
    },

    // 清空输入
    clearInput() {
      this.inputHtml = ''
      this.parsedElements = []
      this.matchedElements = []
      this.filteredResults = []
      this.filteredAllElements = []
      this.selectedElement = null
      this.cssSelector = ''

      // 重置全局过滤器
      this.globalFilterTag = ''
      this.globalFilterType = ''
      this.globalFilterText = ''

      // 重置智能筛选
      this.isSmartFilterActive = false
    },

    // 获取元素类型颜色（用于Tag）
    getElementTypeColor(tagName) {
      const colors = {
        input: 'primary',
        button: 'success',
        a: 'info',
        select: 'warning',
        textarea: 'primary',
        div: '',
        span: '',
        p: 'info'
      }
      return colors[tagName.toLowerCase()] || ''
    },

    // 获取元素显示文本
    getElementDisplayText(element) {
      if (element.textContent && element.textContent.trim()) {
        return this.truncateText(element.textContent, 30)
      }
      if (element.id) {
        return `#${element.id}`
      }
      if (element.className) {
        const firstClass = element.className.split(/\s+/)[0]
        return `.${firstClass}`
      }
      if (element.attributes && element.attributes.placeholder) {
        return element.attributes.placeholder
      }
      if (element.attributes && element.attributes.name) {
        return `name="${element.attributes.name}"`
      }
      return `${element.tagName}元素`
    },

    // 获取主要属性（过滤掉不重要的属性）
    getMainAttributes(attributes) {
      const mainAttrs = {}
      const importantAttrs = ['id', 'class', 'name', 'type', 'placeholder', 'href', 'src', 'alt', 'title', 'data-testid']

      for (const [key, value] of Object.entries(attributes)) {
        if (importantAttrs.includes(key) || key.startsWith('data-')) {
          mainAttrs[key] = value
        }
      }

      // 如果没有重要属性，返回前3个属性
      if (Object.keys(mainAttrs).length === 0) {
        const entries = Object.entries(attributes).slice(0, 3)
        for (const [key, value] of entries) {
          mainAttrs[key] = value
        }
      }

      return mainAttrs
    },

    // 修改truncateText方法，支持自定义长度
    truncateText(text, maxLength = 50) {
      if (!text) return ''
      const cleaned = text.replace(/\s+/g, ' ').trim()
      return cleaned.length > maxLength ? cleaned.substring(0, maxLength) + '...' : cleaned
    },

    // 智能筛选相关方法
    // 计算元素重要性分数
    calculateElementScore(element) {
      let score = 0
      const tagName = element.tagName.toLowerCase()

      // 装饰性标签直接返回0分
      const decorativeElements = ['style', 'script', 'meta', 'link', 'head', 'title', 'noscript']
      if (decorativeElements.includes(tagName)) {
        return 0
      }

      // 基础分数 - 根据标签类型
      const interactiveElements = ['button', 'input', 'select', 'textarea', 'a']
      const formElements = ['form', 'label', 'fieldset', 'legend']
      const importantContainers = ['main', 'section', 'article', 'nav', 'header', 'footer']
      const textElements = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span']

      if (interactiveElements.includes(tagName)) {
        score += 8 // 交互元素高分
      } else if (formElements.includes(tagName)) {
        score += 7 // 表单相关元素
      } else if (importantContainers.includes(tagName)) {
        score += 6 // 重要容器元素
      } else if (textElements.includes(tagName)) {
        score += 4 // 文本元素
      } else if (tagName === 'div' || tagName === 'span') {
        score += 2 // 普通容器元素
      } else {
        score += 3 // 其他元素
      }

      // 属性加分
      if (element.id) {
        score += 2 // 有ID属性
      }
      if (element.attributes && element.attributes['data-testid']) {
        score += 2 // 有测试ID
      }
      if (element.attributes && element.attributes.name) {
        score += 1 // 有name属性
      }
      if (element.attributes && (element.attributes.role || element.attributes['aria-label'])) {
        score += 1 // 有无障碍属性
      }
      if (element.className && element.className.trim()) {
        score += 0.5 // 有class属性
      }

      // 内容加分
      if (element.textContent && element.textContent.trim()) {
        const textLength = element.textContent.trim().length
        if (textLength > 0 && textLength < 100) {
          score += 1 // 有合适长度的文本内容
        }
      }

      // 特殊属性加分
      if (element.attributes) {
        const attrs = element.attributes
        if (attrs.type === 'submit' || attrs.type === 'button') {
          score += 1 // 提交按钮或按钮类型
        }
        if (attrs.href) {
          score += 1 // 链接
        }
        if (attrs.placeholder) {
          score += 0.5 // 有占位符
        }
      }

      return Math.round(score * 10) / 10 // 保留一位小数
    },

    // 应用智能筛选
    applySmartFilter(elements) {
      console.log('开始智能筛选，输入元素数量:', elements.length)

      // 为每个元素计算分数
      const scoredElements = elements.map(element => ({
        ...element,
        smartScore: this.calculateElementScore(element)
      }))

      console.log(
        '所有元素评分:',
        scoredElements.map(el => ({
          tag: el.tagName,
          id: el.id,
          score: el.smartScore
        }))
      )

      // 按分数筛选
      let filteredElements = scoredElements.filter(element => element.smartScore >= this.smartFilterThreshold)
      console.log('分数筛选后元素数量:', filteredElements.length, '阈值:', this.smartFilterThreshold)

      // 对相同选择器的元素进行去重，保留前3个
      const selectorGroups = {}
      filteredElements.forEach(element => {
        const selector = this.getElementSelector(element)
        if (!selectorGroups[selector]) {
          selectorGroups[selector] = []
        }
        selectorGroups[selector].push(element)
      })

      // 每个选择器组最多保留3个元素
      const finalElements = []
      Object.values(selectorGroups).forEach(group => {
        // 按分数排序，取前3个
        const sortedGroup = group.sort((a, b) => b.smartScore - a.smartScore)
        finalElements.push(...sortedGroup.slice(0, 3))
      })

      // 按分数排序最终结果
      const result = finalElements.sort((a, b) => b.smartScore - a.smartScore)
      console.log('最终筛选结果数量:', result.length)

      return result
    },

    // 切换智能筛选
    toggleSmartFilter() {
      // 检查是否有解析的元素
      if (this.parsedElements.length === 0) {
        this.$message.warning('请先解析HTML代码再使用智能筛选功能')
        return
      }

      this.isSmartFilterActive = !this.isSmartFilterActive

      if (this.isSmartFilterActive) {
        this.$message.success('智能筛选已启用，将优先显示重要的可测试元素')
      } else {
        this.$message.info('智能筛选已关闭，显示所有元素')
      }

      // 重新应用过滤器
      this.applyGlobalFilters()

      // 调试信息
      if (this.isSmartFilterActive) {
        console.log('智能筛选调试信息:')
        console.log('原始元素数量:', this.parsedElements.length)
        console.log('筛选后元素数量:', this.filteredAllElements.length)
        console.log('筛选阈值:', this.smartFilterThreshold)

        // 显示前5个元素的评分
        const testElements = this.parsedElements.slice(0, 5).map(el => ({
          tagName: el.tagName,
          id: el.id,
          className: el.className,
          score: this.calculateElementScore(el)
        }))
        console.log('前5个元素评分:', testElements)
      }
    }
  },

  beforeDestroy() {
    if (this.selectorTimeout) {
      clearTimeout(this.selectorTimeout)
    }
  }
}
</script>

<style scoped>
/* 导入WebUI自动化测试智能体样式 */
@import '../../../../../assets/css/style.css';

.element-select {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.element-select-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
  overflow-y: auto;
  /* padding: 1rem; */
}

/* 通用样式 */
.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.section-title i {
  color: var(--primary-color);
}

.smart-filter-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  margin-left: 1rem;
  font-weight: 500;
}

.smart-filter-indicator i {
  color: #ff6b6b;
}

.title-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: auto;
}

/* 输入区域 */

.selector-section,
.history-section,
.stats-section,
.results-section,
.all-elements-section {
  background-color: var(--card-bg-color);
  border-radius: var(--radius);
  padding: 1rem;
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-color);
}

.input-container {
  background-color: var(--background-color);
  border-radius: var(--radius);
  padding: 1rem;
  border: 1px solid var(--border-color);
}

.html-input {
  width: 100%;
  min-height: 200px;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  resize: vertical;
  background-color: var(--card-bg-color);
  color: var(--text-color);
  transition: var(--transition);
}

.html-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.input-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
  justify-content: flex-end;
}

/* 选择器区域 */
.selector-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.selector-input-group {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.selector-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 0.95rem;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: var(--transition);
}

.selector-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.selector-btn {
  min-width: 100px;
}

.quick-selectors {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.quick-selector-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-light);
}

.quick-selector-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* 历史记录 */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: var(--background-color);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: var(--transition);
}

.history-item:hover {
  background-color: rgba(var(--primary-color-rgb), 0.05);
  border-color: var(--primary-color);
}

.history-selector {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  color: var(--text-color);
}

.history-count {
  font-size: 0.8rem;
  color: var(--text-light);
  background-color: rgba(var(--primary-color-rgb), 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
}

/* 统计区域 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background-color: var(--background-color);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-light);
  font-weight: 500;
}

/* 结果过滤器 */
.result-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-light);
}

.filter-select,
.filter-input {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--background-color);
  color: var(--text-color);
  font-size: 0.9rem;
}

.filter-input {
  min-width: 200px;
}

/* 结果列表 */
.results-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 元素网格布局 */
.elements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
}

/* 元素卡片样式 */
.element-card {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: 1rem;
  background: var(--background-color);
  cursor: pointer;
  transition: var(--transition);
}

.element-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.element-card.selected {
  border-color: var(--primary-color);
  background: rgba(var(--primary-color-rgb), 0.05);
}

.element-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.element-text {
  flex: 1;
  font-weight: 500;
  color: var(--text-color);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 0.5rem;
}

.element-index {
  background: var(--warning-color);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
  margin-right: 0.5rem;
}

.element-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.85rem;
}

.detail-item .label {
  color: var(--text-light);
  font-weight: 600;
}

.detail-value {
  color: var(--text-color);
  background: var(--card-bg-color);
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  line-height: 1.4;
  word-break: break-word;
}

.attributes-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.attribute-item {
  background: var(--card-bg-color);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  color: var(--text-color);
}

.selector-suggestions {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.playwright-selectors {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.selector-code,
.playwright-code {
  background: var(--card-bg-color) !important;
  padding: 0.5rem !important;
  border-radius: var(--radius-sm) !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 0.75rem !important;
  cursor: pointer !important;
  transition: var(--transition) !important;
  border: 1px solid var(--border-color) !important;
  display: block !important;
}

.selector-code:hover,
.playwright-code:hover {
  background: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
}

.result-item {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  transition: var(--transition);
  overflow: hidden;
}

.result-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.result-item.active {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  cursor: pointer;
}

.element-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.element-tag {
  background-color: var(--primary-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.element-id {
  background-color: var(--success-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 500;
}

.element-classes {
  background-color: var(--warning-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 500;
}

.element-index {
  background-color: var(--accent-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.element-actions {
  display: flex;
  gap: 0.5rem;
}

.result-details {
  padding: 0 1rem 1rem;
  border-top: 1px solid var(--border-color);
  background-color: rgba(var(--primary-color-rgb), 0.02);
}

.detail-row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color);
}

.detail-value {
  font-size: 0.9rem;
  color: var(--text-light);
  word-break: break-word;
}

.attributes-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.attribute-tag {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.selector-suggestions,
.playwright-selectors {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.selector-code,
.playwright-code {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85rem;
  cursor: pointer;
  transition: var(--transition);
}

.selector-code:hover,
.playwright-code:hover {
  background-color: rgba(var(--primary-color-rgb), 0.05);
  border-color: var(--primary-color);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-primary.smart-filter-active {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-primary.smart-filter-active:hover:not(:disabled) {
  background: linear-gradient(135deg, #ee5a24, #ff6b6b);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
  background: var(--text-light);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: var(--text-color);
  transform: translateY(-1px);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-info {
  background-color: var(--accent-color);
  color: white;
}

.btn-info:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: white;
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

.btn-tiny {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* 无结果提示 */
.no-results {
  text-align: center;
  padding: 3rem 2rem;
  background-color: var(--card-bg-color);
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.no-results-icon {
  font-size: 3rem;
  color: var(--text-light);
  margin-bottom: 1rem;
}

.no-results-text h4 {
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.no-results-text p {
  color: var(--text-light);
  margin-bottom: 1.5rem;
}

.no-results-suggestions {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.no-results-suggestions h5 {
  color: var(--text-color);
  margin-bottom: 0.75rem;
}

.no-results-suggestions ul {
  color: var(--text-light);
  padding-left: 1.5rem;
}

.no-results-suggestions li {
  margin-bottom: 0.5rem;
}

.no-results-suggestions code {
  background-color: var(--background-color);
  padding: 0.2rem 0.4rem;
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 1rem;
}

/* 动画 */
.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .element-select-content {
    padding: 0.5rem;
    gap: 1rem;
  }

  .input-section,
  .selector-section,
  .history-section,
  .stats-section,
  .results-section {
    padding: 1rem;
  }

  .selector-input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .result-filters {
    flex-direction: column;
    gap: 0.5rem;
  }

  .filter-group {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-input {
    min-width: auto;
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .elements-grid {
    grid-template-columns: 1fr;
  }
}
</style>
