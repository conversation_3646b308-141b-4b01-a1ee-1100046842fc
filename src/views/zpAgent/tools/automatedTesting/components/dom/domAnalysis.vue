<template>
  <div class="dom-analysis">
    <div class="generation-content">
      <div class="left-panel">
        <div class="input-section">
          <div class="panel-header">
            <div class="section-title">分析类型</div>
          </div>
          <div class="analysis-types">
            <div v-for="(type, index) in analysisTypes" :key="index" class="analysis-type" :class="{ active: activeAnalysisType === type.type }" :data-type="type.type" @click="switchAnalysisType(type.type)">
              <div class="analysis-type-icon"><i :class="type.icon"></i></div>
              <span>{{ type.label }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="right-panel">
        <div class="result-section">
          <!-- <div class="section-header">
            <div class="section-title">分析结果</div>
          </div> -->

          <!-- 清理HTML -->
          <htmlClean v-show="activeAnalysisType === 'clean'" ref="htmlClean" />

          <!-- 提取元素 -->
          <elementSelect v-show="activeAnalysisType === 'extract'" ref="elementSelect" />

          <!-- DOM AI -->
          <domAI v-show="activeAnalysisType === 'domAI'" ref="domAI" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import htmlClean from './components/htmlCleaner.vue'
import elementSelect from './components/elementSelect.vue'
import domAI from './components/domAI.vue'

export default {
  name: 'caseGeneration',
  data() {
    return {
      activeAnalysisType: 'clean',
      // 选项卡配置
      analysisTypes: [
        { type: 'clean', label: '清理HTML', icon: 'ri-brush-line' },
        { type: 'extract', label: '提取元素', icon: 'ri-code-s-slash-line' },
        { type: 'generate', label: '生成测试', icon: 'ri-hammer-line' },
        { type: 'analyze', label: 'AI分析', icon: 'ri-brain-line' },
        { type: 'domAI', label: 'DOM AI', icon: 'ri-brush-line' }
      ]
    }
  },
  components: {
    htmlClean,
    elementSelect,
    domAI
  },

  methods: {
    // 切换分析类型
    switchAnalysisType(type) {
      this.activeAnalysisType = type
    }
  },
  created() {}
}
</script>

<style scoped>
.dom-analysis {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-header {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--text-color);
}

.generation-content {
  display: flex;
  gap: 15px;
  flex: 1;
  height: 100%;
}

.left-panel {
  min-width: 250px;
  width: 250px;
  display: flex;
  flex-direction: column;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 10px;
  color: var(--text-color);
}

.input-section,
.result-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

/* 分析类型选择 */
.analysis-types {
  margin-bottom: 20px;
}

.analysis-type {
  padding: 10px 15px;
  border-radius: var(--radius-sm);
  margin-bottom: 8px;
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
  display: flex;
  align-items: center;
  color: var(--text-color);
}

.analysis-type:hover {
  background-color: rgba(var(--primary-color-rgb), 0.05);
  transform: translateX(3px);
}

.analysis-type.active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  font-weight: 500;
  border-left: 3px solid var(--primary-color);
}

.analysis-type-icon {
  margin-right: 8px;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
