<template>
  <div class="zp-agent auto-agent-chat">
    <div class="chat-container">
      <!-- 聊天主区域 -->
      <div class="chat-main">
        <!-- 聊天头部 -->
        <div class="chat-header">
          <div class="chat-title">
            <img src="@/views/zpAgent/assets/images/agent1.svg" alt="" class="chat-title-icon" />
            <div class="chat-title-name">WebUI自动化测试智能体</div>
          </div>
        </div>

        <!-- 聊天消息区域 -->
        <div class="chat-messages" ref="chatMessages">
          <!-- 欢迎消息 -->
          <div class="message agent" v-if="messages.length === 0">
            <img src="@/views/zpAgent/assets/images/system.svg" alt="" class="message-avatar" />
            <div class="agent-content-wrapper">
              <div class="message-content" style="padding: 0.8rem 1rem">
                <div class="welcome-content">
                  <h3>👋 欢迎使用WebUI自动化测试智能体</h3>
                  <p>我可以帮助您：</p>
                  <ul>
                    <li>🎯 生成自动化测试用例</li>
                    <li>🔧 优化测试脚本</li>
                    <li>📊 分析测试结果</li>
                    <li>💡 提供测试建议</li>
                  </ul>
                  <p style="margin-top: 1rem; color: var(--text-light)">请输入您的问题或选择下方推荐问题开始对话</p>
                </div>
              </div>
              <!-- 推荐问题 -->
              <div class="message-actions">
                <div class="message-actions-left">
                  <div class="suggested-questions">
                    <div v-for="(question, index) in suggestedQuestions" :key="index" class="suggested-question" @click="sendSuggestedQuestion(question)">
                      {{ question }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 对话消息 -->
          <template v-for="(message, index) in messages">
            <!-- 用户消息 -->
            <div class="message user" v-if="message.type === 'user'" :key="'user-' + index">
              <div>
                <div class="message-content message-content-user">
                  <pre class="message-content-user-query">{{ message.content }}</pre>
                </div>
                <div class="message-actions">
                  <div class="message-actions-left">
                    <div class="message-action-btn" title="复制消息" @click="copyToClipboard(message.content)">
                      <i class="ri-file-copy-line"></i>
                    </div>
                  </div>
                </div>
              </div>
              <img src="@/views/zpAgent/assets/images/user.svg" alt="" class="message-avatar" />
            </div>

            <!-- AI回复 -->
            <div class="message agent" v-if="message.type === 'agent'" :key="'agent-' + index">
              <img src="@/views/zpAgent/assets/images/system.svg" alt="" class="message-avatar" />
              <div class="message-content-wrapper">
                <div class="agent-content-wrapper">
                  <div class="message-content message-content-agent" :class="{ streaming: message.streaming }">
                    <div v-if="message.streaming" class="typing-indicator">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                    <div v-else v-html="formatMessage(message.content)"></div>
                  </div>
                </div>
                <!-- AI消息操作 -->
                <div class="message-actions" v-if="!message.streaming">
                  <div class="message-actions-right">
                    <span class="message-action-btn" title="复制消息" @click="copyToClipboard(message.content)">
                      <i class="ri-file-copy-line"></i>
                    </span>
                    <span class="message-action-btn" title="再次提问" @click="resendMessage(message.originalQuery)">
                      <i class="ri-refresh-line"></i>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-container" :class="{ isStreaming: isStreaming }">
          <textarea class="chat-input" placeholder="输入您的问题或指令..." rows="2" v-model="inputMessage" @keyup.ctrl.enter="sendMessage" @keydown.enter.exact.prevent="handleEnterPress" :disabled="isStreaming"></textarea>
          <div class="chat-input-actions-container">
            <div class="chat-input-actions">
              <div class="chat-input-actions-left">
                <span class="input-tip">Ctrl + Enter 发送</span>
              </div>
              <button type="button" class="btn-send" :disabled="isStreaming || !inputMessage.trim()" @click="sendMessage">
                <i class="ri-send-plane-fill" v-if="!isStreaming"></i>
                <i class="ri-loader-line loading-icon" v-else></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AutoAgent',
  data() {
    return {
      inputMessage: '',
      messages: [],
      isStreaming: false,
      suggestedQuestions: ['如何生成一个登录页面的自动化测试用例？', '帮我优化这个测试脚本的性能', '如何处理动态加载的页面元素？', '测试用例执行失败了，如何调试？', '如何设置测试数据和环境配置？']
    }
  },
  methods: {
    // 发送消息
    async sendMessage() {
      if (!this.inputMessage.trim() || this.isStreaming) {
        return
      }

      const userMessage = this.inputMessage.trim()
      this.inputMessage = ''

      // 添加用户消息
      this.messages.push({
        type: 'user',
        content: userMessage,
        timestamp: new Date()
      })

      // 添加AI回复占位符
      const aiMessage = {
        type: 'agent',
        content: '',
        streaming: true,
        originalQuery: userMessage,
        timestamp: new Date()
      }
      this.messages.push(aiMessage)

      this.isStreaming = true
      this.scrollToBottom()

      try {
        // 模拟AI回复
        await this.simulateAIResponse(aiMessage, userMessage)
      } catch (error) {
        console.error('发送消息失败:', error)
        aiMessage.content = '抱歉，发生了错误，请稍后重试。'
        aiMessage.streaming = false
      } finally {
        this.isStreaming = false
      }
    },

    // 模拟AI回复
    async simulateAIResponse(aiMessage, userQuery) {
      // 根据用户问题生成相应的回复
      let response = this.generateResponse(userQuery)

      // 模拟打字效果
      for (let i = 0; i <= response.length; i++) {
        aiMessage.content = response.substring(0, i)
        await this.sleep(30) // 打字速度
        this.scrollToBottom()
      }

      aiMessage.streaming = false
    },

    // 生成回复内容
    generateResponse(query) {
      const lowerQuery = query.toLowerCase()

      if (lowerQuery.includes('登录') || lowerQuery.includes('测试用例')) {
        return `我来帮您生成登录页面的自动化测试用例：

**测试用例设计思路：**

1. **正常登录流程测试**
   - 输入正确的用户名和密码
   - 点击登录按钮
   - 验证登录成功跳转

2. **异常情况测试**
   - 用户名为空
   - 密码为空
   - 用户名或密码错误
   - 验证码错误（如果有）

**示例代码：**
\`\`\`javascript
// 登录测试用例
test('用户登录功能测试', async ({ page }) => {
  await page.goto('https://example.com/login');

  // 输入用户名
  await page.fill('#username', 'testuser');

  // 输入密码
  await page.fill('#password', 'password123');

  // 点击登录按钮
  await page.click('#loginBtn');

  // 验证登录成功
  await expect(page).toHaveURL(/.*dashboard/);
});
\`\`\`

您需要我为特定的登录页面生成更详细的测试用例吗？`
      }

      if (lowerQuery.includes('优化') || lowerQuery.includes('性能')) {
        return `我来帮您优化测试脚本的性能：

**性能优化建议：**

1. **等待策略优化**
   - 使用智能等待替代固定延时
   - 利用 \`page.waitForSelector()\` 等待元素出现

2. **并行执行**
   - 使用 \`test.describe.parallel()\` 并行运行测试
   - 合理设置并发数量

3. **资源优化**
   - 禁用不必要的资源加载（图片、CSS等）
   - 使用无头模式运行

**优化示例：**
\`\`\`javascript
// 优化前
await page.waitForTimeout(3000);

// 优化后
await page.waitForSelector('#element', { timeout: 10000 });
\`\`\`

请分享您的具体脚本，我可以提供更针对性的优化建议。`
      }

      if (lowerQuery.includes('动态') || lowerQuery.includes('加载')) {
        return `处理动态加载页面元素的最佳实践：

**解决方案：**

1. **智能等待**
   \`\`\`javascript
   // 等待元素出现
   await page.waitForSelector('.dynamic-content');

   // 等待网络请求完成
   await page.waitForLoadState('networkidle');
   \`\`\`

2. **监听网络请求**
   \`\`\`javascript
   // 等待特定API请求完成
   await page.waitForResponse(response =>
     response.url().includes('/api/data') && response.status() === 200
   );
   \`\`\`

3. **轮询检查**
   \`\`\`javascript
   // 轮询等待条件满足
   await page.waitForFunction(() => {
     return document.querySelectorAll('.item').length > 0;
   });
   \`\`\`

您遇到的是哪种类型的动态加载问题？我可以提供更具体的解决方案。`
      }

      // 默认回复
      return `感谢您的问题！作为WebUI自动化测试智能体，我可以帮助您：

🎯 **测试用例生成**
- 根据页面功能生成完整的测试用例
- 提供最佳实践和代码示例

🔧 **脚本优化**
- 性能优化建议
- 代码重构和改进

📊 **问题诊断**
- 测试失败原因分析
- 调试技巧和方法

💡 **技术咨询**
- 自动化测试框架选择
- 测试策略制定

请告诉我您具体遇到的问题，我会提供更详细的帮助！`
    },

    // 发送推荐问题
    sendSuggestedQuestion(question) {
      this.inputMessage = question
      this.sendMessage()
    },

    // 处理回车键
    handleEnterPress(event) {
      if (!event.shiftKey) {
        this.sendMessage()
      }
    },

    // 重新发送消息
    resendMessage(query) {
      this.inputMessage = query
      this.sendMessage()
    },

    // 复制到剪贴板
    async copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        this.$message.success('已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        this.$message.error('复制失败')
      }
    },

    // 格式化消息内容
    formatMessage(content) {
      // 简单的markdown格式化
      return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
        .replace(/\n/g, '<br>')
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const chatMessages = this.$refs.chatMessages
        if (chatMessages) {
          chatMessages.scrollTop = chatMessages.scrollHeight
        }
      })
    },

    // 延时函数
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style scoped>
@import '../../../../assets/css/style.css';

.auto-agent-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-container {
  display: flex;
  height: 100%;
  flex: 1;
  overflow: hidden;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color);
}

.chat-header {
  padding: 1rem 1.5rem;
  background-color: var(--card-bg-color);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.chat-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.chat-title-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-sm);
}

.chat-title-name {
  font-size: 1.1rem;
  font-weight: 600;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.message {
  display: flex;
  gap: 0.75rem;
  max-width: 100%;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.message-content-wrapper {
  flex: 1;
  min-width: 0;
}

.agent-content-wrapper {
  flex: 1;
  min-width: 0;
}

.message-content {
  background-color: var(--card-bg-color);
  border-radius: var(--radius);
  padding: 1rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.message-content-user {
  background: var(--primary-gradient);
  color: white;
  border: none;
}

.message-content-user-query {
  margin: 0;
  white-space: pre-wrap;
  font-family: inherit;
  font-size: 0.95rem;
  line-height: 1.5;
}

.message-content-agent {
  background-color: var(--card-bg-color);
}

.message-content-agent.streaming {
  position: relative;
}

.welcome-content h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.welcome-content p {
  margin-bottom: 0.75rem;
  color: var(--text-color);
  line-height: 1.6;
}

.welcome-content ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.welcome-content li {
  margin-bottom: 0.5rem;
  color: var(--text-color);
  line-height: 1.5;
}

.message-actions {
  margin-top: 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.message-actions-left {
  flex: 1;
}

.message-actions-right {
  display: flex;
  gap: 0.5rem;
}

.message-action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: var(--background-color);
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.message-action-btn:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.suggested-questions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.suggested-question {
  padding: 0.5rem 1rem;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  font-size: 0.9rem;
  color: var(--text-color);
  cursor: pointer;
  transition: var(--transition);
  max-width: 300px;
  text-align: center;
}

.suggested-question:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
  padding: 0.5rem 0;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary-color);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input-container {
  padding: 1rem 1.5rem;
  background-color: var(--card-bg-color);
  border-top: 1px solid var(--border-color);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.chat-input-container.isStreaming {
  opacity: 0.7;
  pointer-events: none;
}

.chat-input {
  width: 100%;
  min-height: 60px;
  max-height: 120px;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  font-size: 0.95rem;
  font-family: inherit;
  line-height: 1.5;
  background-color: var(--background-color);
  color: var(--text-color);
  resize: none;
  transition: var(--transition);
}

.chat-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.15);
}

.chat-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.chat-input-actions-container {
  margin-top: 0.75rem;
}

.chat-input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-input-actions-left {
  display: flex;
  align-items: center;
}

.input-tip {
  font-size: 0.8rem;
  color: var(--text-light);
}

.btn-send {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: var(--primary-gradient);
  color: white;
  cursor: pointer;
  transition: var(--transition);
  font-size: 1.1rem;
}

.btn-send:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-send:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 代码块样式 */
.message-content pre {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 1rem;
  margin: 0.5rem 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
}

.message-content code {
  background-color: #f6f8fa;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
}

.message-content pre code {
  background-color: transparent;
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-messages {
    padding: 0.75rem;
    gap: 1rem;
  }

  .chat-input-container {
    padding: 0.75rem 1rem;
  }

  .suggested-questions {
    flex-direction: column;
  }

  .suggested-question {
    max-width: none;
    text-align: left;
  }

  .message-actions-right {
    flex-direction: column;
    gap: 0.25rem;
  }
}
</style>
