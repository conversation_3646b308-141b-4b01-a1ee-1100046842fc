<template>
  <div class="case-generation record-case">
    <div class="generation-content">
      <div class="left-panel">
        <div class="input-section">
          <div class="panel-header">
            <span style="font-weight: 500">录制用例</span>
          </div>

          <div class="record-case-left">
            <el-form>
              <el-form-item label="浏览器">
                <el-select v-model="settings.browser" placeholder="请选择" required clearable>
                  <el-option label="Chrome" value="chrome"></el-option>
                  <el-option label="Firefox" value="firefox"></el-option>
                  <el-option label="WebKit" value="webkit"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="基础URL">
                <el-input v-model="settings.url" placeholder="默认: https://www.zhipin.com"></el-input>
              </el-form-item>

              <el-form-item label="预执行用例">
                <el-select v-model="settings.preScript" placeholder="请选择（可选）" clearable>
                  <el-option label="请选择" value=""></el-option>
                  <el-option v-for="item in caseList" :key="item.id" :label="item.caseName + '| ' + item.creator" :value="item.caseScript"></el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <div class="action-buttons">
            <button :class="isRecording ? 'btn btn-warning' : 'btn btn-primary'" size="small" @click="recordGenerateTestCase($event)" :loading="isRecording">
              <i :class="isRecording ? 'ri-stop-circle-line' : 'ri-play-circle-line'"></i>
              {{ isRecording ? '停止录制' : '开始录制' }}
            </button>
          </div>
        </div>

        <div class="input-section">
          <!-- <div class="panel-header">
            <span style="font-weight: 500">元素操作</span>
          </div> -->
          <div class="template-wrapper">
            <!-- <element-enhancer @insert-code="handleInsertCode" /> -->
            <element-select @insert-code="handleInsertCode" />
          </div>
        </div>
      </div>

      <div class="right-panel">
        <div class="result-section">
          <div class="panel-header">
            <span style="font-weight: 500">用例编辑器</span>
          </div>
          <div class="code-container">
            <div class="code-header">
              <div class="case-info">
                <span v-if="caseForm.id">用例：{{ caseForm.id }} {{ caseForm.caseName }}</span>
                <span v-else style="color: #909399">用例未保存</span>
              </div>
              <div class="code-actions">
                <div class="auto-save-wrapper">
                  <el-switch v-model="autoSaveEnabled" :loading="autoSaveLoading" active-text="自动保存" inactive-text="" @change="handleAutoSaveToggle"></el-switch>
                  <span v-if="autoSaveEnabled" class="save-time">{{ caseForm.updateTime | format }}</span>
                </div>
                <button class="btn btn-purple" size="small" @click="aiOptimizeTestCase(caseScript)">
                  <i class="ri-ai-generate-2"></i>
                  AI优化
                </button>
                <button :class="isDebugging ? 'btn btn-warning' : 'btn btn-primary'" size="small" @click="toggleDebugTestCase(caseForm)" :disabled="debugLoading">
                  <i class="ri-error-warning-line"></i>
                  <span v-if="debugLoading">启动中...</span>
                  <span v-else-if="isDebugging">停止调试</span>
                  <span v-else>调试</span>
                </button>
                <button class="btn btn-primary" size="small" @click="executeTestCase()">
                  <i class="ri-play-circle-line"></i>
                  执行
                </button>
                <button class="btn btn-outline" size="small" @click="addTestCase()">
                  <i class="ri-edit-line" style="vertical-align: middle"></i>
                  新建
                </button>
                <button class="btn btn-outline" size="small" @click="saveTestCase()">
                  <i class="ri-refresh-line" style="vertical-align: middle"></i>
                  更新
                </button>
              </div>
            </div>

            <!-- 用例脚本编辑器 -->
            <div class="code-content">
              <codeEdit v-model="caseScript" :height="400" language="javascript" theme="vs-dark" @change="onCmCodeChange" @save="saveTestCase" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加用例 -->
    <addCase ref="addCase" @update-case-form="updateCaseForm"></addCase>
  </div>
</template>

<script>
import { getPlaywrightServiceStatus, runPlaywrightTest, savePlaywrightScript, startPlaywrightRecording, stopPlaywrightRecording, startPlaywrightUI, stopPlaywrightUI, getPlaywrightUIStatus } from '@/api/agent/playwright'
import codeEdit from './codeEdit.vue'
import { getAutoCaseList, updateAutoCase } from '@/api/agent/autoCase'
import addCase from '@/views/zpAgent/tools/automatedTesting/components/case/addCase.vue'
import elementEnhancer from './elementEnhancer.vue'
import elementSelect from './elementSelect.vue'
import { formatDate } from '@/utils/api'

export default {
  name: 'caseGeneration',
  components: {
    codeEdit,
    addCase,
    elementEnhancer,
    elementSelect
  },
  data() {
    return {
      projectId: null,
      name: '',
      apiKey: 'app-K9Fy7mJKPLScEK9UsIoAilxo',
      baseUrl: 'https://www.zhipin.com',

      caseList: [],
      settings: {
        preScript: '',
        url: 'https://www.zhipin.com',
        browser: 'chrome'
      },
      //用例录制参数
      isRecording: false,
      recordingProcess: '',
      //用例调试参数
      debugLoading: false,
      isDebugging: false,
      debugPid: '',
      debugSuitePath: '',
      caseForm: {},
      // 调试状态管理
      debuggingCases: new Map(), // 存储正在调试的用例信息
      uiStatusCheckTimer: null, // UI状态检查定时器

      //用例生成参数
      generatedTestCase: '',
      isGenerating: false,
      guideVisible: false,
      caseScript: '',
      caseName: '',

      // 自动保存相关参数
      autoSaveEnabled: false, // 自动保存开关
      autoSaveLoading: false, // 自动保存加载状态
      autoSaveTimer: null // 自动保存定时器
    }
  },

  methods: {
    // 更新用例表单
    updateCaseForm(caseForm) {
      if (caseForm) {
        console.log('caseForm.status:', caseForm.status)
        console.log('caseForm:', caseForm)
        this.caseForm = JSON.parse(JSON.stringify(caseForm))
        this.caseScript = this.caseForm.caseScript
        this.caseName = this.caseForm.caseName
      }
    },

    // 更新项目ID并重新加载数据
    updateProjectId(projectId) {
      this.projectId = projectId
    },

    //获取用例列表
    async getAutoCaseList() {
      try {
        this.caseList = []
        let res = await getAutoCaseList(this.projectId, '', 0, '', '', 1, 1000)
        if (res.code === 0 && res.data) {
          res.data.list.forEach(item => {
            if (item.type == 'globalSetup') {
              this.caseList.push(item)
            }
          })
        }
      } catch (error) {
        console.error('获取用例列表失败:', error)
      }
    },

    // 启动Playwright录制
    recordGenerateTestCase(event) {
      event.stopPropagation()
      if (this.isRecording) {
        // 如果正在录制，停止录制
        this.stopRecording()
      } else {
        // 开始录制
        this.startRecording(this.settings)
      }
    },

    // 开始录制
    async startRecording(settings = {}) {
      this.dialogVisible = false
      this.$message({ type: 'success', message: '正在启动Playwright录制器...' })

      try {
        // 调用API启动录制，传递录制设置
        const response = await startPlaywrightRecording(settings)
        if (response.data.code === 0 && response.data.data) {
          this.isRecording = true
          this.recordingProcess = response.data.data.pid
          const preScriptMsg = settings.preScript ? '，已执行预脚本' : ''
          this.$message({ type: 'success', message: `录制器已启动${preScriptMsg}，请在弹出的窗口中操作` })
        } else {
          this.$message({ type: 'error', message: `录制器启动失败: ${response.data.msg || '未知错误'}` })
        }
      } catch (error) {
        this.$message({ type: 'error', message: `录制器启动失败: ${error.message}` })
      }
    },

    // 停止录制
    async stopRecording() {
      if (this.recordingProcess) {
        try {
          // 调用API停止录制
          const response = await stopPlaywrightRecording(this.recordingProcess)

          if (response.data.code === 0) {
            this.isRecording = false
            this.recordingProcess = null
            this.$message({ type: 'info', message: '录制已停止' })
            // 如果有生成的脚本，可以保存
            let recordCaseScript = response.data.data.script
            this.caseScript = recordCaseScript
            // 停止录制时将caseForm置空，表示这是一个新用例
            this.caseForm = {}
          } else {
            this.$message({ type: 'error', message: `停止录制失败: ${response.msg || '未知错误'}` })
          }
        } catch (error) {
          this.$message({ type: 'error', message: `停止录制失败: ${error.message}` })
        } finally {
          this.isRecording = false
          this.recordingProcess = null
        }
      }
    },

    // 切换调试状态（启动/停止调试）
    async toggleDebugTestCase(caseForm) {
      event.stopPropagation()
      if (this.isDebugging) {
        // 如果正在调试，则停止调试
        await this.stopDebugTestCase(caseForm)
      } else {
        // 如果未调试，则启动调试
        await this.startDebugTestCase(caseForm)
      }
    },

    // 启动调试
    async startDebugTestCase(caseForm) {
      try {
        // 设置加载状态
        this.debugLoading = true

        // 检查服务是否运行
        const response = await getPlaywrightServiceStatus()
        if (response.data.code !== 0 || response.data.data.status !== 'running') {
          this.$message({ type: 'error', message: '自动化测试服务未运行，请在配置页面启动服务' })
          return
        }

        // 脚本内容
        const scriptContent = this.caseScript || ''
        if (!scriptContent || caseForm.caseScript === '') {
          this.$message({ type: 'warning', message: '用例脚本内容为空，无法启动调试' })
          return
        }

        this.$message({ type: 'info', message: `正在启动调试模式: ${caseForm.caseName}` })

        // 构建测试套件对象（将单个用例包装成测试套件）
        const testSuite = {
          suiteName: `debug_${caseForm.caseName}_${Date.now()}`,
          beforeAll: '',
          afterAll: '',
          beforeEach: '',
          afterEach: '',
          testCases: [
            {
              name: caseForm.caseName,
              script: scriptContent
            }
          ]
        }

        // 保存测试套件
        const saveResponse = await savePlaywrightScript(testSuite)
        if (saveResponse.data.code !== 0) {
          this.$message({ type: 'error', message: `保存测试套件失败: ${saveResponse.data.msg || '未知错误'}` })
          return
        }

        // 获取保存后的测试套件路径
        const suitePath = saveResponse.data.data.suitePath

        // 启动UI模式
        const uiResponse = await startPlaywrightUI({ testPath: suitePath })
        if (uiResponse.data.code === 0) {
          // 设置调试状态
          this.isDebugging = true
          this.debugPid = uiResponse.data.data.pid
          this.debugSuitePath = suitePath

          // 存储调试信息
          this.debuggingCases.set(caseForm.id, {
            pid: uiResponse.data.data.pid,
            suitePath: suitePath,
            caseName: caseForm.caseName
          })

          this.$message({
            type: 'success',
            message: `调试模式已启动: ${caseForm.caseName}，请查看控制台获取访问地址`
          })

          // 启动状态检查定时器
          this.startUIStatusCheck()
        } else {
          this.$message({
            type: 'error',
            message: `启动调试模式失败: ${uiResponse.data.msg || '未知错误'}`
          })
        }
      } catch (error) {
        console.error('启动调试失败:', error)
        this.$message({ type: 'error', message: `启动调试失败: ${error.message}` })
      } finally {
        this.debugLoading = false
      }
    },

    // 停止调试
    async stopDebugTestCase(caseForm) {
      try {
        this.debugLoading = true

        // 停止UI模式
        const response = await stopPlaywrightUI()
        if (response.data.code === 0) {
          // 清除调试状态
          this.isDebugging = false
          this.debugPid = null
          this.debugSuitePath = null

          // 从调试信息中移除
          this.debuggingCases.delete(caseForm.id)

          this.$message({
            type: 'success',
            message: `调试模式已停止: ${caseForm.caseName}`
          })

          // 如果没有其他调试中的用例，停止状态检查
          if (this.debuggingCases.size === 0) {
            this.stopUIStatusCheck()
          }
        } else {
          this.$message({
            type: 'error',
            message: `停止调试模式失败: ${response.data.msg || '未知错误'}`
          })
        }
      } catch (error) {
        console.error('停止调试失败:', error)
        this.$message({ type: 'error', message: `停止调试失败: ${error.message}` })
      } finally {
        this.debugLoading = false
      }
    },

    // 启动UI状态检查定时器
    startUIStatusCheck() {
      if (this.uiStatusCheckTimer) {
        clearInterval(this.uiStatusCheckTimer)
      }

      this.uiStatusCheckTimer = setInterval(async () => {
        try {
          const response = await getPlaywrightUIStatus()
          if (response.data.code === 0) {
            const status = response.data.data.status

            // 如果UI已停止，但还有用例显示为调试中，则更新状态
            if (status === 'stopped' && this.debuggingCases.size > 0) {
              // 清除所有调试状态
              this.caseList.forEach(item => {
                if (this.isDebugging) {
                  this.isDebugging = false
                  this.debugPid = null
                  this.debugSuitePath = null
                }
              })

              this.debuggingCases.clear()
              this.stopUIStatusCheck()

              this.$message({
                type: 'info',
                message: '调试模式已自动停止'
              })
            }
          }
        } catch (error) {
          console.error('检查UI状态失败:', error)
        }
      }, 3000) // 每3秒检查一次
    },

    // 停止UI状态检查定时器
    stopUIStatusCheck() {
      if (this.uiStatusCheckTimer) {
        clearInterval(this.uiStatusCheckTimer)
        this.uiStatusCheckTimer = null
      }
    },

    // 优化用例
    aiOptimizeTestCase(script) {
      if (script) {
        this.$emit('optimize-case', script)
      } else {
        this.$message.warning('用例脚本内容为空，无法优化')
      }
    },

    //数据处理
    contentHandle() {
      let caseScript = this.generatedTestCase || this.caseScript
      // 如果内容包含代码块标记，则只提取代码块内容
      if (this.generatedTestCase && (this.generatedTestCase.includes('```javascript') || this.generatedTestCase.includes('```js'))) {
        const codeBlockRegex = /```(?:javascript|js)([\s\S]*?)```/
        const match = this.generatedTestCase.match(codeBlockRegex)
        if (match && match[1]) {
          caseScript = match[1].trim()
        }
      }
      this.caseScript = caseScript
      let caseName = 'AI自动生成用例'
      const match = caseScript.match(/test\('([^']+)'/)
      if (match && match[1]) {
        caseName = match[1]
      }
      this.caseName = caseName
    },

    // 添加用例
    addTestCase() {
      if (this.caseScript.length === 0) {
        this.$message.warning('用例脚本内容为空，无法新建')
        return
      }
      this.$refs.addCase.dialogVisible = true
      this.$refs.addCase.isEdit = false
      this.$refs.addCase.dialogTitle = '添加用例'
      this.$refs.addCase.editId = ''
      this.$refs.addCase.caseForm = {
        caseName: '',
        caseDesc: '',
        tags: [],
        status: 2,
        type: 'testCase',
        groupId: null,
        caseScript: this.caseScript,
        creator: this.name,
        totalExecutions: 0,
        passCount: 0
      }
    },

    // 保存测试用例
    saveTestCase() {
      if (this.caseScript.length === 0) {
        this.$message.warning('用例脚本内容为空，无法保存')
        return
      }
      if (!this.caseForm.id) {
        this.$message.warning('请先添加用例再编辑')
        return
      }
      try {
        this.contentHandle()
        this.$refs.addCase.dialogVisible = true
        this.$refs.addCase.isEdit = true
        this.$refs.addCase.dialogTitle = '编辑用例'
        this.$refs.addCase.editId = this.caseForm.id
        this.caseForm.caseScript = this.caseScript
        console.log(this.caseForm)
        this.$refs.addCase.caseForm = JSON.parse(JSON.stringify(this.caseForm))
      } catch (error) {
        console.error('保存用例失败:', error)
        this.$message.error('保存用例失败: ' + error.message)
      }
    },

    // 执行测试用例（测试套件模式）
    async executeTestCase() {
      try {
        this.contentHandle()

        // 脚本内容
        const scriptContent = this.caseScript || ''
        const caseName = this.caseName || '用例执行' + Date.now()

        if (!scriptContent) {
          this.$message({ type: 'warning', message: '用例脚本内容为空，无法执行测试' })
          return
        }

        this.$message({ type: 'info', message: `正在准备执行测试用例: ${caseName}` })

        // 检查服务是否运行
        const response = await getPlaywrightServiceStatus()
        if (response.data.code !== 0 || response.data.data.status !== 'running') {
          this.$message({ type: 'error', message: '自动化测试服务未运行，请在配置页面启动服务' })
          return
        }

        // 构建测试套件对象（将单个用例包装成测试套件）
        const testSuite = {
          suiteName: caseName,
          beforeAll: '', // 可以根据需要添加全局前置
          afterAll: '', // 可以根据需要添加全局后置
          beforeEach: '', // 可以根据需要添加每个测试前置
          afterEach: '', // 可以根据需要添加每个测试后置
          testCases: [
            {
              name: caseName,
              script: scriptContent
            }
          ]
        }
        console.log('构建的测试套件:', testSuite)

        // 保存测试套件
        try {
          this.$message({ type: 'info', message: `正在保存测试套件: ${caseName}` })
          const saveResponse = await savePlaywrightScript(testSuite)

          if (saveResponse.data.code !== 0) {
            this.$message({ type: 'error', message: `保存测试套件失败: ${saveResponse.data.msg || '未知错误'}` })
            return
          }

          // 获取保存后的测试套件路径
          const suitePath = saveResponse.data.data.suitePath
          console.log('测试套件已保存到:', suitePath)

          this.$message({ type: 'info', message: `正在执行测试套件: ${caseName}，请稍候...` })

          // 执行测试套件
          const testResponse = await runPlaywrightTest(suitePath, {
            headed: true,
            debug: false,
            timeout: 30000,
            workers: 1
          })

          if (testResponse.data.code === 0 && testResponse.data.data) {
            const result = testResponse.data.data

            // 构建详细的测试结果信息
            let resultMessage = `
                <div style="text-align: left;">
                  <h4>测试套件执行结果</h4>
                  <p><strong>套件名称:</strong> ${this.caseName}</p>
                  <p><strong>执行状态:</strong> <span style="color: ${result.summary.status === '通过' ? '#67C23A' : '#F56C6C'}">${result.summary.status}</span></p>
                  <p><strong>通过测试:</strong> ${result.summary.passed}</p>
                  <p><strong>失败测试:</strong> ${result.summary.failed}</p>
                  <p><strong>跳过测试:</strong> ${result.summary.skipped || 0}</p>
                  <p><strong>总执行时间:</strong> ${Math.floor(result.summary.duration / 1000)}秒</p>
              `

            // 如果有套件级别的结果，显示详细信息
            if (result.suiteResults && result.suiteResults.length > 0) {
              resultMessage += `<h5>测试用例详情:</h5>`
              result.suiteResults.forEach(suite => {
                resultMessage += `
                    <div style="margin-left: 10px; margin-bottom: 10px;">
                      <p><strong>用例:</strong> ${suite.suiteName}</p>
                      <p><strong>状态:</strong> <span style="color: ${suite.status === '通过' ? '#67C23A' : '#F56C6C'}">${suite.status}</span></p>
                      <p><strong>执行时间:</strong> ${Math.floor(suite.duration / 1000)}秒</p>
                    </div>
                  `
              })
            }

            resultMessage += `</div>`
            // 显示测试结果
            this.$alert(resultMessage, '测试套件执行结果', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '查看详细报告',
              cancelButtonText: '关闭',
              showCancelButton: true,
              callback: action => {
                if (action === 'confirm') {
                  // 打开测试报告
                  window.open(`http://localhost:3111/reports`, '_blank')
                }
              }
            })

            // 根据执行结果显示不同的消息
            if (result.summary.status === '通过') {
              // this.$message({
              //   type: 'success',
              //   message: `测试套件 "${this.caseName}" 执行成功！所有测试用例均通过。`,
              //   duration: 3000
              // })
            } else {
              // this.$message({
              //   type: 'warning',
              //   message: `测试套件 "${this.caseName}" 执行完成，但有 ${result.summary.failed} 个测试用例失败。`,
              //   duration: 3000
              // })
            }
          } else {
            console.error('测试执行失败:', testResponse)
            const errorMsg = testResponse.data.msg || '未知错误'
            // this.$message({ type: 'error', message: `测试套件执行失败: ${errorMsg}` })
          }
        } catch (error) {
          console.error('操作失败:', error)
          this.$message({ type: 'error', message: `操作失败: ${error.message}` })
        }
      } catch (error) {
        console.error('测试执行失败:', error)
        this.$message({ type: 'error', message: `测试执行失败: ${error.message}` })
      } finally {
        this.isGenerating = false
      }
    },

    // 处理插入代码
    handleInsertCode(code) {
      if (code) {
        this.addImportToScriptHeader()
        // 在当前脚本内容后面追加新代码
        const currentScript = this.caseScript || ''
        const separator = currentScript && !currentScript.endsWith('\n') ? '\n\n' : ''
        this.caseScript = currentScript + separator + code

        // 如果代码编辑器存在，刷新它
        this.$nextTick(() => {
          if (this.$refs.codeEditor) {
            this.$refs.codeEditor.refresh()
          }
        })
      }
    },

    // 在脚本头部添加固定代码
    addImportToScriptHeader() {
      const importCode = "import { enhance } from '../../tools/ElementEnhancer';"
      const currentScript = this.caseScript || ''

      // 检查是否已经包含了这个导入语句，避免重复添加
      if (!currentScript.includes(importCode)) {
        // 在脚本开头添加导入语句
        this.caseScript = importCode + (currentScript.startsWith('\n') ? '' : '\n\n') + currentScript

        // 如果代码编辑器存在，刷新它
        this.$nextTick(() => {
          if (this.$refs.codeEditor) {
            this.$refs.codeEditor.refresh()
          }
        })
      }
    },

    // 代码编辑器内容变化处理
    onCmCodeChange(value) {
      this.caseScript = value
    },

    // 处理自动保存开关
    handleAutoSaveToggle() {
      if (this.autoSaveEnabled) {
        // 开启自动保存时，立即执行一次自动保存
        this.performAutoSave()
        // 启动定时器，每30秒保存一次
        this.autoSaveTimer = setInterval(() => {
          this.performAutoSave()
        }, 30000)
      } else {
        // 关闭自动保存时，清除定时器
        if (this.autoSaveTimer) {
          clearInterval(this.autoSaveTimer)
          this.autoSaveTimer = null
        }
      }
    },

    // 执行自动保存
    async performAutoSave() {
      if (!this.autoSaveEnabled) return

      // 检查当前tab是否为录制调试tab，只有在录制调试tab下才执行自动保存
      const currentTab = this.$store.state.agent.autoTestActiveTag
      console.log('currentTab:', currentTab)
      if (currentTab !== 'record') {
        console.log('当前不在录制调试tab，跳过自动保存')
        return
      }

      if (this.caseScript.length === 0) {
        this.$message.warning('用例脚本内容为空，无法自动保存')
        this.autoSaveEnabled = false
        return
      }

      if (!this.caseForm.id) {
        this.$message.warning('请先添加用例后再开启自动保存')
        this.autoSaveEnabled = false
        return
      }

      try {
        this.autoSaveLoading = true

        // 调用与更新按钮相同的逻辑
        this.contentHandle()
        this.caseForm.caseScript = this.caseScript

        console.log('this.caseForm.status:', this.caseForm.status)
        // 构建提交数据
        const caseData = {
          id: this.caseForm.id,
          projectId: this.projectId,
          caseName: this.caseForm.caseName,
          caseDesc: this.caseForm.caseDesc,
          tags: this.caseForm.tags ? this.caseForm.tags.join(',') : '',
          status: this.caseForm.status,
          groupId: this.caseForm.groupId,
          caseScript: this.caseScript,
          creator: this.caseForm.creator,
          type: this.caseForm.type,
          totalExecutions: this.caseForm.totalExecutions,
          passCount: this.caseForm.passCount
        }

        //如果脚本中有用例数和断言数
        if (this.caseScript.includes('步骤数')) {
          let stepCountMatch = this.caseScript.match(/步骤数(\d+)/)
          let steps = stepCountMatch ? parseInt(stepCountMatch[1]) : 0
          console.log('步骤数:', steps)
          caseData.stepCount = steps
        } else {
          let steps = this.caseScript.split('\n').filter(line => line.trim().startsWith('await ')).length
          caseData.stepCount = steps
        }

        if (this.caseScript.includes('断言数')) {
          let assertionCountMatch = this.caseScript.match(/断言数(\d+)/)
          let assertions = assertionCountMatch ? parseInt(assertionCountMatch[1]) : 0
          console.log('断言数:', assertions)
          caseData.expectCount = assertions
        } else {
          let assertions = this.caseScript.split('\n').filter(line => line.trim().startsWith('expect')).length
          caseData.expectCount = assertions
        }

        // 调用更新API
        const res = await updateAutoCase(caseData)
        if (res.code === 0) {
          // 更新用例表单数据
          let updatedCaseForm = res.data
          if (updatedCaseForm.tags && typeof updatedCaseForm.tags === 'string') {
            updatedCaseForm.tags = updatedCaseForm.tags.split(',').filter(tag => tag.trim())
          }
          this.caseForm = updatedCaseForm
          // 静默提示，不打扰用户
          console.log('用例自动保存成功:', this.caseForm.caseName)
        } else {
          this.$message({
            type: 'error',
            message: '自动保存失败: ' + (res.msg || ''),
            duration: 2000
          })
          this.autoSaveEnabled = false
        }
      } catch (error) {
        console.error('自动保存出错:', error)
        this.$message({
          type: 'error',
          message: '自动保存出错: ' + error.message,
          duration: 2000
        })
        this.autoSaveEnabled = false
      } finally {
        this.autoSaveLoading = false
      }
    }
  },

  filters: {
    format(time) {
      var date = new Date(time)
      return formatDate(date, 'hh:mm:ss')
    }
  },

  created() {
    this.name = this.$store.state.user.name
    this.projectId = this.$store.state.agent.selectedProjectId
  },

  mounted() {
    this.getAutoCaseList()
    // 确保编辑器正确初始化
    this.$nextTick(() => {
      setTimeout(() => {
        if (this.$refs.codeEditor && this.$refs.codeEditor.codemirror) {
          this.$refs.codeEditor.codemirror.refresh()
        }
      }, 100)
    })
  },

  beforeDestroy() {
    // 清理定时器
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
    }
    if (this.uiStatusCheckTimer) {
      clearInterval(this.uiStatusCheckTimer)
    }
  }
}
</script>

<style scoped>
.case-generation.record-case {
  height: 100vh;
  overflow: hidden;
  padding-right: 15px;
}

.record-case-left ::v-deep .el-input__suffix {
  height: 140%;
}

.case-generation {
  height: 100%;
}

.panel-header {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--text-color);
}

.generation-content {
  display: flex;
  gap: 15px;
  flex: 1;
  height: calc(100vh - 212px);
  min-height: 0;
}

.left-panel {
  flex: 0 0 40%;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100vh;
  overflow: hidden;
}

.right-panel {
  flex: 0 0 60%;
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 10px;
  color: var(--text-color);
}

.input-section,
.result-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.input-section:first-child {
  flex: 0 0 auto;
  margin-bottom: 15px;
}

.input-section:last-child {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.result-section {
  height: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.file-info {
  font-size: 12px;
  color: #666;
}

.textarea-wrapper {
  margin-bottom: 15px;
}

.url-input {
  margin-bottom: 15px;
}

.url-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 5px;
}

.action-buttons {
  margin-top: 5px;
  display: flex;
  justify-content: flex-end;
  -webkit-box-pack: end;
  gap: 3px;
}

.generate-btn {
  background: var(--primary-gradient);
  border: none;
}

.code-container {
  border-radius: 6px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 12px 8px 12px;
  flex-shrink: 0;
}

.case-info {
  font-size: 13px;
  color: var(--text-color);
  flex: 1;
}

.code-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.auto-save-wrapper .el-switch {
  margin-bottom: 3px;
}

.auto-save-wrapper ::v-deep .el-switch.is-checked .el-switch__core {
  border-color: var(--primary-color-V1);
  background-color: var(--primary-color-V1);
}

.auto-save-wrapper ::v-deep .el-switch__label.is-active {
  color: var(--primary-color-V1);
}

.auto-save-wrapper ::v-deep .el-switch__label {
  font-size: 12px !important;
  color: #606266;
}

.save-time {
  font-size: 11px;
  color: #666;
  white-space: nowrap;
  line-height: 1.2;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafbfc;
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.toolbar-btn {
  cursor: pointer;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  transition: all 0.3s;
  user-select: none;
}

.toolbar-btn:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.toolbar-btn i {
  font-size: 14px;
}

.code-content {
  padding: 0;
  overflow: hidden;
  flex: 1;
  min-height: 0;
  border-radius: 0 0 6px 6px;
}

.code-content pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: 'Courier New', Courier, monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
}

/* 其他样式 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
  background-color: #f5f7fa;
  border-radius: 6px;
  text-align: center;
  padding: 40px;
}

.case-generation pre {
  background-color: #212529;
  color: #f8f9fa;
  padding: 10px;
  border-radius: 5px;
  white-space: pre-wrap;
  word-break: break-all;
  height: 100%;
}

.case-generation ::v-deep .el-button + .el-button {
  margin-left: 0;
}

.case-generation ::v-deep .el-form-item {
  margin-bottom: 15px;
}

.template-wrapper {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.record-case ::v-deep .el-select {
  display: contents;
}

.record-case ::v-deep .el-form-item__label {
  line-height: 30px;
}
</style>
