<template>
  <div class="ai-assistant-container">
    <div class="chat-main">
      <!-- 聊天消息区域 -->
      <div class="chat-messages" ref="chatMessages">
        <!-- 欢迎消息 -->
        <div class="message-agent" v-if="messages.length === 0">
          <div class="message-content message-content-welcome" style="padding: 15px">
            <div class="welcome-content">
              <p>我是您的专业Playwright助手，可以帮助您解决以下问题：</p>
              <ul>
                <li>Playwright API使用方法</li>
                <li>调试和问题排查</li>
                <li>选择器和元素定位</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 对话消息 -->
        <template v-for="(message, index) in messages">
          <!-- 用户消息 -->
          <div class="message-user" v-if="message.type === 'user'" :key="'user-' + index">
            <div class="message-content message-content-user">
              <pre class="user-query">{{ message.content }}</pre>
            </div>
          </div>

          <!-- AI回复 -->
          <div class="message-agent" v-if="message.type === 'agent'" :key="'agent-' + index">
            <div class="agent-message-wrapper">
              <div class="message-content message-content-agent" :class="{ streaming: message.streaming }">
                <div v-if="message.streaming && message.content === '正在思考中...'" class="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                  <div class="typing-text">{{ message.content }}</div>
                </div>
                <div v-else-if="message.streaming" class="streaming-content">
                  <MarkdownRendererEdit :content="message.content" :messageType="'agent'" :darkMode="true" />
                </div>
                <MarkdownRendererEdit v-else :content="message.content" :messageType="'agent'" :darkMode="true" />
              </div>
              <!-- 停止按钮放在消息卡片的下方 -->
              <div v-if="message.streaming" class="stop-btn-container">
                <button class="stop-btn-bottom" @click="stopAIResponse(message)" title="停止响应">
                  <i class="ri-stop-circle-line"></i>
                  <span>停止响应</span>
                </button>
              </div>
            </div>
          </div>
        </template>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input-container" :class="{ isStreaming: isStreaming }">
        <div class="chat-input-wrapper">
          <textarea class="chat-input" placeholder="请输入您的Playwright问题..." rows="2" v-model="inputMessage" @keyup.ctrl.enter="sendMessage" @keydown.enter.exact.prevent="handleEnterPress" @compositionstart="onCompositionStart" @compositionend="onCompositionEnd" :disabled="isStreaming" ref="chatInput"></textarea>
          <button type="button" class="btn-send" :disabled="isStreaming || !inputMessage.trim()" @click="sendMessage" :title="isStreaming ? '正在发送...' : '发送消息 (Ctrl + Enter)'">
            <i class="ri-send-plane-fill" v-if="!isStreaming"></i>
            <i class="ri-loader-line loading-icon" v-else></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MarkdownRendererEdit from '@/views/zpAgent/components/MarkdownRendererV1.vue'

export default {
  name: 'AiAssistant',
  data() {
    return {
      apiKey: 'app-HmMOxYenPITvXvyNndoIbBAz',
      inputMessage: '',
      messages: [],
      isStreaming: false,
      isComposing: false, // 跟踪输入法状态
      name: '',
      currentConversationId: '',
      currentAgentId: '',
      prompt: `# 角色
自动化测试脚本开发专家，专注于使用Playwright框架进行Web应用程序的自动化测试，具备丰富的测试脚本编写经验，能够高效地生成可靠、可维护的测试脚本。

# 目标
1. 使用Playwright框架生成自动化测试脚本，覆盖用户指定的Web应用程序功能。
2. 确保测试脚本的可读性、可维护性和高效性。

# 技能
1. 精通Playwright框架的API和功能，能够熟练使用其进行自动化测试。
2. 具备良好的编程能力，能够编写结构清晰、逻辑严密的代码。
3. 熟悉常见的测试模式和方法，能够根据需求选择合适的测试策略。

# 工作流程
1. 与用户沟通，明确需要测试的Web应用程序功能及其测试需求。
2. 根据需求设计测试用例，包括测试场景、预期结果等。
3. 使用Playwright编写自动化测试脚本，覆盖所有测试用例。
4. 对脚本进行调试和优化，确保其在不同环境下均能稳定运行。
5. 提供测试脚本的使用说明和必要的文档支持。

# 约束
1. 测试脚本必须覆盖所有指定的功能，确保测试的全面性。
2. 脚本代码必须遵循良好的编程规范，确保其可读性和可维护性。
3. 禁止使用可能导致测试不稳定的代码或方法。

# 输出格式
以清晰、有条理的代码形式呈现自动化测试脚本，可根据需要添加注释和说明，确保用户能够轻松理解和使用。`
    }
  },

  components: {
    MarkdownRendererEdit
  },

  methods: {
    // 发送消息
    async sendMessage() {
      if (!this.inputMessage.trim() || this.isStreaming) {
        return
      }

      if (this.inputMessage.length > 10000) {
        this.$message.warning('消息长度不能超过10000字符')
        return
      }

      const userMessage = this.inputMessage.trim()
      this.inputMessage = ''

      // 添加用户消息
      this.messages.push({
        type: 'user',
        content: userMessage,
        timestamp: new Date()
      })

      // 添加AI回复占位符
      const aiMessage = {
        type: 'agent',
        content: '正在思考中...',
        streaming: true,
        timestamp: new Date()
      }
      this.messages.push(aiMessage)

      this.isStreaming = true
      this.scrollToBottom()

      try {
        await this.getAIResponse(aiMessage, userMessage)
      } catch (error) {
        console.error('发送消息失败:', error)
        aiMessage.content = '抱歉，发生了错误，请稍后重试。'
        aiMessage.streaming = false
      } finally {
        this.isStreaming = false
      }
    },

    // 获取AI回复 - 真正的流式API调用
    async getAIResponse(aiMessage, userQuery) {
      // 设置请求超时
      let timeoutId = setTimeout(() => {
        if (this.isStreaming && aiMessage.content === '正在思考中...') {
          aiMessage.content = '响应超时，请稍后重试。'
          aiMessage.streaming = false
          this.isStreaming = false
        }
      }, 30000) // 30秒超时

      try {
        // 发送消息到服务器
        const response = await fetch('https://muses.weizhipin.com/v1/chat-messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + this.apiKey
          },
          body: JSON.stringify({
            inputs: {
              prompt: this.prompt
            },
            query: userQuery,
            response_mode: 'streaming',
            user: this.name,
            conversation_id: this.currentConversationId || '',
            agent_id: this.currentAgentId
          })
        })

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`HTTP error! Status: ${response.status}, Details: ${errorText}`)
        }

        // 清除超时计时器
        clearTimeout(timeoutId)

        // 处理流式响应
        const contentType = response.headers.get('Content-Type')
        if (contentType && contentType.includes('text/event-stream')) {
          const reader = response.body.getReader()
          const decoder = new TextDecoder()
          let buffer = ''
          let isFirstChunk = true

          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            buffer += decoder.decode(value, { stream: true })
            const lines = buffer.split('\n')
            buffer = lines.pop() || ''

            for (const line of lines) {
              if (line.trim() === '') continue

              try {
                let data
                let dataString = ''

                if (line.startsWith('data:')) {
                  dataString = line.slice(5).trim()
                  if (dataString === '[DONE]') continue
                } else if (line.startsWith('{') || line.startsWith('[')) {
                  dataString = line
                } else {
                  continue
                }

                // 解析JSON数据
                try {
                  data = JSON.parse(dataString)
                  this.currentConversationId = data.conversation_id || this.currentConversationId
                  aiMessage.task_id = data.task_id
                  aiMessage.conversation_id = data.conversation_id
                  aiMessage.message_id = data.message_id
                } catch (e) {
                  console.warn('无法解析JSON数据:', dataString)
                  continue
                }

                // 从响应中提取内容
                let content = ''
                if (data.data && typeof data.data === 'string') {
                  content = data.data
                } else if (data.choices && data.choices.length > 0) {
                  content = data.choices[0].delta?.content || ''
                } else if (data.answer) {
                  content = data.answer
                } else if (data.text) {
                  content = data.text
                } else if (data.content) {
                  content = data.content
                } else if (data.message) {
                  content = data.message
                }

                if (content) {
                  // 收到第一个有内容的响应时，替换掉"正在思考中..."
                  if (isFirstChunk && aiMessage.content === '正在思考中...') {
                    aiMessage.content = content
                    isFirstChunk = false
                  } else {
                    aiMessage.content += content
                  }
                  this.scrollToBottom()
                }
              } catch (e) {
                console.error('处理流数据行时出错:', e)
              }
            }
          }

          // 流结束后
          aiMessage.streaming = false
        } else {
          // 非流式响应处理
          const data = await response.json()

          if (data.code === 0 || data.answer) {
            let responseText = ''
            if (data.data && data.data.answer) {
              responseText = data.data.answer
            } else if (data.answer) {
              responseText = data.answer
            }

            if (aiMessage.content === '正在思考中...') {
              aiMessage.content = responseText
            } else {
              aiMessage.content += responseText
            }
            aiMessage.streaming = false
          } else {
            aiMessage.content = '消息发送失败，请重试。'
            aiMessage.streaming = false
          }
        }
      } catch (error) {
        clearTimeout(timeoutId)
        console.error('API调用出错:', error)
        aiMessage.content = '发生错误，请重试。错误信息：' + error.message
        aiMessage.streaming = false
        throw error
      }
    },

    // 处理回车键
    handleEnterPress(event) {
      // 如果输入法正在输入中，不触发发送
      if (this.isComposing) {
        return
      }

      if (!event.shiftKey) {
        this.sendMessage()
      }
    },

    // 输入法开始输入
    onCompositionStart() {
      this.isComposing = true
    },

    // 输入法结束输入
    onCompositionEnd() {
      this.isComposing = false
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const chatMessages = this.$refs.chatMessages
        if (chatMessages) {
          chatMessages.scrollTop = chatMessages.scrollHeight
        }
      })
    },

    // 延时函数
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    // 停止AI回复
    async stopAIResponse(message) {
      try {
        if (message.task_id) {
          await fetch('https://muses.weizhipin.com/v1/chat-messages/' + message.task_id + '/stop', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: 'Bearer ' + this.apiKey
            },
            body: JSON.stringify({
              conversation_id: this.currentConversationId,
              user: this.name
            })
          })
        }

        // 更新消息状态
        message.streaming = false
        this.isStreaming = false

        // 如果消息内容是"正在思考中..."，更新为停止状态
        if (message.content === '正在思考中...') {
          message.content = '已停止响应'
        }
      } catch (error) {
        console.error('停止AI回复失败:', error)
        message.streaming = false
        this.isStreaming = false
      }
    }
  },

  mounted() {
    // 初始化用户名
    this.name = this.$store.state.user?.name

    // 自动聚焦输入框
    this.$nextTick(() => {
      if (this.$refs.chatInput) {
        this.$refs.chatInput.focus()
      }
    })
  }
}
</script>

<style scoped>
@import '../../../../assets/css/style.css';

/* 主容器 - 确保100%高度 */
.ai-assistant-container {
  height: 100%;
  min-height: 43vh;
  max-height: 100vh;
  display: flex;
  background-color: #f5f5f5;
  border-radius: var(--radius, 8px);
  overflow: hidden;
  box-shadow: var(--shadow-card, 0 4px 12px rgba(0, 0, 0, 0.1));
  position: relative;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 0;
  position: relative;
}

/* 消息区域 - 固定高度，预留输入框空间 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background-color: #f5f5f5;
  padding-bottom: 80px; /* 为固定输入框预留空间，减少间距 */
}

/* 消息基础样式 */
.message-user,
.message-agent {
  display: flex;
  max-width: 100%;
  animation: messageSlideIn 0.3s ease-out;
}

/* AI消息包装器 - 垂直布局 */
.agent-message-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 用户消息 - 关键修复：使用justify-content而不是嵌套容器 */
.message-user {
  justify-content: flex-end;
}

.message-content {
  background-color: #ffffff;
  border-radius: var(--radius, 8px);
  box-shadow: var(--shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.1));
  border: 1px solid #e0e0e0;
  position: relative;
  word-wrap: break-word;
  word-break: break-word;
}

/* 用户消息内容样式 */
.message-content-user {
  background: var(--primary-gradient, linear-gradient(135deg, #667eea 0%, #764ba2 100%));
  color: white;
  border: none;
  max-width: 85%;
  margin-left: auto;
}

.user-query {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.6;
  background-color: transparent;
  border: 0;
  color: white;
  white-space: pre-wrap;
  font-family: inherit;
  padding: 3px;
}

/* AI消息内容样式 */
.message-content-agent {
  background-color: #ffffff;
  max-width: 95%;
  padding: 0.8rem 1rem;
}

.message-content-agent.streaming {
  position: relative;
}

/* 欢迎内容 */
.welcome-content p {
  margin-bottom: 0.75rem;
  color: var(--text-color, #333);
  line-height: 1.6;
}

.welcome-content ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.welcome-content li {
  margin-bottom: 0.5rem;
  color: var(--text-color, #333);
  line-height: 1.5;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
  padding: 0.5rem 0;
  position: relative;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary-color, #667eea);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-text {
  margin-left: 8px;
  color: var(--text-light, #999);
  font-size: 0.9rem;
}

/* 流式内容容器 */
.streaming-content {
  position: relative;
}

/* 停止按钮容器 - 位于消息卡片下方 */
.stop-btn-container {
  display: flex;
  justify-content: flex-start;
  margin-top: 8px;
  width: 100%;
}

.stop-btn-bottom {
  background: #e3f2fd;
  border: 1px solid #90caf9;
  border-radius: 16px;
  padding: 4px 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #1976d2;
  font-size: 12px;
  box-shadow: none;
  height: 28px;
}

.stop-btn-bottom:hover {
  background: #bbdefb;
  color: #1565c0;
  transform: none;
  box-shadow: none;
}

.stop-btn-bottom:active {
  transform: translateY(0);
}

.stop-btn-bottom i {
  font-size: 12px;
}

.stop-btn-bottom span {
  font-size: 12px;
  font-weight: 400;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 - 固定在底部 */
.chat-input-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.5rem;
  background-color: #f5f5f5;
  border-top: 1px solid #e0e0e0;
  z-index: 100;
}

.chat-input-container.isStreaming {
  opacity: 0.7;
  pointer-events: none;
}

.chat-input-wrapper {
  position: relative;
  background-color: #ffffff;
  border: 2px solid #e0e0e0;
  border-radius: var(--radius, 8px);
  transition: var(--transition, all 0.3s ease);
}

.chat-input-wrapper:focus-within {
  border-color: var(--primary-color, #667eea);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 调整输入框高度 */
.chat-input {
  width: 100%;
  border: none;
  outline: none;
  padding: 0.6rem 50px 0.6rem 0.8rem;
  font-size: 0.9rem;
  line-height: 1.4;
  background: transparent;
  color: var(--text-color, #333);
  resize: none;
  font-family: inherit;
  min-height: 36px;
  max-height: 100px;
}

.chat-input::placeholder {
  color: var(--text-light, #999);
}

.btn-send {
  position: absolute;
  right: 6px;
  bottom: 6px;
  width: 36px;
  height: 36px;
  border: none;
  background: var(--primary-gradient, linear-gradient(135deg, #667eea 0%, #764ba2 100%));
  color: white;
  border-radius: var(--radius-sm, 6px);
  cursor: pointer;
  transition: var(--transition, all 0.3s ease);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.btn-send:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-send:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--text-light, #999);
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-messages {
    padding: 0.8rem;
    gap: 1rem;
    padding-bottom: 110px;
  }

  .chat-input-container {
    padding: 0.8rem;
  }

  .message-content-user {
    max-width: 90%;
  }

  .message-content-agent {
    max-width: 95%;
  }
}

@media (max-width: 480px) {
  .message-content-user {
    max-width: 95%;
  }

  .chat-messages {
    padding-bottom: 100px;
  }
}
</style>
