<template>
  <div class="element-matching-section">
    <!-- URL和Cookie输入区域 -->
    <div class="url-input-section">
      <div class="input-group">
        <el-input v-model="targetUrl" placeholder="输入要分析的页面URL，例如：https://www.zhipin.com" class="url-input" @keyup.enter="validateLocators">
          <template slot="prepend">
            <i class="el-icon-link"></i>
          </template>
        </el-input>
        <button type="primary" class="btn btn-primary" style="height: 32px; margin-top: 2px" size="small" @click="validateLocators" :disabled="isValidating || !targetUrl.trim()">
          <i class="ri-loader-4-line" v-if="isValidating" style="animation: spin 1s linear infinite"></i>
          <i class="ri-search-line" v-else></i>
          {{ isValidating ? '验证中...' : '验证定位器' }}
        </button>
      </div>
      <div class="cookie-input-group">
        <el-input v-model="cookieValue" placeholder="输入Cookie值,示例：wt2=xx（可选）" class="cookie-input" type="textarea" :rows="2">
          <template slot="prepend">
            <i class="el-icon-key"></i>
          </template>
        </el-input>
        <div class="cookie-hint">
          <span>提示：某些需要登录的页面可能需要Cookie值才能正常分析</span>
        </div>
      </div>
    </div>

    <!-- 定位器输入区域 -->
    <div class="locators-input-section">
      <div class="section-header">
        <h4>Playwright定位器列表</h4>
        <button class="btn btn-outline" size="small" @click="addLocator">添加定位器</button>
      </div>
      <div class="locators-list">
        <div v-for="(locator, index) in locators" :key="index" class="locator-item">
          <el-input v-model="locator.value" placeholder="输入Playwright定位器，例如：page.locator('button')" class="locator-input">
            <template slot="prepend">
              <span>{{ index + 1 }}</span>
            </template>
          </el-input>
          <button class="btn btn-danger" size="small" @click="removeLocator(index)" :disabled="locators.length <= 1">
            <i class="ri-delete-bin-line"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 验证结果 -->
    <div v-if="validationResults.length > 0" class="validation-results">
      <div class="results-header">
        <span style="font-size: 14px">验证结果 ({{ validationResults.length }} 个定位器)</span>
        <!-- <div class="filter-controls">
          <el-input v-model="searchText" placeholder="搜索定位器" size="small" style="width: 200px" @input="filterResults" clearable></el-input>
          <el-select v-model="statusFilter" placeholder="状态筛选" size="small" clearable @change="filterResults" style="width: 120px">
            <el-option label="全部" value="all"></el-option>
            <el-option label="有效" value="valid"></el-option>
            <el-option label="无效" value="invalid"></el-option>
          </el-select>
        </div> -->
      </div>

      <div class="results-list">
        <div v-for="(result, index) in filteredResults" :key="index" class="result-item" :class="{ valid: result.valid, invalid: !result.valid }">
          <!-- 定位器信息 -->
          <div class="result-info">
            <div class="result-status">
              <el-tag :type="result.valid ? 'success' : 'danger'" size="mini">
                {{ result.valid ? '有效' : '无效' }}
              </el-tag>
              <span class="element-count">{{ result.count }} 个元素</span>
            </div>
            <div class="result-locator">
              <span class="locator-label">定位器:</span>
              <code class="locator-code" v-html="highlightSearchText(result.locator)"></code>
            </div>
            <div v-if="result.selector" class="result-selector">
              <span class="selector-label">选择器:</span>
              <code class="selector-code">{{ result.selector }}</code>
            </div>
          </div>

          <!-- 匹配的元素列表 -->
          <div v-if="result.elements && result.elements.length > 0" class="matched-elements">
            <div class="elements-header" @click="toggleElementsVisibility(index)">
              <span>匹配的元素 ({{ result.elements.length }})</span>
              <i :class="result.showElements ? 'ri-arrow-up-s-line' : 'ri-arrow-down-s-line'"></i>
            </div>
            <div v-show="result.showElements" class="elements-list">
              <div v-for="(element, elemIndex) in result.elements" :key="elemIndex" class="element-item">
                <div class="element-info">
                  <div class="element-tag">
                    <el-tag size="mini" type="info">{{ element.tagName || 'unknown' }}</el-tag>
                  </div>
                  <div class="element-details">
                    <div v-if="element.text" class="element-text">{{ element.text }}</div>
                    <div v-if="element.attributes" class="element-attributes">
                      <span v-for="(value, key) in element.attributes" :key="key" class="attribute-item">{{ key }}="{{ value }}"</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { validatePlaywrightLocators } from '@/api/agent/domAnalysis'

export default {
  name: 'ElementMatching',
  data() {
    return {
      targetUrl: '',
      cookieValue: '',
      isValidating: false,
      locators: [{ value: '' }],
      validationResults: [],
      filteredResults: [],
      searchText: '',
      statusFilter: 'all'
    }
  },
  methods: {
    // 添加定位器
    addLocator() {
      this.locators.push({ value: '' })
    },

    // 删除定位器
    removeLocator(index) {
      if (this.locators.length > 1) {
        this.locators.splice(index, 1)
      }
    },

    // 验证定位器
    async validateLocators() {
      if (!this.targetUrl.trim()) {
        this.$message.warning('请输入要分析的页面URL')
        return
      }

      // 过滤出非空的定位器
      const validLocators = this.locators.map(loc => loc.value.trim()).filter(loc => loc.length > 0)

      if (validLocators.length === 0) {
        this.$message.warning('请至少输入一个定位器')
        return
      }

      // 保存URL和Cookie到store
      this.$store.dispatch('agent/setAutoDomAnalysisUrl', this.targetUrl)
      this.$store.dispatch('agent/setAutoDomAnalysisCookie', this.cookieValue)

      this.isValidating = true
      try {
        // 调用validatePlaywrightLocators接口
        const response = await validatePlaywrightLocators(this.targetUrl, validLocators, this.cookieValue.trim() || undefined)

        if (response.data.code === 0) {
          this.validationResults = response.data.data.map(result => ({
            ...result,
            showElements: false // 添加展开/收起状态
          }))
          this.filteredResults = [...this.validationResults]

          const validCount = this.validationResults.filter(r => r.valid).length
          const invalidCount = this.validationResults.length - validCount

          this.$message.success(`验证完成：${validCount} 个有效，${invalidCount} 个无效`)
        } else {
          throw new Error(response.data.msg || '验证失败')
        }
      } catch (error) {
        console.error('定位器验证失败:', error)
        this.$message.error('定位器验证失败: ' + error.message)
      } finally {
        this.isValidating = false
      }
    },

    // 过滤结果
    filterResults() {
      let filtered = [...this.validationResults]

      // 按状态过滤
      if (this.statusFilter === 'valid') {
        filtered = filtered.filter(result => result.valid)
      } else if (this.statusFilter === 'invalid') {
        filtered = filtered.filter(result => !result.valid)
      }

      // 按搜索文本过滤
      if (this.searchText && this.searchText.trim()) {
        const searchTerm = this.searchText.trim().toLowerCase()
        filtered = filtered.filter(result => {
          return result.locator.toLowerCase().includes(searchTerm) || (result.selector && result.selector.toLowerCase().includes(searchTerm))
        })
      }

      this.filteredResults = filtered
    },

    // 切换元素列表显示/隐藏
    toggleElementsVisibility(index) {
      this.$set(this.filteredResults[index], 'showElements', !this.filteredResults[index].showElements)
    },

    // 高亮搜索文本
    highlightSearchText(text) {
      if (!this.searchText || !text) return text
      const searchTerm = this.searchText.trim()
      if (!searchTerm) return text

      const regex = new RegExp(`(${searchTerm})`, 'gi')
      return text.replace(regex, '<mark>$1</mark>')
    }
  },

  mounted() {
    // 从store获取之前保存的URL和Cookie
    this.targetUrl = this.$store.state.agent.autoDomAnalysisUrl || ''
    this.cookieValue = this.$store.state.agent.autoDomAnalysisCookie || ''

    // 如果没有定位器，添加一些示例
    if (this.locators.length === 1 && !this.locators[0].value) {
      this.locators = [{ value: "page.locator('button')" }]
    }
  }
}
</script>

<style scoped>
.element-matching-section {
  height: 100%;
  padding: 15px;
  background-color: #f8f9fb;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.url-input-section {
  margin-bottom: 20px;
}

.input-group {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.url-input {
  flex: 1;
}

.cookie-input-group {
  margin-top: 10px;
}

.cookie-input {
  width: 100%;
  margin-bottom: 8px;
}

.cookie-hint {
  font-size: 12px;
  color: #909399;
  padding-left: 10px;
}

.locators-input-section {
  margin-bottom: 20px;
  background-color: white;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #ebeef5;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.locators-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.locator-item {
  display: flex;
  gap: 10px;
  align-items: center;
}

.locator-input {
  flex: 1;
}

.validation-results {
  background-color: white;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #ebeef5;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.filter-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.results-list {
  max-height: 500px;
  overflow-y: auto;
}

.result-item {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  margin-bottom: 15px;
  padding: 15px;
  transition: all 0.3s;
}

.result-item.valid {
  background-color: #f0f9ff;
}

.result-item.invalid {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.result-info {
  margin-bottom: 10px;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.element-count {
  font-size: 12px;
  color: #909399;
}

.result-locator,
.result-selector {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 6px;
}

.locator-label,
.selector-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
  min-width: 50px;
  flex-shrink: 0;
}

.locator-code,
.selector-code {
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #476582;
  word-break: break-all;
}

.matched-elements {
  border-top: 1px solid #ebeef5;
  padding-top: 10px;
}

.elements-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 5px 0;
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.elements-header:hover {
  color: #409eff;
}

.elements-list {
  margin-top: 8px;
}

.element-item {
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
  margin-bottom: 6px;
  border: 1px solid #f0f0f0;
}

.element-info {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.element-tag {
  flex-shrink: 0;
}

.element-details {
  flex: 1;
}

.element-text {
  font-size: 13px;
  color: #303133;
  margin-bottom: 4px;
  font-weight: 500;
}

.element-attributes {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.attribute-item {
  font-size: 11px;
  color: #909399;
  background-color: #f4f4f5;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 搜索高亮样式 */
mark {
  background-color: #fff3cd;
  color: #856404;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
}

/* 加载动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input-group {
    flex-direction: column;
  }

  .locator-item {
    flex-direction: column;
    align-items: stretch;
  }

  .results-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .filter-controls {
    width: 100%;
    justify-content: space-between;
  }
}
</style>
