<template>
  <div class="dom-analysis-section">
    <!-- <div class="section-header">
      <h4>
        页面DOM分析
        <span class="optional-tag">(可选，使用之前清先更新下client服务)</span>
      </h4>
      <el-tooltip content="分析目标页面的DOM结构，帮助AI生成更准确的测试脚本" placement="top">
        <i class="el-icon-question"></i>
      </el-tooltip>
    </div> -->

    <div class="url-input-section">
      <div class="input-group">
        <el-input v-model="targetUrl" placeholder="输入要分析的页面URL，例如：https://www.zhipin.com" class="url-input" @keyup.enter="analyzePageDOM">
          <template slot="prepend">
            <i class="el-icon-link"></i>
          </template>
        </el-input>
        <button type="primary" class="btn btn-primary" style="height: 32px; margin-top: 2px" size="small" @click="analyzePageDOM" :disabled="isAnalyzing || !targetUrl.trim()">
          <i class="ri-loader-4-line" v-if="isAnalyzing" style="animation: spin 1s linear infinite"></i>
          <i class="ri-search-line" v-else></i>
          {{ isAnalyzing ? '分析中...' : '分析页面' }}
        </button>
        <button type="primary" class="btn btn-success" style="height: 32px; margin-top: 2px" size="small" @click="smartExtractElements" :disabled="isSmartExtracting || !targetUrl.trim()">
          <i class="ri-loader-4-line" v-if="isSmartExtracting" style="animation: spin 1s linear infinite"></i>
          <i class="ri-code-fill" v-else></i>
          {{ isSmartExtracting ? '提取中...' : '智能提取' }}
        </button>
      </div>
      <div class="cookie-input-group">
        <el-input v-model="cookieValue" placeholder="输入Cookie值,示例：wt2=xx（可选）" class="cookie-input" type="textarea" :rows="2">
          <template slot="prepend">
            <i class="el-icon-key"></i>
          </template>
        </el-input>
        <div class="cookie-hint">
          <span>提示：某些需要登录的页面可能需要Cookie值才能正常分析</span>
        </div>
      </div>
    </div>

    <!-- DOM分析结果 -->
    <div v-if="analyzedElements.length > 0" class="analysis-results">
      <div class="results-header">
        <span style="font-size: 14px">{{ analyzedElements.length }} 个可交互元素</span>
        <div class="filter-controls">
          <el-input v-model="searchText" placeholder="搜索元素(选择器/文本/属性/类型)" size="small" style="width: 200px" @input="filterElements" clearable></el-input>

          <el-select v-model="elementTypeFilter" placeholder="元素类型" size="small" clearable @change="filterElements" style="width: 100px">
            <el-option label="全部" value="all"></el-option>
            <el-option v-for="item in elementTypes" :key="item.value" :label="item.label + '(' + item.count + ')'" :value="item.value"></el-option>
          </el-select>
          <button class="btn btn-outline" size="small" style="height: 28px" @click="selectAllElements" :disabled="filteredElements.length === 0">全选</button>
          <button class="btn btn-outline" size="small" style="height: 28px" @click="clearSelection">清空</button>
        </div>
      </div>

      <div class="elements-list">
        <div v-for="element in filteredElements" :key="element.id" class="element-item" :class="{ selected: selectedElements.includes(element.id) }" @click="toggleElementSelection(element.id)">
          <!-- 左侧：基本信息 -->
          <div class="element-info">
            <div class="element-type">
              <el-tag :type="getElementTypeColor(element.type)" size="mini">
                {{ element.type }}
              </el-tag>
            </div>
            <div class="element-details">
              <div class="element-text" v-html="highlightSearchText(element.text || getSelectorText(element.selector))"></div>
              <div v-if="element.attributes && Object.keys(element.attributes).length > 0" class="element-attributes">
                <div class="attributes-tags">
                  <span class="attributes-label" v-if="element.iframeContext">iframe:{{ element.iframeIndex }} {{ element.iframeSrc }}</span>
                  <span class="attributes-label">属性:</span>
                  <el-tag v-for="(value, key) in element.attributes" :key="key" size="mini" type="info" class="attribute-tag" :title="`${key}: ${value}`">
                    <span class="attr-key">{{ key }}</span>
                    <span class="attr-separator">=</span>
                    <span class="attr-value">"{{ value }}"</span>
                  </el-tag>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：选择器详细信息 -->
          <div class="element-selector-details">
            <div v-if="element.selector && typeof element.selector === 'object'" class="selector-object">
              <div class="selector-property">
                <span class="property-key">定位:</span>
                <span class="property-value">"{{ element.selector.playwrightCode || '' }}"</span>
              </div>
              <div v-if="element.selector.isUnique !== undefined" class="selector-property">
                <span class="property-key">唯一:</span>
                <span class="property-value" style="width: 40px">{{ element.selector.isUnique }}</span>
                <span class="property-key">稳定:</span>
                <span class="property-value" style="width: 60px">{{ element.selector.stability }}</span>
              </div>
              <div v-if="element.selector.count !== undefined" class="selector-property">
                <span class="property-key">匹配:</span>
                <span class="property-value" style="width: 40px">{{ element.selector.count }}</span>
                <span class="property-key">优先:</span>
                <span class="property-value" style="width: 60px">{{ element.selector.priority }}</span>
              </div>
            </div>
            <div v-else class="selector-simple">
              <span class="property-key">selector:</span>
              <span class="property-value">{{ getSelectorText(element.selector) }}</span>
            </div>
          </div>

          <!-- 选择框 -->
          <div class="element-actions">
            <el-checkbox :value="selectedElements.includes(element.id)" @change="toggleElementSelection(element.id)"></el-checkbox>
          </div>
        </div>
      </div>

      <div class="analysis-actions">
        <button type="primary" class="btn btn-success" style="height: 28px" size="small" @click="generateDOMContext" :disabled="selectedElements.length === 0">
          <i class="ri-check-line"></i>
          生成DOM上下文 ({{ selectedElements.length }}个)
        </button>
        <button class="btn btn-outline" size="small" style="height: 28px" @click="clearAnalysis">
          <i class="ri-refresh-line"></i>
          重新分析
        </button>
      </div>
    </div>

    <!-- DOM上下文预览 -->
    <div v-if="domContext" class="dom-context-preview">
      <div class="context-header">
        <span>DOM上下文预览</span>
        <el-button size="mini" type="text" @click="copyDOMContext">
          <i class="ri-file-copy-2-line"></i>
          复制
        </el-button>
      </div>
      <div class="context-content">
        <pre>{{ domContext }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { analyzePageDOM, extractKeyElements, validatePlaywrightLocators } from '@/api/agent/domAnalysis'
// import lib from 'qs'

export default {
  name: 'DomAnalysis',
  data() {
    return {
      targetUrl: '',
      cookieValue: '',
      isAnalyzing: false,
      isSmartExtracting: false,
      analyzedElements: [],
      filteredElements: [],
      selectedElements: [],
      elementTypeFilter: 'all',
      searchText: '',
      domContext: '',
      elementTypes: [
        { label: 'div', value: 'div', count: 0 },
        { label: 'input', value: 'input', count: 0 },
        { label: 'button', value: 'button', count: 0 },
        { label: 'span', value: 'span', count: 0 },
        { label: 'li', value: 'li', count: 0 },
        { label: 'a', value: 'a', count: 0 },
        { label: 'p', value: 'p', count: 0 },
        { label: 'select', value: 'select', count: 0 },
        { label: 'textarea', value: 'textarea', count: 0 },
        { label: 'form', value: 'form', count: 0 },
        { label: 'img', value: 'img', count: 0 },
        { label: 'h1', value: 'h1', count: 0 },
        { label: 'h2', value: 'h2', count: 0 },
        { label: 'h3', value: 'h3', count: 0 },
        { label: 'h4', value: 'h4', count: 0 },
        { label: 'h5', value: 'h5', count: 0 },
        { label: 'h6', value: 'h6', count: 0 },
        { label: 'b', value: 'b', count: 0 },
        { label: 'q', value: 'q', count: 0 },
        { label: 'address', value: 'address', count: 0 },
        { label: 'section', value: 'section', count: 0 },
        { label: 'nav', value: 'nav', count: 0 },
        { label: 'svg', value: 'svg', count: 0 },
        { label: 'link', value: 'link', count: 0 },
        { label: 'title', value: 'title', count: 0 },
        { label: 'body', value: 'body', count: 0 },
        { label: 'path', value: 'path', count: 0 },
        { label: 'aside', value: 'aside', count: 0 },
        { label: 'footer', value: 'footer', count: 0 },
        { label: 'header', value: 'header', count: 0 },
        { label: 'main', value: 'main', count: 0 },
        { label: 'dialog', value: 'dialog', count: 0 },
        { label: 'details', value: 'details', count: 0 },
        { label: 'summary', value: 'summary', count: 0 },
        { label: 'figure', value: 'figure', count: 0 },
        { label: 'figcaption', value: 'figcaption', count: 0 },
        { label: 'picture', value: 'picture', count: 0 },
        { label: 'source', value: 'source', count: 0 },
        { label: 'pre', value: 'pre', count: 0 },
        { label: 'code', value: 'code', count: 0 },
        { label: 'blockquote', value: 'blockquote', count: 0 },
        { label: 'hr', value: 'hr', count: 0 },
        { label: 'br', value: 'br', count: 0 },
        { label: 'iframe', value: 'iframe', count: 0 },
        { label: 'video', value: 'video', count: 0 },
        { label: 'audio', value: 'audio', count: 0 }
      ]
    }
  },
  methods: {
    // 分析页面DOM
    async analyzePageDOM() {
      if (!this.targetUrl.trim()) {
        this.$message.warning('请输入要分析的页面URL')
        return
      }
      //保存到store
      this.$store.dispatch('agent/setAutoDomAnalysisUrl', this.targetUrl)
      this.$store.dispatch('agent/setAutoDomAnalysisCookie', this.cookieValue)

      this.isAnalyzing = true
      this.clearAnalysis()
      try {
        // 调用后端API分析页面DOM
        const response = await this.fetchPageDOM(this.targetUrl)
        if (response.success) {
          this.analyzedElements = response.elements
          this.filteredElements = [...this.analyzedElements]

          // 处理iframe元素
          let iframeElements = response.iframeElements
          if (iframeElements.length > 0) {
            iframeElements.forEach(iframeElement => {
              //每项添加iframe信息
              iframeElement.elements.forEach(element => {
                element.iframeIndex = iframeElement.iframeIndex
                element.iframeSrc = iframeElement.iframeSrc
                // 为iframe中的元素生成唯一ID，避免与主页面元素ID冲突
                element.id = `iframe_${iframeElement.iframeIndex}_${element.id}`
              })
              this.analyzedElements = [...this.analyzedElements, ...iframeElement.elements]
              this.filteredElements = [...this.filteredElements, ...iframeElement.elements]
            })
          }

          this.updateElementTypeCounts()
          this.$message.success(`成功分析页面，发现 ${this.analyzedElements.length} 个可交互元素`)
        } else {
          throw new Error(response.message || '分析失败')
        }
      } catch (error) {
        console.error('DOM分析失败:', error)
        this.$message.error('DOM分析失败: ' + error.message)
      } finally {
        this.isAnalyzing = false
      }
    },

    // 调用后端API获取页面DOM
    async fetchPageDOM(url) {
      try {
        // 调用真实的后端API
        const elementSelectors = this.elementTypes.map(item => item.value)
        const cookie = this.cookieValue.trim() || undefined
        const response = await analyzePageDOM(url, cookie, {
          elementTypes: elementSelectors,
          includeHidden: false,
          maxDepth: 50
        })

        if (response.data.code === 0) {
          return {
            success: true,
            elements: response.data.data.elements,
            iframeElements: response.data.data.iframeElements
          }
        } else {
          throw new Error(response.data.msg || '分析失败')
        }
      } catch (error) {
        console.warn('调用真实API失败，使用模拟数据:', error)
        this.$message.error('生成失败！')
      }
    },

    // 过滤元素
    filterElements() {
      let filtered = [...this.analyzedElements]

      // 按类型过滤
      if (this.elementTypeFilter !== 'all') {
        filtered = filtered.filter(element => element.type === this.elementTypeFilter)
      }

      // 按搜索文本过滤
      if (this.searchText && this.searchText.trim()) {
        const searchTerm = this.searchText.trim().toLowerCase()
        filtered = filtered.filter(element => {
          // 搜索元素类型
          if (element.type && element.type.toLowerCase().includes(searchTerm)) {
            return true
          }

          // 搜索元素文本
          if (element.text && element.text.toLowerCase().includes(searchTerm)) {
            return true
          }

          // 搜索选择器
          if (element.selector) {
            if (typeof element.selector === 'object') {
              // 搜索selector对象中的各个字段
              const selectorFields = [element.selector.selector, element.selector.strategy, element.selector.playwrightCode, element.selector.description, element.selector.stability].filter(Boolean).join(' ').toLowerCase()

              if (selectorFields.includes(searchTerm)) {
                return true
              }
            } else {
              // 搜索字符串selector
              if (element.selector.toLowerCase().includes(searchTerm)) {
                return true
              }
            }
          }

          // 搜索属性
          if (element.attributes && typeof element.attributes === 'object') {
            // 搜索属性名
            const attributeKeys = Object.keys(element.attributes).join(' ').toLowerCase()
            if (attributeKeys.includes(searchTerm)) {
              return true
            }

            // 搜索属性值
            const attributeValues = Object.values(element.attributes).join(' ').toLowerCase()
            if (attributeValues.includes(searchTerm)) {
              return true
            }
          }

          return false
        })
      }

      this.filteredElements = filtered
      this.updateElementTypeCounts()
    },

    // 切换元素选择状态
    toggleElementSelection(elementId) {
      const index = this.selectedElements.indexOf(elementId)
      if (index > -1) {
        this.selectedElements.splice(index, 1)
      } else {
        this.selectedElements.push(elementId)
      }
    },

    // 全选元素
    selectAllElements() {
      this.selectedElements = this.filteredElements.map(element => element.id)
    },

    // 清空选择
    clearSelection() {
      this.selectedElements = []
    },

    // 清空分析结果
    clearAnalysis() {
      this.analyzedElements = []
      this.filteredElements = []
      this.selectedElements = []
      this.searchText = ''
      this.elementTypeFilter = 'all'
      this.domContext = ''
      this.updateElementTypeCounts()
    },

    // 更新元素类型计数
    updateElementTypeCounts() {
      // 统计当前过滤结果中各类型元素的数量
      const typeCounts = {}
      this.filteredElements.forEach(element => {
        typeCounts[element.type] = (typeCounts[element.type] || 0) + 1
      })

      // 更新 elementTypes 中的计数
      this.elementTypes.forEach(typeItem => {
        typeItem.count = typeCounts[typeItem.value] || 0
      })
    },

    // 获取元素类型颜色
    getElementTypeColor(type) {
      const colorMap = {
        button: 'success',
        input: 'primary',
        link: 'info',
        select: 'warning'
      }
      return colorMap[type] || 'default'
    },

    // 获取选择器文本
    getSelectorText(selector) {
      if (!selector) return ''
      if (typeof selector === 'string') return selector
      if (typeof selector === 'object' && selector.selector) {
        return selector.selector
      }
      return ''
    },

    // 高亮搜索文本
    highlightSearchText(text) {
      if (!this.searchText || !text) return text
      const searchTerm = this.searchText.trim()
      if (!searchTerm) return text

      const regex = new RegExp(`(${searchTerm})`, 'gi')
      return text.replace(regex, '<mark>$1</mark>')
    },

    // 生成DOM上下文
    generateDOMContext() {
      const selectedElementsData = this.analyzedElements.filter(element => this.selectedElements.includes(element.id))

      let context = `页面URL: ${this.targetUrl}\n`
      context += `cookie: ${this.cookieValue}\n\n可交互元素列表:\n`

      selectedElementsData.forEach((element, index) => {
        context += `${index + 1}. ${element.type.toUpperCase()}: ${element.text || '无文本'}\n`
        context += `   选择器: ${this.getSelectorText(element.selector)}\n`
        if (element.selector && typeof element.selector === 'object') {
          //   context += `   选择策略: ${element.selector.strategy || 'unknown'}\n`
          if (element.selector.playwrightCode) {
            // context += `   Playwright: ${element.selector.playwrightCode}\n`
          }
        }
        if (element.attributes) {
          const attrs = Object.entries(element.attributes)
            .map(([key, value]) => `${key}="${value}"`)
            .join(' ')
          context += `   属性: ${attrs}\n`
        }
        context += '\n'
      })

      this.domContext = context

      // 将DOM上下文传递给父组件
      this.$emit('dom-context-generated', {
        url: this.targetUrl,
        context: context,
        elements: selectedElementsData
      })

      this.$message.success('DOM上下文生成成功')
    },

    // 复制DOM上下文
    copyDOMContext() {
      navigator.clipboard
        .writeText(this.domContext)
        .then(() => {
          this.$message.success('DOM上下文已复制到剪贴板')
        })
        .catch(err => {
          console.error('复制失败:', err)
          this.$message.error('复制失败')
        })
    },

    // 智能提取元素（基于父组件的测试描述）
    async smartExtractElements() {
      if (!this.targetUrl.trim()) {
        this.$message.warning('请输入要分析的页面URL')
        return
      }

      // 获取父组件的测试描述
      const testDescription = this.$parent.testDescription || ''
      if (!testDescription.trim()) {
        this.$message.warning('请先输入测试描述，以便智能提取相关元素')
        return
      }

      this.isSmartExtracting = true
      try {
        // 调用智能提取API
        const response = await extractKeyElements(this.targetUrl, testDescription, this.cookieValue.trim() || undefined)
        if (response.data.code === 0) {
          this.analyzedElements = response.data.data.elements
          this.filteredElements = [...this.analyzedElements]
          this.updateElementTypeCounts()
          // 自动选择所有智能提取的元素
          this.selectedElements = this.analyzedElements.map(element => element.id)
          this.$message.success(`智能提取成功，从 ${response.data.data.filteredFrom} 个元素中筛选出 ${this.analyzedElements.length} 个相关元素`)
        } else {
          throw new Error(response.data.msg || '智能提取失败')
        }
      } catch (error) {
        console.warn('智能提取API失败:', error)
      } finally {
        this.isSmartExtracting = false
      }
    }
  },

  mounted() {
    this.targetUrl = this.$store.state.agent.autoDomAnalysisUrl
    this.cookieValue = this.$store.state.agent.autoDomAnalysisCookie
  }
}
</script>

<style scoped>
.dom-analysis-section {
  height: 100%;
  padding: 15px;
  background-color: #f8f9fb;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.optional-tag {
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

.section-header i {
  margin-left: 8px;
  color: #909399;
  cursor: help;
}

.url-input-section {
  margin-bottom: 20px;
}

.input-group {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.url-input {
  flex: 1;
}

.cookie-input-group {
  margin-top: 10px;
}

.cookie-input {
  width: 100%;
  margin-bottom: 8px;
}

.cookie-hint {
  font-size: 12px;
  color: #909399;
  padding-left: 10px;
}

.analysis-results {
  background-color: white;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.filter-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.filter-controls .el-input {
  transition: all 0.3s;
}

.filter-controls .el-input:focus-within {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.elements-list {
  max-height: 350px;
  overflow-y: auto;
}

.element-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
  gap: 15px;
}

.element-item:hover {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
}

.element-item.selected {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.element-info {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.element-details {
  flex: 1;
  min-width: 0;
}

.element-type {
  width: 45px;
  flex-shrink: 0;
}

.element-text {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  margin-bottom: 6px;
  word-break: break-word;
}

/* 选择器详细信息样式 */
.element-selector-details {
  flex: 1;
  max-width: 400px;
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
  border: 1px solid #e9ecef;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.selector-object {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.selector-property {
  display: flex;
  align-items: flex-start;
  gap: 4px;
}

.property-key {
  color: #d73a49;
  font-weight: 500;
  /* min-width: 80px; */
  flex-shrink: 0;
}

.property-value {
  color: #032f62;
  word-break: break-all;
  /* flex: 1; */
}

.selector-simple {
  display: flex;
  gap: 4px;
}

.element-actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  padding-top: 2px;
}

.analysis-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.dom-context-preview {
  background-color: white;
  border-radius: 6px;
  padding: 15px;
}

.context-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: 500;
  color: #303133;
}

.context-content {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.context-content pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.5;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 属性展示样式 */
.element-attributes {
  margin-top: 8px;
}

.attributes-label {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
  /* margin-bottom: 4px; */
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.attributes-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.attribute-tag {
  background-color: #f4f4f5 !important;
  border: 1px solid #e4e7ed !important;
  padding: 2px 6px !important;
  border-radius: 3px !important;
  font-size: 11px !important;
  line-height: 1.2 !important;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.attribute-tag:hover {
  background-color: #ecf5ff !important;
  border-color: #b3d8ff !important;
}

.attr-key {
  font-weight: 600;
  color: #409eff;
}

.attr-separator {
  color: #909399;
}

.attr-value {
  color: #67c23a;
}

/* 搜索高亮样式 */
mark {
  background-color: #fff3cd;
  color: #856404;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
}

/* 加载动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 按钮加载状态样式 */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .element-selector-details {
    max-width: 300px;
  }

  .property-key {
    min-width: 70px;
  }
}

@media (max-width: 768px) {
  .element-item {
    flex-direction: column;
    gap: 10px;
  }

  .element-selector-details {
    max-width: none;
    width: 100%;
  }

  .element-info {
    width: 100%;
  }

  .filter-controls {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
