/**
 * 自动化测试数据流类型定义
 * 定义组件间数据传递的标准接口
 */

// HTML清洗结果数据结构
export const CleanedHtmlData = {
  // 原始HTML
  originalHtml: '',
  // 清洗后的HTML
  cleanedHtml: '',
  // 清洗统计信息
  stats: {
    originalSize: 0,
    cleanedSize: 0,
    removedElements: 0,
    removedAttributes: 0,
    compressionRatio: 0
  },
  // 清洗选项
  options: {
    removeEmptyTags: true,
    minify: false,
    prettify: true,
    removeComments: true,
    removeScripts: true,
    removeStyles: true,
    preserveDataAttributes: true
  },
  // 清洗时间戳
  timestamp: null,
  // 错误信息
  errors: []
}

// 元素数据结构
export const ElementData = {
  // 基本信息
  tagName: '',
  id: '',
  className: '',
  textContent: '',
  
  // 属性信息
  attributes: {},
  
  // 选择器信息
  selector: '',
  xpath: '',
  
  // 位置信息
  path: '',
  depth: 0,
  index: 0,
  
  // 智能筛选评分
  smartScore: 0,
  
  // 元素类型分类
  elementType: '', // interactive, form, text, media, container
  
  // 测试相关属性
  testId: '',
  ariaLabel: '',
  placeholder: '',
  
  // 父子关系
  parentSelector: '',
  children: [],
  
  // 元素状态
  isVisible: true,
  isEnabled: true,
  isSelected: false,
  
  // 元数据
  metadata: {
    importance: 0, // 重要性评分 0-10
    testability: 0, // 可测试性评分 0-10
    stability: 0, // 稳定性评分 0-10
    uniqueness: 0 // 唯一性评分 0-10
  }
}

// 智能筛选结果数据结构
export const SmartFilterResult = {
  // 筛选后的元素列表
  filteredElements: [],
  
  // 筛选统计
  stats: {
    totalElements: 0,
    filteredElements: 0,
    filterRatio: 0,
    averageScore: 0,
    highScoreElements: 0
  },
  
  // 筛选配置
  config: {
    threshold: 5,
    maxElements: 50,
    prioritizeInteractive: true,
    includeContainers: false,
    preserveHierarchy: true
  },
  
  // 筛选规则应用结果
  appliedRules: [],
  
  // 筛选时间戳
  timestamp: null
}

// AI生成配置数据结构
export const AIGenerateConfig = {
  // 基本配置
  framework: 'playwright', // playwright, selenium, cypress
  language: 'javascript', // javascript, typescript, python, java
  testType: 'e2e', // e2e, integration, unit
  
  // 生成选项
  options: {
    includeComments: true,
    includeAssertions: true,
    includeWaits: true,
    includeErrorHandling: false,
    generatePageObject: false,
    useDataTestIds: true
  },
  
  // 模板配置
  template: {
    testName: '',
    description: '',
    baseUrl: '',
    timeout: 30000,
    retries: 2
  }
}

// AI生成结果数据结构
export const AIGenerateResult = {
  // 生成的脚本代码
  script: '',
  
  // 脚本元数据
  metadata: {
    framework: '',
    language: '',
    testType: '',
    generatedAt: null,
    version: '1.0.0'
  },
  
  // 使用的元素信息
  usedElements: [],
  
  // 生成统计
  stats: {
    linesOfCode: 0,
    numberOfActions: 0,
    numberOfAssertions: 0,
    estimatedRunTime: 0
  },
  
  // 生成建议和警告
  suggestions: [],
  warnings: [],
  
  // 脚本质量评分
  quality: {
    maintainability: 0, // 可维护性 0-10
    reliability: 0, // 可靠性 0-10
    readability: 0, // 可读性 0-10
    coverage: 0 // 覆盖度 0-10
  }
}

// 工作流状态数据结构
export const WorkflowState = {
  // 当前步骤
  currentStep: 'html-cleaning', // html-cleaning, element-selection, script-generation
  
  // 步骤状态
  steps: {
    htmlCleaning: {
      completed: false,
      data: null,
      errors: []
    },
    elementSelection: {
      completed: false,
      data: null,
      errors: []
    },
    scriptGeneration: {
      completed: false,
      data: null,
      errors: []
    }
  },
  
  // 全局配置
  globalConfig: {
    projectName: '',
    targetUrl: '',
    description: '',
    tags: []
  },
  
  // 会话信息
  session: {
    id: '',
    startTime: null,
    lastUpdateTime: null,
    autoSave: true
  }
}

// 事件类型定义
export const EventTypes = {
  // HTML清洗事件
  HTML_CLEANING_STARTED: 'html-cleaning-started',
  HTML_CLEANING_COMPLETED: 'html-cleaning-completed',
  HTML_CLEANING_FAILED: 'html-cleaning-failed',
  
  // 元素选择事件
  ELEMENTS_PARSED: 'elements-parsed',
  ELEMENT_SELECTED: 'element-selected',
  ELEMENT_DESELECTED: 'element-deselected',
  SMART_FILTER_APPLIED: 'smart-filter-applied',
  SELECTION_COMPLETED: 'selection-completed',
  
  // 脚本生成事件
  SCRIPT_GENERATION_STARTED: 'script-generation-started',
  SCRIPT_GENERATION_COMPLETED: 'script-generation-completed',
  SCRIPT_GENERATION_FAILED: 'script-generation-failed',
  SCRIPT_UPDATED: 'script-updated',
  
  // 工作流事件
  WORKFLOW_STEP_CHANGED: 'workflow-step-changed',
  WORKFLOW_DATA_UPDATED: 'workflow-data-updated',
  WORKFLOW_RESET: 'workflow-reset',
  
  // 项目管理事件
  PROJECT_SAVED: 'project-saved',
  PROJECT_LOADED: 'project-loaded',
  PROJECT_DELETED: 'project-deleted'
}

// 错误类型定义
export const ErrorTypes = {
  HTML_PARSE_ERROR: 'html-parse-error',
  ELEMENT_SELECTION_ERROR: 'element-selection-error',
  SCRIPT_GENERATION_ERROR: 'script-generation-error',
  API_ERROR: 'api-error',
  VALIDATION_ERROR: 'validation-error',
  NETWORK_ERROR: 'network-error'
}

// 工具函数
export const DataUtils = {
  // 创建默认的清洗数据
  createDefaultCleanedData() {
    return {
      ...CleanedHtmlData,
      timestamp: new Date(),
      errors: []
    }
  },
  
  // 创建默认的元素数据
  createDefaultElementData(element = {}) {
    return {
      ...ElementData,
      ...element,
      metadata: {
        ...ElementData.metadata,
        ...element.metadata
      }
    }
  },
  
  // 创建默认的筛选结果
  createDefaultFilterResult() {
    return {
      ...SmartFilterResult,
      timestamp: new Date()
    }
  },
  
  // 创建默认的生成配置
  createDefaultGenerateConfig() {
    return {
      ...AIGenerateConfig
    }
  },
  
  // 创建默认的工作流状态
  createDefaultWorkflowState() {
    return {
      ...WorkflowState,
      session: {
        ...WorkflowState.session,
        id: this.generateSessionId(),
        startTime: new Date(),
        lastUpdateTime: new Date()
      }
    }
  },
  
  // 生成会话ID
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  },
  
  // 验证数据结构
  validateCleanedData(data) {
    return data && typeof data.cleanedHtml === 'string' && data.stats
  },
  
  validateElementData(data) {
    return data && typeof data.tagName === 'string' && typeof data.selector === 'string'
  },
  
  validateGenerateResult(data) {
    return data && typeof data.script === 'string' && data.metadata
  }
}

// 导出所有类型和工具
export default {
  CleanedHtmlData,
  ElementData,
  SmartFilterResult,
  AIGenerateConfig,
  AIGenerateResult,
  WorkflowState,
  EventTypes,
  ErrorTypes,
  DataUtils
}
