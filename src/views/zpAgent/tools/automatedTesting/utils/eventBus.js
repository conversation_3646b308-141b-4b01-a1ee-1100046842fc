/**
 * 事件总线 - 组件间通信中心
 * 提供发布订阅模式的事件通信机制
 */

import { EventTypes } from '../types/dataTypes.js'

class EventBus {
  constructor() {
    this.events = {}
    this.maxListeners = 100
    this.debug = process.env.NODE_ENV === 'development'
  }

  /**
   * 订阅事件
   * @param {string} eventType 事件类型
   * @param {function} callback 回调函数
   * @param {object} context 上下文对象
   */
  on(eventType, callback, context = null) {
    if (!this.events[eventType]) {
      this.events[eventType] = []
    }

    if (this.events[eventType].length >= this.maxListeners) {
      console.warn(`EventBus: 事件 ${eventType} 的监听器数量超过限制 ${this.maxListeners}`)
    }

    this.events[eventType].push({
      callback,
      context,
      once: false
    })

    if (this.debug) {
      console.log(`EventBus: 订阅事件 ${eventType}`)
    }

    // 返回取消订阅函数
    return () => this.off(eventType, callback)
  }

  /**
   * 一次性订阅事件
   * @param {string} eventType 事件类型
   * @param {function} callback 回调函数
   * @param {object} context 上下文对象
   */
  once(eventType, callback, context = null) {
    if (!this.events[eventType]) {
      this.events[eventType] = []
    }

    this.events[eventType].push({
      callback,
      context,
      once: true
    })

    if (this.debug) {
      console.log(`EventBus: 一次性订阅事件 ${eventType}`)
    }
  }

  /**
   * 取消订阅事件
   * @param {string} eventType 事件类型
   * @param {function} callback 回调函数
   */
  off(eventType, callback) {
    if (!this.events[eventType]) {
      return
    }

    this.events[eventType] = this.events[eventType].filter(
      listener => listener.callback !== callback
    )

    if (this.debug) {
      console.log(`EventBus: 取消订阅事件 ${eventType}`)
    }
  }

  /**
   * 发布事件
   * @param {string} eventType 事件类型
   * @param {any} data 事件数据
   */
  emit(eventType, data = null) {
    if (!this.events[eventType]) {
      if (this.debug) {
        console.log(`EventBus: 没有监听器订阅事件 ${eventType}`)
      }
      return
    }

    const listeners = [...this.events[eventType]]
    
    listeners.forEach(listener => {
      try {
        if (listener.context) {
          listener.callback.call(listener.context, data)
        } else {
          listener.callback(data)
        }
      } catch (error) {
        console.error(`EventBus: 事件 ${eventType} 处理出错:`, error)
      }
    })

    // 移除一次性监听器
    this.events[eventType] = this.events[eventType].filter(
      listener => !listener.once
    )

    if (this.debug) {
      console.log(`EventBus: 发布事件 ${eventType}`, data)
    }
  }

  /**
   * 清除所有事件监听器
   */
  clear() {
    this.events = {}
    if (this.debug) {
      console.log('EventBus: 清除所有事件监听器')
    }
  }

  /**
   * 清除指定事件的所有监听器
   * @param {string} eventType 事件类型
   */
  clearEvent(eventType) {
    delete this.events[eventType]
    if (this.debug) {
      console.log(`EventBus: 清除事件 ${eventType} 的所有监听器`)
    }
  }

  /**
   * 获取事件统计信息
   */
  getStats() {
    const stats = {}
    Object.keys(this.events).forEach(eventType => {
      stats[eventType] = this.events[eventType].length
    })
    return stats
  }
}

// 创建全局事件总线实例
const eventBus = new EventBus()

// 预定义的事件处理器
export const EventHandlers = {
  // HTML清洗事件处理器
  htmlCleaning: {
    onStarted: (callback) => eventBus.on(EventTypes.HTML_CLEANING_STARTED, callback),
    onCompleted: (callback) => eventBus.on(EventTypes.HTML_CLEANING_COMPLETED, callback),
    onFailed: (callback) => eventBus.on(EventTypes.HTML_CLEANING_FAILED, callback)
  },

  // 元素选择事件处理器
  elementSelection: {
    onElementsParsed: (callback) => eventBus.on(EventTypes.ELEMENTS_PARSED, callback),
    onElementSelected: (callback) => eventBus.on(EventTypes.ELEMENT_SELECTED, callback),
    onElementDeselected: (callback) => eventBus.on(EventTypes.ELEMENT_DESELECTED, callback),
    onSmartFilterApplied: (callback) => eventBus.on(EventTypes.SMART_FILTER_APPLIED, callback),
    onSelectionCompleted: (callback) => eventBus.on(EventTypes.SELECTION_COMPLETED, callback)
  },

  // 脚本生成事件处理器
  scriptGeneration: {
    onStarted: (callback) => eventBus.on(EventTypes.SCRIPT_GENERATION_STARTED, callback),
    onCompleted: (callback) => eventBus.on(EventTypes.SCRIPT_GENERATION_COMPLETED, callback),
    onFailed: (callback) => eventBus.on(EventTypes.SCRIPT_GENERATION_FAILED, callback),
    onUpdated: (callback) => eventBus.on(EventTypes.SCRIPT_UPDATED, callback)
  },

  // 工作流事件处理器
  workflow: {
    onStepChanged: (callback) => eventBus.on(EventTypes.WORKFLOW_STEP_CHANGED, callback),
    onDataUpdated: (callback) => eventBus.on(EventTypes.WORKFLOW_DATA_UPDATED, callback),
    onReset: (callback) => eventBus.on(EventTypes.WORKFLOW_RESET, callback)
  }
}

// 事件发射器
export const EventEmitters = {
  // HTML清洗事件发射器
  htmlCleaning: {
    started: (data) => eventBus.emit(EventTypes.HTML_CLEANING_STARTED, data),
    completed: (data) => eventBus.emit(EventTypes.HTML_CLEANING_COMPLETED, data),
    failed: (error) => eventBus.emit(EventTypes.HTML_CLEANING_FAILED, error)
  },

  // 元素选择事件发射器
  elementSelection: {
    elementsParsed: (data) => eventBus.emit(EventTypes.ELEMENTS_PARSED, data),
    elementSelected: (data) => eventBus.emit(EventTypes.ELEMENT_SELECTED, data),
    elementDeselected: (data) => eventBus.emit(EventTypes.ELEMENT_DESELECTED, data),
    smartFilterApplied: (data) => eventBus.emit(EventTypes.SMART_FILTER_APPLIED, data),
    selectionCompleted: (data) => eventBus.emit(EventTypes.SELECTION_COMPLETED, data)
  },

  // 脚本生成事件发射器
  scriptGeneration: {
    started: (data) => eventBus.emit(EventTypes.SCRIPT_GENERATION_STARTED, data),
    completed: (data) => eventBus.emit(EventTypes.SCRIPT_GENERATION_COMPLETED, data),
    failed: (error) => eventBus.emit(EventTypes.SCRIPT_GENERATION_FAILED, error),
    updated: (data) => eventBus.emit(EventTypes.SCRIPT_UPDATED, data)
  },

  // 工作流事件发射器
  workflow: {
    stepChanged: (data) => eventBus.emit(EventTypes.WORKFLOW_STEP_CHANGED, data),
    dataUpdated: (data) => eventBus.emit(EventTypes.WORKFLOW_DATA_UPDATED, data),
    reset: () => eventBus.emit(EventTypes.WORKFLOW_RESET)
  }
}

export default eventBus
