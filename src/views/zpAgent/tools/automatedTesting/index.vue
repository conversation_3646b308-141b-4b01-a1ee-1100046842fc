<template>
  <div class="zp-agent automated-testing">
    <!-- <StylesComponent> -->
    <div class="container full-height-container">
      <div class="analysis-header">
        <div class="header-left">
          <h1>WebUI自动化测试智能体</h1>
          <p style="padding-top: 6px">智能录制、生成、管理、执行UI自动化测试用例</p>
        </div>
        <div class="header-right">
          <div class="project-selector">
            <label class="project-label">项目：</label>
            <el-select v-model="selectedProject" placeholder="请选择项目" @change="handleProjectChange" class="project-select">
              <template slot="prefix" v-if="selectedProjectInfo">
                <div class="selected-project-display">
                  <img :src="selectedProjectInfo.image" :alt="selectedProjectInfo.projectName" class="selected-project-icon" />
                </div>
              </template>
              <el-option v-for="project in projectList" :key="project.id" :label="project.projectName" :value="project.id">
                <div class="project-option-item">
                  <img :src="project.image" :alt="project.projectName" class="project-icon" />
                  <span>{{ project.projectName }}</span>
                </div>
              </el-option>
              <el-option value="create-project" class="create-project-option">
                <div class="create-project-item" @click.stop="showCreateProject">
                  <i class="el-icon-plus"></i>
                  <span>创建项目</span>
                </div>
              </el-option>
            </el-select>
          </div>
        </div>
      </div>

      <div class="analysis-tabs">
        <div class="tabs-left">
          <div class="analysis-tab" :class="{ active: activeTab === 'dom' }" @click="switchTab('dom')">DOM分析</div>
          <!-- <div class="analysis-tab" :class="{ active: activeTab === 'agent' }" @click="switchTab('agent')">智能体</div> -->
          <div class="analysis-tab" :class="{ active: activeTab === 'generation' }" @click="switchTab('generation')">AI生成</div>
          <div class="analysis-tab" :class="{ active: activeTab === 'record' }" @click="switchTab('record')">录制调试</div>
          <div class="analysis-tab" :class="{ active: activeTab === 'case' }" @click="switchTab('case')">用例管理</div>
          <div class="analysis-tab" :class="{ active: activeTab === 'task' }" @click="switchTab('task')">任务管理</div>
          <div class="analysis-tab" :class="{ active: activeTab === 'report' }" @click="switchTab('report')">测试报告</div>
          <div class="analysis-tab" :class="{ active: activeTab === 'setting' }" @click="switchTab('setting')">设置</div>
        </div>

        <!-- 服务状态显示区域 -->
        <div class="tabs-right">
          <div class="service-status-container" @click="goToSettings" :title="serviceRunning ? `服务运行中${servicePid ? ' (PID: ' + servicePid + ')' : ''}` : '服务已停止，点击前往设置页面'">
            <!-- <i :class="['status-icon', serviceRunning ? 'ri-checkbox-circle-fill' : 'ri-close-circle-fill']"></i> -->
            <span class="status-label">可执行状态:</span>
            <span :class="['status-badge', serviceRunning ? 'status-running' : 'status-stopped']">
              {{ serviceRunning ? '运行中' : '已停止' }}
            </span>
          </div>
        </div>
      </div>

      <div class="tab-content">
        <!-- DOM分析 -->
        <domAnalysis v-show="activeTab === 'dom'" ref="domAnalysis" />
        <!-- 智能体 -->
        <autoAgent v-show="activeTab === 'agent'" ref="autoAgent" />

        <!-- AI生成 -->
        <generation v-show="activeTab === 'generation'" ref="generation" />

        <!-- 录制回放 -->
        <record v-show="activeTab === 'record'" ref="record" @optimize-case="optimizeTestCase" />

        <div class="automated-testing-container" v-show="activeTab === 'case'">
          <!-- 分组管理组件 -->
          <groupManager @submit-query="handleQuery" @group-selected="handleGroupSelected" ref="groupManager" />

          <!-- 用例管理组件 -->
          <caseManage @debug-case="handleDebugCase" ref="caseManage" />
        </div>

        <!-- 任务管理组件 -->
        <taskManager v-show="activeTab === 'task'" ref="taskManager" />

        <!-- 测试报告组件 -->
        <reportManager v-show="activeTab === 'report'" ref="reportManager" />

        <!-- 设置组件 -->
        <setting v-show="activeTab === 'setting'" ref="setting" />
      </div>

      <!-- 项目管理器组件 -->
      <projectManager ref="projectManager" @project-created="handleProjectCreated" />
    </div>
    <!-- </StylesComponent> -->
  </div>
</template>

<script>
import domAnalysis from './components/dom/domAnalysis.vue'
import generation from './components/generation/generationCase.vue'
import groupManager from './components/group/groupManager.vue'
import caseManage from './components/case/caseManager.vue'
import reportManager from './components/report/reportManager.vue'
import taskManager from './components/task/taskManager.vue'
import StylesComponent from './components/styles.vue'
import setting from './components/setting/setting.vue'
import record from './components/record/recordCase.vue'
import projectManager from './components/project/projectManager.vue'
import autoAgent from './components/agent/autoAgent.vue'
import { getPlaywrightServiceStatus } from '@/api/agent/playwright'
import { getProject } from '@/api/zpInterface/project'
export default {
  name: 'automatedTesting',
  components: {
    generation,
    record,
    groupManager,
    caseManage,
    reportManager,
    taskManager,
    StylesComponent,
    setting,
    projectManager,
    autoAgent,
    domAnalysis
  },
  provide() {
    return {
      getCurrentProjectId: () => this.selectedProject
    }
  },

  data() {
    return {
      title: '自动化测试',
      activeTab: 'case',
      serviceRunning: false,
      servicePid: null,
      serviceCheckInterval: null,
      selectedProject: null,
      projectList: []
    }
  },
  computed: {
    selectedProjectInfo() {
      if (!this.selectedProject || !this.projectList.length) {
        return null
      }
      return this.projectList.find(project => project.id === this.selectedProject)
    }
  },
  methods: {
    // tab切换
    switchTab(tab) {
      this.activeTab = tab
      this.$store.dispatch('agent/setAutoTestActiveTag', tab)
      if (tab === 'report') {
        this.$refs.reportManager.getAutoReportList(this.selectedProject)
      } else if (tab === 'case') {
        this.$refs.caseManage.getCaseList(this.selectedProject)
      } else if (tab === 'task') {
        this.$refs.taskManager.getTaskList(this.selectedProject)
      }
    },

    // 分组选择处理
    handleGroupSelected(group) {
      if (group) {
        let groupId = group.id
        if (groupId == 9999) {
          groupId = 0
        }
        this.$refs.caseManage.updateGroupId(groupId)
        this.$refs.caseManage.getCaseList(this.projectId, '', groupId)
      } else {
        this.$refs.caseManage.updateGroupId(0)
        this.$refs.caseManage.getCaseList(this.projectId, '', 0)
      }
    },

    // 处理查询提交
    handleQuery(type, formData) {
      // 这里可以添加统一处理查询的逻辑
    },

    // 用例更新处理
    handleDebugCase(caseForm) {
      this.activeTab = 'record'
      this.$refs.record.updateCaseForm(caseForm)
    },

    // 跳转到设置页面
    goToSettings() {
      this.switchTab('setting')
    },

    // 检查服务状态
    async checkServiceStatus() {
      try {
        const response = await getPlaywrightServiceStatus()
        if (response.data.code === 0) {
          this.serviceRunning = response.data.data.status
          this.servicePid = response.data.data.pid || null
        } else {
          this.serviceRunning = false
          this.servicePid = null
        }
      } catch (error) {
        this.serviceRunning = false
        this.servicePid = null
      }
    },

    // 优化用例处理
    optimizeTestCase(script) {
      this.activeTab = 'generation'
      this.$refs.generation.aiOptimizeTestCase(script)
    },

    // 处理项目选择
    handleProjectChange(value) {
      if (value === 'create-project') {
        // 重置选择器的值，避免显示"创建项目"
        this.$nextTick(() => {
          this.selectedProject = this.$store.state.agent.selectedProjectId || (this.projectList.length > 0 ? this.projectList[0].id : null)
        })
        return
      }
      // 更新store中的项目ID
      this.$store.dispatch('agent/setSelectedProjectId', value)
      // 更新URL参数
      this.updateUrlParams(value)
      // 通知所有子组件项目已更改
      this.notifyProjectChange(value)
    },

    // 通知所有子组件项目已更改
    notifyProjectChange(projectId) {
      // 更新各个子组件的projectId并重新加载数据
      this.$nextTick(() => {
        // 用例管理组件
        if (this.$refs.caseManage) {
          this.$refs.caseManage.updateProjectId(projectId)
        }

        // 分组管理组件
        if (this.$refs.groupManager) {
          this.$refs.groupManager.updateProjectId(projectId)
        }

        // 任务管理组件
        if (this.$refs.taskManager) {
          this.$refs.taskManager.updateProjectId(projectId)
        }

        // 测试报告组件
        if (this.$refs.reportManager) {
          this.$refs.reportManager.updateProjectId(projectId)
        }

        // 录制组件
        if (this.$refs.record) {
          this.$refs.record.updateProjectId(projectId)
        }
      })
    },

    // 更新URL参数
    updateUrlParams(projectId) {
      const url = new URL(window.location)
      if (projectId) {
        url.searchParams.set('projectId', projectId)
      } else {
        url.searchParams.delete('projectId')
      }
      window.history.replaceState({}, '', url)
    },

    // 初始化项目列表
    async initProjectList() {
      let res = await getProject(5)
      if (res.code == 0) {
        this.projectList = res.data.project
        for (let i = 0; i < this.projectList.length; i++) {
          this.projectList[i].image = require('@/views/zpAgent/assets/images/agent' + (i + 1) + '.svg')
        }

        console.log(this.projectList)
        // 初始化项目选择
        this.initProjectSelection()
      }
    },

    // 初始化项目选择
    initProjectSelection() {
      // 优先从URL参数获取项目ID
      const urlParams = new URLSearchParams(window.location.search)
      const urlProjectId = urlParams.get('projectId')
      // 其次从store获取项目ID
      const storeProjectId = this.$store.state.agent.selectedProjectId

      let targetProjectId = null

      if (urlProjectId) {
        // 检查URL中的项目ID是否在项目列表中存在
        const projectExists = this.projectList.some(project => project.id.toString() === urlProjectId)
        if (projectExists) {
          targetProjectId = parseInt(urlProjectId)
        }
      }

      if (!targetProjectId && storeProjectId) {
        // 检查store中的项目ID是否在项目列表中存在
        const projectExists = this.projectList.some(project => project.id === storeProjectId)
        if (projectExists) {
          targetProjectId = storeProjectId
        }
      }

      // 如果以上都没有或不存在，默认选择第一个项目
      if (!targetProjectId && this.projectList.length > 0) {
        targetProjectId = this.projectList[0].id
      }

      if (targetProjectId) {
        this.selectedProject = targetProjectId
        // 更新store
        this.$store.dispatch('agent/setSelectedProjectId', targetProjectId)
        // 更新URL参数
        this.updateUrlParams(targetProjectId)
        // 通知所有子组件项目已更改
        this.notifyProjectChange(targetProjectId)
      }
    },

    // 显示创建项目对话框
    showCreateProject() {
      this.$refs.projectManager.show()
    },

    // 处理项目创建
    handleProjectCreated(project) {
      // 将新项目添加到项目列表，添加图片
      const newProject = {
        ...project,
        image: require('@/views/zpAgent/assets/images/agent1.svg') // 新项目使用默认图片
      }
      this.projectList.push(newProject)

      // 选中新创建的项目
      this.selectedProject = newProject.id
      // 更新store
      this.$store.dispatch('agent/setSelectedProjectId', newProject.id)
      // 更新URL参数
      this.updateUrlParams(newProject.id)
      // 通知所有子组件项目已更改
      this.notifyProjectChange(newProject.id)

      this.$message.success(`项目 "${project.projectName}" 创建成功`)
    }
  },
  mounted() {
    // 支持从URL参数设置activeTab
    const urlParams = new URLSearchParams(window.location.search)
    const tabFromUrl = urlParams.get('tab')

    if (tabFromUrl && ['agent', 'generation', 'record', 'case', 'task', 'report', 'setting'].includes(tabFromUrl)) {
      this.activeTab = tabFromUrl
      this.$store.dispatch('agent/setAutoTestActiveTag', tabFromUrl)
    } else {
      this.activeTab = this.$store.state.agent.autoTestActiveTag
      if (!this.activeTab) {
        this.activeTab = 'agent'
      }
    }

    // 初始化项目列表
    this.initProjectList()

    // 开始检查服务状态
    this.checkServiceStatus()
    // 每10秒检查一次服务状态
    this.serviceCheckInterval = setInterval(this.checkServiceStatus, 15000)
  },

  beforeDestroy() {
    // 清除定时器
    if (this.serviceCheckInterval) {
      clearInterval(this.serviceCheckInterval)
    }
  }
}
</script>

<style>
@import '../../assets/css/style.css';

.zp-agent {
  background-color: var(--background-color);
  min-height: calc(100vh - var(--header-height) - 0px);
  height: 100%;
  overflow-y: hidden;
}

.automated-testing h1 {
  margin: 0.5em 0;
}

.case-manager-container h1 {
  margin: 0.5em 0 !important;
}

.full-height-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - var(--header-height));
  max-width: 90%;
  margin: 0 auto;
  padding-bottom: 0px !important;
}

.tab-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.automated-testing .analysis-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  margin-bottom: 10px;
  background-color: #f7f9fc;
}

.automated-testing .header-left {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  gap: 12px;
}

.automated-testing .header-left h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0.5em 0;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.automated-testing .header-left p {
  color: var(--text-light);
  font-size: 14px;
  margin: 0;
}

.automated-testing .header-right {
  display: flex;
  align-items: center;
}

.automated-testing .project-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f7f9fc;
  /* border: 1px solid #e0e0e0; */
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: none;
  transition: var(--transition);
}

.automated-testing .project-selector:hover {
  background-color: var(--card-bg-color);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.automated-testing .project-label {
  font-size: 14px;
  color: #909399;
  font-weight: 400;
  white-space: nowrap;
}

.automated-testing .project-selector:hover .project-label {
  color: var(--text-color);
  font-weight: 500;
}

.automated-testing .project-select {
  width: 200px;
}

.automated-testing .analysis-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  border-bottom: 1px solid var(--border-color);
  /* padding-right: 1rem; */
}

.automated-testing .tabs-left {
  display: flex;
}

.automated-testing .tabs-right {
  display: flex;
  align-items: center;
}

.automated-testing .analysis-tab {
  padding: 10px 20px;
  cursor: pointer;
  position: relative;
  font-size: 14px;
  color: var(--text-light);
  transition: var(--transition);
}

.automated-testing .analysis-tab.active {
  /* color: var(--primary-color); */
  color: var(--primary-color-V1);
  font-weight: 500;
}

.automated-testing .analysis-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;

  background: var(--primary-gradient);
}

.automated-testing .analysis-tab:hover:not(.active) {
  color: var(--text-color);
}

/* 服务状态样式 */
.automated-testing .service-status-container {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 13px;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.automated-testing .service-status-container:hover {
  background-color: rgba(var(--primary-color-rgb), 0.05);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.automated-testing .status-icon {
  font-size: 16px;
  transition: var(--transition);
}

.automated-testing .status-icon.ri-checkbox-circle-fill {
  color: var(--success-color);
}

.automated-testing .status-icon.ri-close-circle-fill {
  color: var(--error-color);
}

.automated-testing .status-label {
  color: var(--text-light);
  font-weight: 500;
}

.automated-testing .status-badge {
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  transition: var(--transition);
}
.automated-testing .status-running {
  background-color: rgba(52, 211, 153, 0.15); /* 使用主色的 15% 透明度 */
  color: var(--auto-success-btn-color); /* 使用主色作为文本颜色 */
  border: 1px solid rgba(52, 211, 153, 0.3); /* 使用主色的 30% 透明度作为边框颜色 */
}

.automated-testing .status-stopped {
  background-color: rgba(239, 68, 68, 0.15); /* 使用主色的 15% 透明度 */
  color: var(--auto-error-btn-color); /* 使用主色作为文本颜色 */
  border: 1px solid rgba(239, 68, 68, 0.3); /* 使用主色的 30% 透明度作为边框颜色 */
}

.automated-testing-container {
  display: flex;
  gap: 15px;
  height: 100%;
  overflow-y: auto;
}

.automated-testing-container > * {
  overflow-y: auto;
  height: 100%;
}

/* 使任务管理和测试报告组件也能占满高度并支持滚动 */
.automated-testing .taskManager,
.automated-testing .reportManager {
  height: 100%;
  overflow-y: auto;
}

/* 子组件共同使用 */
.automated-testing .card-container {
  flex: 1;
  background-color: var(--card-bg-color);
  border-radius: var(--radius);
  box-shadow: var(--shadow-card);
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.automated-testing .btn {
  margin-left: 0px !important;
}

.automated-testing .btn-danger {
  background-color: #f56c6c;
  color: #fff;
  border: none;
  padding: 9px 20px;
  border-radius: 4px;
  cursor: pointer;
}

.automated-testing .btn-cancel:hover {
  background-color: #e6e6e6;
}

.automated-testing .btn-danger:hover {
  background-color: #f78989;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .automated-testing .tabs-right {
    margin-left: 0.5rem;
  }

  .automated-testing .service-status-container {
    padding: 6px 12px;
    font-size: 12px;
  }

  .automated-testing .project-select {
    min-width: 160px;
  }
}

@media (max-width: 768px) {
  .automated-testing .analysis-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .automated-testing .header-right {
    justify-content: center;
  }

  .automated-testing .project-selector {
    justify-content: center;
  }

  .automated-testing .analysis-tabs {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
    padding-right: 0;
  }

  .automated-testing .tabs-right {
    margin-left: 0;
    justify-content: center;
  }

  .automated-testing .service-status-container {
    margin-left: 0;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .automated-testing .project-select {
    min-width: 140px;
  }

  .automated-testing .project-label {
    font-size: 13px;
  }

  .automated-testing .service-status-container {
    padding: 4px 8px;
    font-size: 11px;
  }

  .automated-testing .status-label {
    display: none; /* 在小屏幕上隐藏标签，只显示图标和状态 */
  }
}

/* 自定义分页器样式 - 使用更高优先级 */
.zp-agent.automated-testing >>> .el-pagination .el-pager li {
  background-color: #fff !important;
  border: 1px solid #dcdfe6 !important;
  color: #606266 !important;
  font-size: 13px !important;
  min-width: 30px !important;
  height: 30px !important;
  line-height: 28px !important;
  margin: 0 3px !important;
  border-radius: 4px !important;
  transition: all 0.3s !important;
}

.zp-agent.automated-testing >>> .el-pagination .el-pager li:hover {
  color: #5470c6 !important;
  border-color: #5470c6 !important;
}

.zp-agent.automated-testing >>> .el-pagination .el-pager li.active {
  background-color: #5470c6 !important;
  border-color: #5470c6 !important;
  color: #fff !important;
}

.zp-agent.automated-testing >>> .el-pagination .btn-prev,
.zp-agent.automated-testing >>> .el-pagination .btn-next {
  background-color: #fff !important;
  border: 1px solid #dcdfe6 !important;
  color: #606266 !important;
  font-size: 12px !important;
  min-width: 30px !important;
  height: 30px !important;
  line-height: 28px !important;
  margin: 0 3px !important;
  border-radius: 4px !important;
  transition: all 0.3s !important;
}

.zp-agent.automated-testing >>> .el-pagination .btn-prev:hover,
.zp-agent.automated-testing >>> .el-pagination .btn-next:hover {
  color: #5470c6 !important;
  border-color: #5470c6 !important;
}

.zp-agent.automated-testing >>> .el-pagination .btn-prev:disabled,
.zp-agent.automated-testing >>> .el-pagination .btn-next:disabled {
  color: #c0c4cc !important;
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  cursor: not-allowed !important;
}

.zp-agent.automated-testing >>> .el-pagination {
  font-weight: normal !important;
}

/* 创建项目选项样式 */
.automated-testing .create-project-option {
  border-top: 1px solid #f0f0f0;
  margin-top: 4px;
  padding-top: 4px;
  background: #f7f9fc !important;
}

.automated-testing .create-project-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--primary-color);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.automated-testing ::v-deep .create-project-item:hover {
  color: var(--primary-color-dark);
  transform: translateX(2px);
}

.automated-testing ::v-deep .create-project-item i {
  font-size: 14px;
}

/* 项目选项图标样式 */
.project-option-item .project-option-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.project-option-item .project-icon {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  flex-shrink: 0;
}

/* 选中项目显示样式 */

.automated-testing .selected-project-icon {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  flex-shrink: 0;
  margin-top: 8px;
}
</style>
