!(function (a) {
  var l,
    t,
    c,
    h,
    e,
    i =
      '<svg><symbol id="icon-erweima" viewBox="0 0 1024 1024"><path d="M399.872 908.288H199.68c-43.008 0-78.336-35.328-78.336-78.336v-223.232c0-10.752 8.704-19.456 19.456-19.456s19.456 8.704 19.456 19.456v223.232c0 21.504 17.408 38.912 38.912 38.912H399.36c10.752 0 19.456 8.704 19.456 19.456s-8.192 19.968-18.944 19.968zM830.464 908.288h-211.968c-10.752 0-19.456-8.704-19.456-19.456s8.704-19.456 19.456-19.456h211.968c21.504 0 38.912-17.408 38.912-38.912v-223.232c0-10.752 8.704-19.456 19.456-19.456s19.456 8.704 19.456 19.456v223.232c0.512 42.496-34.816 77.824-77.824 77.824zM140.8 407.04c-10.752 0-19.456-8.704-19.456-19.456V199.168c0-43.008 35.328-78.336 78.336-78.336h200.704c10.752 0 19.456 8.704 19.456 19.456s-8.704 19.456-19.456 19.456H199.68c-21.504 0-38.912 17.408-38.912 38.912v188.416c-0.512 11.264-9.216 19.968-19.968 19.968zM888.832 404.48c-10.752 0-19.456-8.704-19.456-19.456V199.168c0-21.504-17.408-38.912-38.912-38.912h-211.968c-10.752 0-19.456-8.704-19.456-19.456s8.704-19.456 19.456-19.456h211.968c43.008 0 78.336 35.328 78.336 78.336v185.856c0 10.24-8.704 18.944-19.968 18.944z" fill="#4C4C4C" ></path><path d="M907.776 524.288H122.368c-10.752 0-19.456-8.704-19.456-19.456s8.704-19.456 19.456-19.456h785.408c10.752 0 19.456 8.704 19.456 19.456s-8.704 19.456-19.456 19.456z" fill="#4C4C4C" ></path></symbol><symbol id="icon-huoquxuke" viewBox="0 0 1024 1024"><path d="M64 960 64 64l504 0 0 112L176 176l0 672 672 0L848 512 960 512l0 448L64 960zM550.848 626.752C550.848 626.752 550.848 626.752 550.848 626.752l-14.272 14.272c-10.944 10.944-28.672 10.944-39.616 0 0 0 0 0 0 0L457.408 601.408c0 0 0 0 0 0L299.008 443.008c-10.944-10.944-10.944-28.672 0-39.616l39.616-39.616c10.944-10.944 28.672-10.944 39.616 0L516.8 502.4l295.488-295.488c18.816-18.816 42.944-25.216 53.888-14.272l39.616 39.616c10.944 10.944 4.544 35.072-14.272 53.888L550.848 626.752z"  ></path></symbol><symbol id="icon-daochu" viewBox="0 0 1024 1024"><path d="M516.394 102.699c10.469 0 19.289 3.592 26.453 10.744L728.91 299.506c7.164 7.164 10.744 15.98 10.744 26.452 0 10.668-3.537 19.532-10.6 26.6-7.072 7.076-15.938 10.608-26.598 10.608-10.475 0-19.299-3.584-26.459-10.748l-122.381-122.68v431.126c0 10.266-3.635 19.041-10.902 26.316-7.277 7.271-16.049 10.908-26.316 10.908-10.281 0-19.049-3.637-26.32-10.908-7.276-7.275-10.92-16.051-10.92-26.316V229.738l-122.364 122.68c-7.56 7.168-16.376 10.748-26.464 10.748-10.28 0-19.04-3.64-26.316-10.908-7.276-7.272-10.916-16.052-10.916-26.32 0-10.084 3.576-18.908 10.744-26.456l186.061-186.063c7.16-7.168 15.992-10.744 26.457-10.744l0.034 0.024z m367.71 520.935c10.264 0 19.045 3.639 26.309 10.912 7.268 7.275 10.912 16.051 10.912 26.328v148.818c0 31.018-10.748 57.277-32.264 78.793-21.896 21.889-48.064 32.84-78.492 32.84H214.303c-30.417 0-56.772-10.855-79.06-32.568-21.716-22.287-32.568-48.643-32.568-79.064V660.874c0-10.285 3.634-19.053 10.908-26.328 7.276-7.273 16.049-10.912 26.321-10.912 10.266 0 19.04 3.639 26.316 10.912 7.273 7.275 10.908 16.051 10.908 26.328v148.818c0 10.27 3.634 19.049 10.914 26.324 7.274 7.273 16.042 10.896 26.318 10.896h596.256c10.076 0 18.656-3.623 25.729-10.896 7.072-7.275 10.611-16.055 10.611-26.324V660.874c0-10.285 3.637-19.053 10.908-26.328 7.277-7.273 16.045-10.912 26.313-10.912h-0.073z"  ></path></symbol><symbol id="icon-daoru" viewBox="0 0 1032 1024"><path d="M563.2 42.666667v640c0 23.466667-19.2 42.666667-42.666667 42.666666s-42.666667-19.2-42.666666-42.666666V42.666667c0-23.466667 19.2-42.666667 42.666666-42.666667s42.666667 19.2 42.666667 42.666667"  ></path><path d="M806.1952 457.275733L550.5792 712.900267a42.7776 42.7776 0 0 1-60.330667 0c-16.597333-16.605867-16.597333-43.776 0-60.330667l255.616-255.624533a42.7776 42.7776 0 0 1 60.330667 0 42.7776 42.7776 0 0 1 0 60.330666"  ></path><path d="M490.350933 712.8832L234.734933 457.275733a42.7776 42.7776 0 0 1 0-60.330666c16.597333-16.605867 43.776-16.605867 60.330667 0l255.616 255.616a42.7776 42.7776 0 0 1 0 60.330666 42.7776 42.7776 0 0 1-60.330667 0"  ></path><path d="M904.533333 1024H136.533333c-70.570667 0-128-57.429333-128-128V640c0-23.594667 19.072-42.666667 42.666667-42.666667s42.666667 19.072 42.666667 42.666667v256c0 23.552 19.114667 42.666667 42.666666 42.666667h768c23.552 0 42.666667-19.114667 42.666667-42.666667V640c0-23.594667 19.072-42.666667 42.666667-42.666667s42.666667 19.072 42.666666 42.666667v256c0 70.570667-57.429333 128-128 128"  ></path></symbol><symbol id="icon-biaoshilei_ceshi" viewBox="0 0 1024 1024"><path d="M64 960h896l-320-593.024V128h64V64H320v64h64v238.976L64 960z m107.2-64l172.224-320h336.256l172.864 320H171.2zM448 383.104V128h128v255.104L645.184 512H377.92L448 383.104z" fill="" ></path></symbol><symbol id="icon-ceshi" viewBox="0 0 1024 1024"><path d="M967.111111 113.777778v682.666666H56.888889V113.777778h910.222222zM113.777778 170.666667v568.888889h796.444444V170.666667H113.777778z" fill="#333333" ></path><path d="M170.666667 853.333333h682.666666v56.888889H170.666667z" fill="#333333" ></path><path d="M665.6 648.533333l-39.822222-45.511111L773.688889 455.111111 625.777778 307.2l39.822222-45.511111L853.333333 455.111111zM358.4 648.533333l39.822222-45.511111L250.311111 455.111111 398.222222 307.2l-39.822222-45.511111L170.666667 455.111111zM540.899556 221.866667l55.182222 13.824L483.157333 688.355556l-55.182222-13.767112z" fill="#333333" ></path></symbol><symbol id="icon-guolv" viewBox="0 0 1024 1024"><path d="M875.9 148L629.1 425.4 616 440.2v435.7l-2.2-1.8-2.4-1.5L408 743.4V440.2l-13.1-14.8L148.1 148h727.8m0-52H148.1c-44.4 0-68.4 52.1-39.5 85.8L356 460v283.4c0 17.8 9.1 34.4 24.1 43.9l203.4 129.2c9.9 7.9 21.2 11.5 32.2 11.5 26.9 0 52.2-21.2 52.2-52.1V460l247.4-278.2c29-33.7 5-85.8-39.4-85.8z"  ></path></symbol><symbol id="icon-guolv1" viewBox="0 0 1024 1024"><path d="M741.248 79.68l-234.112 350.08v551.488l55.296 24.704v-555.776l249.152-372.544c8.064-32.96-10.496-59.712-41.152-59.712h-709.248c-30.464 0-49.28 26.752-41.344 59.712l265.728 372.544v432.256l55.36 24.704v-478.592l-248.896-348.864h649.216z m-68.032 339.648c0-16.832 12.096-30.592 27.264-30.848h277.888c15.232 0 27.712 13.824 27.712 30.848s-12.416 30.848-27.712 30.848h-277.888c-15.168-0.32-27.264-14.016-27.264-30.848z m0 185.216c0-16.832 12.096-30.592 27.264-30.848h277.888c15.232 0 27.712 13.824 27.712 30.848s-12.416 30.848-27.712 30.848h-277.888c-15.168-0.256-27.264-14.016-27.264-30.848z m0 185.28c0-16.832 12.096-30.592 27.264-30.848h277.888c15.232 0 27.712 13.824 27.712 30.848s-12.416 30.848-27.712 30.848h-277.888c-15.168-0.32-27.264-13.952-27.264-30.848z"  ></path></symbol><symbol id="icon-test-case-secondary" viewBox="0 0 1024 1024"><path d="M256 85.333333h512c25.6 0 42.666667-17.066667 42.666667-42.666666S793.6 0 768 0h-512c-25.6 0-42.666667 17.066667-42.666667 42.666667s17.066667 42.666667 42.666667 42.666666zM896 341.333333H128c-51.2 0-85.333333 34.133333-85.333333 85.333334v512c0 51.2 34.133333 85.333333 85.333333 85.333333h768c51.2 0 85.333333-34.133333 85.333333-85.333333V426.666667c0-51.2-34.133333-85.333333-85.333333-85.333334z m0 597.333334H128V426.666667h768v512zM170.666667 256h682.666666c25.6 0 42.666667-17.066667 42.666667-42.666667S878.933333 170.666667 853.333333 170.666667h-682.666666c-25.6 0-42.666667 17.066667-42.666667 42.666666s17.066667 42.666667 42.666667 42.666667z"  ></path></symbol><symbol id="icon-_ruanjianceshi" viewBox="0 0 1024 1024"><path d="M755.768889 494.364444H268.231111a26.737778 26.737778 0 0 0 0 53.570371h487.537778a26.737778 26.737778 0 1 0 0-53.570371zM755.768889 313.931852H268.231111a26.737778 26.737778 0 0 0 0 53.57037h487.537778a26.737778 26.737778 0 1 0 0-53.57037z" fill="#0097FF" ></path><path d="M380.965926 268.8a26.737778 26.737778 0 0 0-26.737778 26.737778v81.825185a26.737778 26.737778 0 0 0 53.570371 0v-81.73037a26.737778 26.737778 0 0 0-26.832593-26.832593zM651.472593 449.232593a26.737778 26.737778 0 0 0-26.737778 26.737777v81.730371a26.737778 26.737778 0 0 0 53.57037 0v-81.635556a26.737778 26.737778 0 0 0-26.832592-26.832592z" fill="#0097FF" ></path><path d="M833.232593 78.601481H190.767407A133.878519 133.878519 0 0 0 56.888889 212.48v428.373333a133.878519 133.878519 0 0 0 133.878518 133.878519h642.465186A133.878519 133.878519 0 0 0 967.111111 640.853333V212.48A133.878519 133.878519 0 0 0 833.232593 78.601481z m80.308148 562.157038a80.308148 80.308148 0 0 1-80.308148 80.308148H190.767407a80.308148 80.308148 0 0 1-80.308148-80.308148V212.48a80.308148 80.308148 0 0 1 80.308148-80.308148h642.465186a80.308148 80.308148 0 0 1 80.308148 80.308148zM144.402963 945.398519a28.444444 28.444444 0 1 1 0-56.888889h735.194074a28.444444 28.444444 0 1 1 0 56.888889z" fill="#0097FF" ></path></symbol><symbol id="icon-_ruanjianceshi1" viewBox="0 0 1024 1024"><path d="M755.768889 494.364444H268.231111a26.737778 26.737778 0 0 0 0 53.570371h487.537778a26.737778 26.737778 0 1 0 0-53.570371zM755.768889 313.931852H268.231111a26.737778 26.737778 0 0 0 0 53.57037h487.537778a26.737778 26.737778 0 1 0 0-53.57037z" fill="#0097FF" ></path><path d="M380.965926 268.8a26.737778 26.737778 0 0 0-26.737778 26.737778v81.825185a26.737778 26.737778 0 0 0 53.570371 0v-81.73037a26.737778 26.737778 0 0 0-26.832593-26.832593zM651.472593 449.232593a26.737778 26.737778 0 0 0-26.737778 26.737777v81.730371a26.737778 26.737778 0 0 0 53.57037 0v-81.635556a26.737778 26.737778 0 0 0-26.832592-26.832592z" fill="#0097FF" ></path><path d="M833.232593 78.601481H190.767407A133.878519 133.878519 0 0 0 56.888889 212.48v428.373333a133.878519 133.878519 0 0 0 133.878518 133.878519h642.465186A133.878519 133.878519 0 0 0 967.111111 640.853333V212.48A133.878519 133.878519 0 0 0 833.232593 78.601481z m80.308148 562.157038a80.308148 80.308148 0 0 1-80.308148 80.308148H190.767407a80.308148 80.308148 0 0 1-80.308148-80.308148V212.48a80.308148 80.308148 0 0 1 80.308148-80.308148h642.465186a80.308148 80.308148 0 0 1 80.308148 80.308148zM144.402963 945.398519a28.444444 28.444444 0 1 1 0-56.888889h735.194074a28.444444 28.444444 0 1 1 0 56.888889z" fill="#0097FF" ></path></symbol><symbol id="icon-guolv2" viewBox="0 0 1024 1024"><path d="M640 928a34.56 34.56 0 0 1-14.08-3.2l-256-128A32.64 32.64 0 0 1 352 768V397.44L105.6 150.4a30.08 30.08 0 0 1-7.04-34.56A32 32 0 0 1 128 96h768a32 32 0 0 1 29.44 19.84 30.08 30.08 0 0 1-7.04 34.56L672 397.44V896a33.28 33.28 0 0 1-15.36 27.52 37.76 37.76 0 0 1-16.64 4.48z m-224-179.84l192 96V384a30.08 30.08 0 0 1 9.6-22.4l200.96-201.6H205.44l200.96 201.6A30.08 30.08 0 0 1 416 384z" fill="#4D4D4D" ></path></symbol><symbol id="icon-ceshi1" viewBox="0 0 1024 1024"><path d="M704 128a32 32 0 0 1 0 64h-21.333333v160.789333a10.666667 10.666667 0 0 0 1.088 4.693334l180.874666 369.6c28.48 58.218667 4.384 128.490667-53.813333 156.970666A117.333333 117.333333 0 0 1 759.242667 896H264.746667c-64.8 0-117.333333-52.533333-117.333334-117.333333a117.333333 117.333333 0 0 1 11.946667-51.573334l180.874667-369.621333a10.666667 10.666667 0 0 0 1.088-4.693333V192h-21.333334a32 32 0 0 1-31.946666-30.122667L288 160a32 32 0 0 1 32-32z m51.968 522.666667H268.010667l-51.157334 104.554666A53.333333 53.333333 0 0 0 264.757333 832H759.253333a53.333333 53.333333 0 0 0 47.893334-76.778667L755.978667 650.666667zM618.666667 192H405.333333v160.789333a74.666667 74.666667 0 0 1-7.594666 32.821334L299.328 586.666667h425.322667l-98.389334-201.066667A74.666667 74.666667 0 0 1 618.666667 352.8V192z"  ></path></symbol><symbol id="icon-ceshi2" viewBox="0 0 1024 1024"><path d="M948.48 831.488l-134.464-279.744-0.064-0.064-19.392-40.32-83.136-172.864V128H729.6c17.664 0 32-14.336 32-32s-14.336-32-32-32H281.6c-17.664 0-32 14.336-32 32s14.336 32 32 32h24.576v210.496l-95.36 198.272-6.4 13.248c-0.064 0.128-0.128 0.32-0.192 0.448L69.12 831.488c-17.664 36.736-15.232 79.296 6.464 113.856 21.696 34.496 59.008 55.104 99.776 55.104h666.88c40.768 0 78.08-20.608 99.776-55.104 21.696-34.56 24.128-77.12 6.464-113.856z m-360.128-182.208c-49.216 22.4-169.152-28.48-227.84-83.968a207.84 207.84 0 0 1-10.368-10.752c-1.152-1.28-2.368-2.56-3.648-3.776-1.024-1.088-2.112-2.048-3.264-3.072-1.088-0.96-2.24-1.92-3.392-2.816-0.256-0.192-0.448-0.384-0.704-0.576-1.152-0.896-2.24-1.728-3.392-2.496-1.344-0.96-2.688-1.792-4.032-2.624-1.216-0.768-2.432-1.472-3.648-2.112-5.824-3.264-12.16-5.76-18.88-7.616-1.536-0.448-3.136-0.896-4.8-1.216-1.6-0.384-3.264-0.704-4.992-1.024a0.192 0.192 0 0 0-0.256 0c-1.472-0.32-3.072-0.576-4.608-0.768-0.32-0.064-0.64-0.064-0.96-0.128-1.408-0.192-2.88-0.32-4.416-0.448-0.192-0.064-0.448-0.064-0.704-0.064-0.448-0.064-0.832-0.128-1.28-0.128L367.04 359.68c2.048-4.352 3.136-9.088 3.136-13.888V132.352h277.248v213.44c0 4.8 1.088 9.536 3.136 13.888l95.232 197.952c-44.672 35.968-117.376 73.408-157.44 91.648z" fill="#666666" ></path></symbol><symbol id="icon-ceshishenqing" viewBox="0 0 1024 1024"><path d="M607.232 896a32 32 0 1 1 0 64H192a96 96 0 0 1-96-96V160a96 96 0 0 1 96-96h544a96 96 0 0 1 96 96 32 32 0 0 1-64 0 32 32 0 0 0-32-32H192a32 32 0 0 0-32 32v704a32 32 0 0 0 32 32h415.232zM832 585.76v146.656c0 42.656-64 42.656-64 0V585.76c0-42.688 64-42.688 64 0z"  ></path><path d="M288 320a32 32 0 1 1 0-64h224a32 32 0 0 1 0 64H288z m0 160a32 32 0 0 1 0-64h128a32 32 0 0 1 0 64H288z m351.424 446.592a32 32 0 1 1-64 0L576 800a96 96 0 0 1 96-96l127.744 1.152a32 32 0 1 1 0 64L672 768a32 32 0 0 0-32 32l-0.576 126.592z"  ></path><path d="M637.92 947.648a32 32 0 1 1-44.32-46.208l180.384-172.992a32 32 0 1 1 44.288 46.176L637.92 947.648z m242.976-613.12a31.424 31.424 0 0 0 3.328-44.416l-0.416-0.512a32 32 0 0 0-44.896-3.36L408.32 652.288l-15.2 51.84 58.336-4.544 429.44-365.056z m51.616-86.4a95.36 95.36 0 0 1-9.408 134.496l-438.08 372.448a32 32 0 0 1-18.24 7.52l-114.944 8.896a32 32 0 0 1-33.184-40.928L349.6 625.088a32 32 0 0 1 9.984-15.36l437.824-372.224a96 96 0 0 1 134.688 10.112l0.416 0.512z"  ></path></symbol><symbol id="icon-ceshishenqing1" viewBox="0 0 1024 1024"><path d="M607.232 896a32 32 0 1 1 0 64H192a96 96 0 0 1-96-96V160a96 96 0 0 1 96-96h544a96 96 0 0 1 96 96 32 32 0 0 1-64 0 32 32 0 0 0-32-32H192a32 32 0 0 0-32 32v704a32 32 0 0 0 32 32h415.232zM832 585.76v146.656c0 42.656-64 42.656-64 0V585.76c0-42.688 64-42.688 64 0z"  ></path><path d="M288 320a32 32 0 1 1 0-64h224a32 32 0 0 1 0 64H288z m0 160a32 32 0 0 1 0-64h128a32 32 0 0 1 0 64H288z m351.424 446.592a32 32 0 1 1-64 0L576 800a96 96 0 0 1 96-96l127.744 1.152a32 32 0 1 1 0 64L672 768a32 32 0 0 0-32 32l-0.576 126.592z"  ></path><path d="M637.92 947.648a32 32 0 1 1-44.32-46.208l180.384-172.992a32 32 0 1 1 44.288 46.176L637.92 947.648z m242.976-613.12a31.424 31.424 0 0 0 3.328-44.416l-0.416-0.512a32 32 0 0 0-44.896-3.36L408.32 652.288l-15.2 51.84 58.336-4.544 429.44-365.056z m51.616-86.4a95.36 95.36 0 0 1-9.408 134.496l-438.08 372.448a32 32 0 0 1-18.24 7.52l-114.944 8.896a32 32 0 0 1-33.184-40.928L349.6 625.088a32 32 0 0 1 9.984-15.36l437.824-372.224a96 96 0 0 1 134.688 10.112l0.416 0.512z"  ></path></symbol></svg>',
    o = (o = document.getElementsByTagName('script'))[o.length - 1].getAttribute('data-injectcss'),
    s = function (a, l) {
      l.parentNode.insertBefore(a, l)
    }
  if (o && !a.__iconfont__svg__cssinject__) {
    a.__iconfont__svg__cssinject__ = !0
    try {
      document.write('<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>')
    } catch (a) {
      console && console.log(a)
    }
  }
  function n() {
    e || ((e = !0), c())
  }
  function d() {
    try {
      h.documentElement.doScroll('left')
    } catch (a) {
      return void setTimeout(d, 50)
    }
    n()
  }
  ;(l = function () {
    var a,
      l = document.createElement('div')
    ;(l.innerHTML = i), (i = null), (l = l.getElementsByTagName('svg')[0]) && (l.setAttribute('aria-hidden', 'true'), (l.style.position = 'absolute'), (l.style.width = 0), (l.style.height = 0), (l.style.overflow = 'hidden'), (l = l), (a = document.body).firstChild ? s(l, a.firstChild) : a.appendChild(l))
  }),
    document.addEventListener
      ? ~['complete', 'loaded', 'interactive'].indexOf(document.readyState)
        ? setTimeout(l, 0)
        : ((t = function () {
            document.removeEventListener('DOMContentLoaded', t, !1), l()
          }),
          document.addEventListener('DOMContentLoaded', t, !1))
      : document.attachEvent &&
        ((c = l),
        (h = a.document),
        (e = !1),
        d(),
        (h.onreadystatechange = function () {
          'complete' == h.readyState && ((h.onreadystatechange = null), n())
        }))
})(window)
