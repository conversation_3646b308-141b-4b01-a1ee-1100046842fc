@font-face {
  font-family: "iconfont"; /* Project id 3482870 */
  src: url('iconfont.woff2?t=1657015616608') format('woff2'),
       url('iconfont.woff?t=1657015616608') format('woff'),
       url('iconfont.ttf?t=1657015616608') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-erweima:before {
  content: "\e61a";
}

.icon-huoquxuke:before {
  content: "\e67f";
}

.icon-daochu:before {
  content: "\e61b";
}

.icon-daoru:before {
  content: "\e639";
}

.icon-biaoshilei_ceshi:before {
  content: "\e607";
}

.icon-ceshi:before {
  content: "\e6ac";
}

.icon-guolv:before {
  content: "\e64c";
}

.icon-guolv1:before {
  content: "\e6e9";
}

.icon-test-case-secondary:before {
  content: "\e606";
}

.icon-_ruanjianceshi:before {
  content: "\e608";
}

.icon-_ruanjianceshi1:before {
  content: "\e614";
}

.icon-guolv2:before {
  content: "\e617";
}

.icon-ceshi1:before {
  content: "\e8ad";
}

.icon-ceshi2:before {
  content: "\e624";
}

.icon-ceshishenqing:before {
  content: "\e601";
}

.icon-ceshishenqing1:before {
  content: "\e605";
}

