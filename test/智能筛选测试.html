<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能筛选测试页面</title>
    <style>
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        .btn { padding: 10px 20px; margin: 5px; }
        .hidden { display: none; }
        .decoration { color: #ccc; font-size: 12px; }
    </style>
    <script>
        function validateForm() {
            console.log('表单验证');
        }
    </script>
</head>
<body>
    <div class="container">
        <header id="main-header">
            <h1>用户注册表单</h1>
            <nav role="navigation">
                <a href="#home" id="home-link">首页</a>
                <a href="#about">关于我们</a>
                <a href="#contact" data-testid="contact-link">联系我们</a>
            </nav>
        </header>

        <main id="main-content">
            <form id="registration-form" name="registration" action="/register" method="post">
                <div class="form-group">
                    <label for="username">用户名：</label>
                    <input type="text" id="username" name="username" data-testid="username-input" 
                           placeholder="请输入用户名" required>
                </div>

                <div class="form-group">
                    <label for="email">邮箱：</label>
                    <input type="email" id="email" name="email" placeholder="请输入邮箱地址" required>
                </div>

                <div class="form-group">
                    <label for="password">密码：</label>
                    <input type="password" id="password" name="password" data-testid="password-input" 
                           placeholder="请输入密码" required>
                </div>

                <div class="form-group">
                    <label for="country">国家：</label>
                    <select id="country" name="country" data-testid="country-select">
                        <option value="">请选择国家</option>
                        <option value="cn">中国</option>
                        <option value="us">美国</option>
                        <option value="jp">日本</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="bio">个人简介：</label>
                    <textarea id="bio" name="bio" placeholder="请输入个人简介" rows="4"></textarea>
                </div>

                <div class="form-group">
                    <input type="checkbox" id="agree" name="agree" data-testid="agree-checkbox">
                    <label for="agree">我同意用户协议</label>
                </div>

                <div class="form-actions">
                    <button type="submit" id="submit-btn" data-testid="submit-button" class="btn btn-primary">
                        提交注册
                    </button>
                    <button type="reset" id="reset-btn" class="btn btn-secondary">
                        重置表单
                    </button>
                    <button type="button" id="cancel-btn" onclick="history.back()">
                        取消
                    </button>
                </div>
            </form>
        </main>

        <aside class="sidebar">
            <div class="widget">
                <h3>相关链接</h3>
                <ul>
                    <li><a href="/help">帮助中心</a></li>
                    <li><a href="/privacy" data-testid="privacy-link">隐私政策</a></li>
                    <li><a href="/terms">服务条款</a></li>
                </ul>
            </div>
        </aside>

        <footer id="main-footer">
            <p class="copyright">© 2024 测试网站. 保留所有权利.</p>
            <div class="social-links">
                <a href="#" class="social-link" data-platform="weibo">微博</a>
                <a href="#" class="social-link" data-platform="wechat">微信</a>
            </div>
        </footer>

        <!-- 装饰性和隐藏元素 -->
        <div class="hidden">隐藏内容</div>
        <span class="decoration">装饰文本</span>
        <div></div>
        <div class="empty-container"></div>
        
        <!-- 更多测试元素 -->
        <div class="test-section">
            <img src="test.jpg" alt="测试图片" id="test-image">
            <video controls id="test-video">
                <source src="test.mp4" type="video/mp4">
            </video>
            <canvas id="test-canvas" width="200" height="100"></canvas>
        </div>
    </div>

    <!-- 更多脚本和样式 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
        });
    </script>
    
    <style>
        .additional-styles {
            color: red;
        }
    </style>
</body>
</html>
