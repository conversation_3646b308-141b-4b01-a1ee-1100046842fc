const { exec } = require('child_process')
const fs = require('fs')
const path = require('path')
const Mock = require('mockjs')

// 保存进程ID和信息
const processes = {}

// 确保测试目录存在
const ensureTestDir = () => {
  const testDir = path.join(process.cwd(), 'tests/boss')
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true })
  }
  return testDir
}

// 生成随机ID
const generateId = () => Mock.Random.guid()

module.exports = [
  // 启动Playwright录制
  {
    url: '/playwright/record/start',
    type: 'post',
    response: (req, res) => {
      try {
        // 启动Playwright录制
        const childProcess = exec('npx playwright codegen', (error, stdout, stderr) => {
          if (error) {
            console.error(`执行错误: ${error}`)
            return
          }
          if (stdout) console.log(`录制输出: ${stdout}`)
          if (stderr) console.log(`录制错误: ${stderr}`)
        })

        const processId = generateId()
        processes[processId] = {
          process: childProcess,
          startTime: new Date(),
          status: 'recording'
        }

        return {
          code: 0,
          data: {
            processId: processId,
            message: '录制已启动'
          }
        }
      } catch (error) {
        console.error('启动录制错误:', error)
        return {
          code: 500,
          msg: `启动录制失败: ${error.message}`
        }
      }
    }
  },

  // 停止Playwright录制
  {
    url: '/playwright/record/stop',
    type: 'post',
    response: (req, res) => {
      const { processId } = req.body
      
      if (!processId || !processes[processId]) {
        return {
          code: 400,
          msg: '无效的进程ID'
        }
      }

      try {
        const processInfo = processes[processId]
        if (processInfo.process) {
          // 根据操作系统终止进程
          if (process.platform === 'win32') {
            exec(`taskkill /pid ${processInfo.process.pid} /T /F`)
          } else {
            exec(`kill -9 ${processInfo.process.pid}`)
          }
        }

        // 模拟生成的测试脚本
        const generatedScript = `
// 生成的Playwright测试脚本
import { test, expect } from '@playwright/test';

test('测试场景', async ({ page }) => {
  // 访问页面
  await page.goto('https://example.com');
  
  // 等待元素加载
  await page.waitForSelector('.some-element');
  
  // 填写表单
  await page.fill('#username', 'testuser');
  await page.fill('#password', 'password123');
  
  // 点击登录按钮
  await page.click('#login-button');
  
  // 断言登录成功
  await expect(page.locator('.welcome-message')).toBeVisible();
});
`

        // 清理进程信息
        delete processes[processId]

        return {
          code: 0,
          data: {
            script: generatedScript
          }
        }
      } catch (error) {
        console.error('停止录制错误:', error)
        return {
          code: 500,
          msg: `停止录制失败: ${error.message}`
        }
      }
    }
  },

  // 运行Playwright测试
  {
    url: '/playwright/test/run',
    type: 'post',
    response: (req, res) => {
      const { scriptPath } = req.body
      
      if (!scriptPath) {
        return {
          code: 400,
          msg: '缺少脚本路径'
        }
      }

      try {
        // 这里实际上并不执行测试，而是模拟测试结果
        // 在实际的后端中，你会真正地执行命令
        // exec(`npx playwright test ${scriptPath}`)

        // 模拟测试执行结果
        return {
          code: 0,
          data: {
            status: '通过',
            passed: 5,
            failed: 0,
            duration: 1250,
            details: '所有测试通过'
          }
        }
      } catch (error) {
        console.error('运行测试错误:', error)
        return {
          code: 500,
          msg: `运行测试失败: ${error.message}`
        }
      }
    }
  },

  // 保存Playwright测试脚本
  {
    url: '/playwright/script/save',
    type: 'post',
    response: (req, res) => {
      const { script, filename } = req.body
      
      if (!script || !filename) {
        return {
          code: 400,
          msg: '缺少脚本内容或文件名'
        }
      }

      try {
        const testDir = ensureTestDir()
        const scriptPath = path.join(testDir, `${filename}.spec.js`)
        
        // 在实际的后端中，你会真正地写入文件
        // fs.writeFileSync(scriptPath, script)

        return {
          code: 0,
          data: {
            scriptPath: `tests/boss/${filename}.spec.js`
          }
        }
      } catch (error) {
        console.error('保存脚本错误:', error)
        return {
          code: 500,
          msg: `保存脚本失败: ${error.message}`
        }
      }
    }
  },

  // 下载Playwright客户端服务
  {
    url: '/playwright/client/download',
    type: 'get',
    response: (req, res) => {
      try {
        const JSZip = require('jszip')
        const fs = require('fs')
        const path = require('path')

        // 创建zip实例
        const zip = new JSZip()
        const clientFolder = zip.folder('playwright-client')

        // 需要打包的文件路径（相对于项目根目录）
        const files = [
          'src/views/zpAgent/tools/automatedTesting/playwright.config.js',
          'src/views/zpAgent/tools/automatedTesting/playwrightRunner.js',
          'src/views/zpAgent/tools/automatedTesting/start-playwright-server.js',
          'src/views/zpAgent/tools/automatedTesting/stop-playwright-server.js',
          'src/views/zpAgent/tools/automatedTesting/README.md'
        ]

        // 添加文件到zip
        files.forEach(filePath => {
          try {
            const fullPath = path.join(process.cwd(), filePath)
            if (fs.existsSync(fullPath)) {
              const fileName = path.basename(filePath)
              const fileContent = fs.readFileSync(fullPath)
              clientFolder.file(fileName, fileContent)
              console.log(`已添加文件: ${fileName}`)
            } else {
              console.warn(`文件不存在: ${fullPath}`)
            }
          } catch (err) {
            console.error(`处理文件 ${filePath} 时出错:`, err)
          }
        })

        // 添加package.json文件
        clientFolder.file('package.json', JSON.stringify({
          "name": "playwright-client",
          "version": "1.0.0",
          "description": "Playwright客户端服务",
          "scripts": {
            "start": "node start-playwright-server.js",
            "stop": "node stop-playwright-server.js",
            "install-browsers": "npx playwright install"
          },
          "dependencies": {
            "@playwright/test": "^1.30.0",
            "express": "^4.18.2",
            "body-parser": "^1.20.1",
            "cors": "^2.8.5"
          },
          "author": "",
          "license": "ISC"
        }, null, 2))

        // 生成zip
        zip.generateAsync({ type: 'nodebuffer' })
          .then(content => {
            // 设置响应头
            res.set({
              'Content-Type': 'application/zip',
              'Content-Disposition': 'attachment; filename=playwright-client.zip',
              'Content-Length': content.length
            })
            
            // 发送zip文件
            res.send(content)
          })
          .catch(err => {
            console.error('生成zip文件失败:', err)
            res.status(500).json({
              code: 1,
              msg: `生成zip文件失败: ${err.message}`
            })
          })
      } catch (error) {
        console.error('下载客户端服务错误:', error)
        res.status(500).json({
          code: 1,
          msg: `下载客户端服务失败: ${error.message}`
        })
      }
    }
  }
] 