<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@4.6.0/fonts/remixicon.css">
    <title>BZL 测试平台</title>
    <!-- Monaco Editor CDN -->
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs/loader.js"></script>
    <script>
      // 配置 Monaco Editor 加载器
      if (typeof require !== 'undefined') {
        require.config({ 
          paths: { 
            'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs' 
          } 
        });
      }
    </script>
    <script>
    window.onerror = function (message, source, lineno, colno, error) {
        if (message.includes('Uncaught SyntaxError')) {
            console.error('Failed to load chunk:', error);
            window.location.reload();
        } else {
            console.error('Error:', error, '\nMessage:', message);
        }
    }
  </script>
</head>

<body>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <!--设备平台js-->
    <!-- <script type="text/javascript" src="/YUVCanvas.js"></script>
    <script type="text/javascript" src="/Decoder.js"></script>
    <script type="text/javascript" src="/Player.js"></script> -->
</body>



</html>
 
