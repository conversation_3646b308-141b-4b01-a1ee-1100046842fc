# AI生成组件使用说明

## 组件概述

`aigenerate.vue` 是一个智能的 Playwright 自动化脚本生成组件，它整合了：
- 智能筛选的元素选择
- DOM树形结构上下文
- 用户自然语言描述
- 多框架多语言支持

## 功能特点

### 1. 智能元素管理
- 显示从智能筛选中选中的元素
- 支持元素的添加、移除和查看详情
- 自动生成元素的选择器和上下文信息

### 2. 多框架支持
- **Playwright**: JavaScript, TypeScript, Python, Java
- **Selenium**: 多语言支持
- **Cypress**: JavaScript, TypeScript

### 3. 智能脚本生成
- 根据元素类型自动推断操作（点击、输入、选择等）
- 生成完整的测试脚本结构
- 支持脚本编辑和下载

### 4. 用户体验优化
- 实时预览生成结果
- 支持脚本复制和下载
- 响应式设计，适配不同屏幕

## 使用方法

### 1. 基本用法

```vue
<template>
  <div class="test-generation">
    <AIGenerate 
      :selected-elements="selectedElements"
      :dom-context="domContext"
      @remove-element="handleRemoveElement"
    />
  </div>
</template>

<script>
import AIGenerate from './components/generation/aigenerate.vue'

export default {
  components: {
    AIGenerate
  },
  data() {
    return {
      selectedElements: [
        {
          tagName: 'input',
          selector: '#username',
          id: 'username',
          className: 'form-control',
          textContent: '',
          attributes: {
            type: 'text',
            placeholder: '请输入用户名'
          }
        },
        {
          tagName: 'button',
          selector: '#login-btn',
          id: 'login-btn',
          className: 'btn btn-primary',
          textContent: '登录',
          attributes: {
            type: 'submit'
          }
        }
      ],
      domContext: `页面结构:
- 登录表单
  - 用户名输入框 (#username)
  - 密码输入框 (#password)
  - 登录按钮 (#login-btn)`
    }
  },
  methods: {
    handleRemoveElement(index) {
      this.selectedElements.splice(index, 1)
    }
  }
}
</script>
```

### 2. 与智能筛选组件集成

```vue
<template>
  <div class="automation-testing">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="元素选择" name="selection">
        <ElementSelect 
          @elements-selected="handleElementsSelected"
        />
      </el-tab-pane>
      
      <el-tab-pane label="脚本生成" name="generation">
        <AIGenerate 
          :selected-elements="selectedElements"
          :dom-context="domContext"
          @remove-element="handleRemoveElement"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ElementSelect from './components/dom/components/elementSelect.vue'
import AIGenerate from './components/generation/aigenerate.vue'

export default {
  components: {
    ElementSelect,
    AIGenerate
  },
  data() {
    return {
      activeTab: 'selection',
      selectedElements: [],
      domContext: ''
    }
  },
  methods: {
    handleElementsSelected(elements) {
      this.selectedElements = elements
      this.generateDomContext(elements)
      this.activeTab = 'generation'
    },
    
    handleRemoveElement(index) {
      this.selectedElements.splice(index, 1)
      this.generateDomContext(this.selectedElements)
    },
    
    generateDomContext(elements) {
      const contextLines = []
      elements.forEach((el, index) => {
        contextLines.push(`${index + 1}. ${el.tagName}`)
        if (el.id) contextLines.push(`   ID: ${el.id}`)
        if (el.className) contextLines.push(`   Class: ${el.className}`)
        if (el.textContent) contextLines.push(`   Text: ${el.textContent}`)
      })
      this.domContext = contextLines.join('\n')
    }
  }
}
</script>
```

## 组件属性

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| selectedElements | Array | [] | 选中的元素数组 |
| domContext | String | '' | DOM结构上下文信息 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| remove-element | index | 移除指定索引的元素 |

### 元素对象结构

```javascript
{
  tagName: 'input',           // 标签名
  selector: '#username',      // CSS选择器
  id: 'username',            // ID属性
  className: 'form-control',  // 类名
  textContent: '',           // 文本内容
  attributes: {              // 属性对象
    type: 'text',
    placeholder: '请输入用户名'
  }
}
```

## 生成的脚本示例

### Playwright JavaScript
```javascript
// 用户登录测试
const { test, expect } = require('@playwright/test');

test('用户登录测试', async ({ page }) => {
  // 导航到页面
  await page.goto('YOUR_URL_HERE');
  
  // input元素操作
  await page.fill('#username', "输入内容");

  // button元素操作
  await page.click('#login-btn');
  
  // 验证结果
  await expect(page).toHaveTitle(/.*页面标题.*/);
});
```

### Playwright Python
```python
# 用户登录测试
import pytest
from playwright.sync_api import Page, expect

def test_用户登录测试(page: Page):
    # 导航到页面
    page.goto("YOUR_URL_HERE")
    
    # input元素操作
    page.fill("#username", "输入内容")

    # button元素操作
    page.click("#login-btn")
    
    # 验证结果
    expect(page).to_have_title(re.compile(".*页面标题.*"))
```

## 自定义配置

### 支持的框架
- `playwright`: Playwright测试框架
- `selenium`: Selenium WebDriver
- `cypress`: Cypress测试框架

### 支持的语言
- `javascript`: JavaScript
- `typescript`: TypeScript  
- `python`: Python
- `java`: Java

### 测试类型
- `e2e`: 端到端测试
- `integration`: 集成测试
- `unit`: 单元测试

## 最佳实践

### 1. 元素选择建议
- 优先选择有唯一ID的元素
- 选择有意义的CSS类名的元素
- 避免选择过于通用的元素（如div、span）

### 2. 测试描述编写
- 使用清晰、具体的描述
- 包含测试的主要步骤和预期结果
- 避免过于复杂的测试场景

### 3. 脚本优化
- 生成后可以手动编辑脚本
- 添加适当的等待和验证
- 考虑异常处理和错误恢复

## 扩展功能

### 1. 自定义模板
可以扩展 `generateScriptTemplate` 方法来支持自定义的脚本模板。

### 2. API集成
将 `callGenerateAPI` 方法替换为实际的AI服务API调用。

### 3. 更多框架支持
可以添加对其他测试框架的支持，如WebdriverIO、TestCafe等。

## 故障排除

### 常见问题

1. **生成的脚本不准确**
   - 检查元素选择器是否正确
   - 确保测试描述足够详细
   - 验证DOM上下文信息

2. **脚本无法运行**
   - 检查目标URL是否正确
   - 验证元素选择器在目标页面中存在
   - 确保测试环境配置正确

3. **样式显示异常**
   - 确保正确导入了样式文件
   - 检查CSS变量是否定义
   - 验证响应式断点设置
