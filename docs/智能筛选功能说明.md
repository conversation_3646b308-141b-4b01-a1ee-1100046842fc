# 智能筛选功能说明

## 功能概述

智能筛选功能是 `elementSelect.vue` 组件的核心特性，旨在从大量HTML元素中自动筛选出最重要、最具测试价值的元素，提高自动化测试的效率。

## 功能特点

### 1. 智能评分系统
- **交互元素优先**：按钮、输入框、选择器等交互元素获得最高分数（8分）
- **表单元素重要**：表单相关元素获得高分（7分）
- **容器元素适中**：重要容器元素获得中等分数（6分）
- **装饰元素过滤**：样式、脚本等装饰性元素直接过滤（0分）

### 2. 属性加分机制
- **ID属性**：+2分（唯一标识符，测试价值高）
- **测试ID**：+2分（data-testid属性，专为测试设计）
- **名称属性**：+1分（name属性，表单元素重要标识）
- **无障碍属性**：+1分（role、aria-label等）
- **类名属性**：+0.5分（class属性）

### 3. 内容评估
- **文本内容**：有合适长度文本内容的元素+1分
- **特殊类型**：提交按钮、链接等特殊类型元素额外加分
- **占位符**：有placeholder属性的元素+0.5分

## 筛选策略

### 1. 分数阈值
- 默认阈值：5分
- 只显示分数≥阈值的元素
- 自动过滤低价值元素

### 2. 去重机制
- 相同选择器的元素最多保留3个
- 按分数排序，优先保留高分元素
- 避免重复元素干扰

### 3. 排序规则
- 最终结果按分数从高到低排序
- 确保最重要的元素优先显示

## 使用方法

### 1. 启用智能筛选
1. 在HTML代码输入区域粘贴HTML代码
2. 点击"解析HTML"按钮解析元素
3. 点击"智能筛选"按钮启用智能筛选
4. 按钮变为橙色"智能筛选中"状态，显示筛选指示器

### 2. 查看筛选结果
- 元素列表只显示高价值元素
- 顶部显示筛选统计信息
- 每个元素卡片显示完整信息

### 3. 关闭智能筛选
- 再次点击"智能筛选"按钮
- 恢复显示所有元素

## 评分示例

### 高分元素（8-10分）
```html
<button id="submit-btn" data-testid="submit">提交</button>  <!-- 10.5分 -->
<input type="email" name="email" placeholder="邮箱">      <!-- 9.5分 -->
<select name="country" id="country-select">               <!-- 9分 -->
```

### 中分元素（5-7分）
```html
<div class="form-group" id="email-group">                 <!-- 6.5分 -->
<h1>页面标题</h1>                                         <!-- 5分 -->
<p class="description">描述文本</p>                       <!-- 5.5分 -->
```

### 低分元素（0-4分，被过滤）
```html
<style>.hidden { display: none; }</style>                <!-- 0分 -->
<script>console.log('test');</script>                     <!-- 0分 -->
<div></div>                                               <!-- 2分 -->
```

## 技术实现

### 核心方法
- `calculateElementScore(element)`: 计算元素分数
- `applySmartFilter(elements)`: 应用智能筛选
- `toggleSmartFilter()`: 切换筛选状态

### 数据属性
- `isSmartFilterActive`: 筛选状态
- `smartFilterThreshold`: 分数阈值

## 优势

1. **提高效率**：自动过滤无关元素，专注重要内容
2. **智能排序**：按重要性排序，优先显示关键元素
3. **减少干扰**：去除装饰性元素，清晰展示可测试元素
4. **灵活控制**：可随时开启/关闭，适应不同需求
5. **直观反馈**：清晰的状态指示和统计信息

## 适用场景

- **页面元素分析**：快速识别页面关键交互元素
- **测试用例编写**：为自动化测试选择合适的元素
- **UI审查**：检查页面可访问性和交互性
- **元素定位**：在复杂页面中快速定位目标元素
